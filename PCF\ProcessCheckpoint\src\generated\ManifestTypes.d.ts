/*
*This is auto generated from the ControlManifest.Input.xml file
*/

// Define IInputs and IOutputs Type. They should match with ControlManifest.
export interface IInputs {
    sourceControl: ComponentFramework.PropertyTypes.StringProperty;
    language: ComponentFramework.PropertyTypes.StringProperty;
    enableFileAttachment: ComponentFramework.PropertyTypes.TwoOptionsProperty;
    requireFileAttachment: ComponentFramework.PropertyTypes.TwoOptionsProperty;
    allowedFileTypes: ComponentFramework.PropertyTypes.StringProperty;
    maxFileSize: ComponentFramework.PropertyTypes.WholeNumberProperty;
    customText: ComponentFramework.PropertyTypes.StringProperty;
}
export interface IOutputs {
    sourceControl?: string;
}
