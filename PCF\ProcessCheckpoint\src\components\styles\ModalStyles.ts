import type { CSSProperties } from "react";
import {
  UI_COLORS,
  UI_DIMENSIONS,
  UI_EFFECTS,
  UI_SPACING,
  UI_TYPOGRAPHY,
} from "../../types";

export const modalOverlayStyle: CSSProperties = {
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: UI_COLORS.background.MODAL_OVERLAY,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: UI_DIMENSIONS.MODAL_Z_INDEX,
  padding: UI_SPACING.SPACING_XL,
};

export const modalContentStyle: CSSProperties = {
  backgroundColor: UI_COLORS.WHITE,
  borderRadius: UI_DIMENSIONS.MODAL_BORDER_RADIUS,
  boxShadow: UI_EFFECTS.MODAL_SHADOW,
  width: "100%",
  maxWidth: UI_DIMENSIONS.MODAL_MAX_WIDTH,
  maxHeight: UI_DIMENSIONS.MODAL_MAX_HEIGHT,
  display: "flex",
  flexDirection: "column",
  animation: UI_EFFECTS.MODAL_FADE_IN_ANIMATION,
};

export const modalHeaderStyle: CSSProperties = {
  padding: `${UI_SPACING.SPACING_XL} ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_L}`,
  borderBottom: `1px solid ${UI_COLORS.border.DEFAULT}`,
};

export const modalTitleStyle: CSSProperties = {
  margin: 0,
  fontSize: UI_TYPOGRAPHY.MODAL_TITLE_SIZE,
  fontWeight: UI_TYPOGRAPHY.FONT_WEIGHT_SEMIBOLD,
  color: UI_COLORS.text.DEFAULT,
};

export const modalFooterStyle: CSSProperties = {
  padding: `${UI_SPACING.SPACING_L} ${UI_SPACING.SPACING_XXL}`,
  borderTop: `1px solid ${UI_COLORS.border.DEFAULT}`,
  display: "flex",
  justifyContent: "flex-end",
  gap: UI_SPACING.SPACING_S,
};
