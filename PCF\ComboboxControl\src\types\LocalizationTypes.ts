/**
 * ComboboxControl Localization Types & Interface
 * Consolidated for easy maintenance
 */

import type { NestedObject } from "../../../SharedLibraries/types/CoreTypes";

// Localization Strings Interface
export interface LocalizationStrings extends NestedObject {
  ui: {
    labels: {
      attribute: string;
      stringAttribute: string;
      picklistAttribute: string;
      dateTimeAttribute: string;
      statuscode: string;
      statecode: string;
      error: string;
    };
    messages: {
      loading: string;
      selectBPF: string;
    };
  };
  errors: {
    statecodeNotConfigured: string;
    permissionDenied: string;
    timeout: string;
    attributeNotFound: string; // Template: "Attribute {fieldType} not found in entity {entityName}"
    optionsLoadFailed: string; // Template: "Failed to load options: {error}"
    bpfMissingPrimaryEntity: string; // Template: "BPF {bpfId} is missing primary entity"
    bpfNotFound: string; // Template: "BPF {bpfId} not found"
    entityNotFound: string; // Template: "Entity {entityName} not found"
  };
  constants: {
    workflow: string;
    selectPrimaryEntity: string;
    displayNameFormat: string;
    freeformKey: string;
    alertRole: string;
  };
}
