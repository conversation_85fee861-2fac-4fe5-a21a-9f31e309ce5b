import type { CSSProperties } from "react";
import {
  UI_COLORS,
  UI_CURSORS,
  UI_DIMENSIONS,
  UI_SPACING,
  UI_TYPOGRAPHY,
} from "../../types";

// A general-purpose style for informational banners.
export const infoBannerStyle: CSSProperties = {
  backgroundColor: UI_COLORS.background.READ_ONLY_INFO,
  borderLeft: `${UI_DIMENSIONS.INFO_BAR_BORDER_WIDTH} solid ${UI_COLORS.border.READ_ONLY_INFO}`,
  padding: `${UI_SPACING.SPACING_S} ${UI_SPACING.SPACING_M}`,
  marginBottom: UI_SPACING.SPACING_M,
  fontSize: UI_TYPOGRAPHY.FONT_SIZE_SMALL,
  color: UI_COLORS.text.SECONDARY,
};

// Reusable text styles grouped for easy access.
export const textStyles = {
  smallSecondary: {
    color: UI_COLORS.text.READ_ONLY,
    fontSize: UI_TYPOGRAPHY.FONT_SIZE_SMALL,
  } as CSSProperties,
  semibold: {
    fontWeight: UI_TYPOGRAPHY.FONT_WEIGHT_SEMIBOLD,
  } as CSSProperties,
  link: {
    color: UI_COLORS.text.LINK,
    cursor: UI_CURSORS.POINTER,
    textDecoration: "underline",
  } as CSSProperties,
};

// Reusable layout patterns.
export const layoutStyles = {
  flexRow: {
    display: "flex",
    alignItems: "center",
  } as CSSProperties,
};

// Common utility styles.
export const utilityStyles = {
  visuallyHidden: {
    display: "none",
  } as CSSProperties,
};
