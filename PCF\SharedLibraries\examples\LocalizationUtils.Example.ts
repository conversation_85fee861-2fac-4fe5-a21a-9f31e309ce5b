import { englishStrings } from './localization/en';
import { vietnameseStrings } from './localization/vi';
import { LocalizationStrings } from './localization/LocalizationStrings';
import { 
  createLocalizationMerger, 
  validateAndMergeStrings,
  createAdvancedLocalizationMerger,
  createLanguageLoader,
  getValidatedLanguage,
  detectSystemLanguage,
  getLanguageDisplayName,
  LANGUAGES,
  SupportedLanguage
} from './localization/LocalizationUtils';

export class SampleControl implements ComponentFramework.StandardControl<IInputs, IOutputs> {
  private _strings: LocalizationStrings;
  private _context: ComponentFramework.Context<IInputs>;
  private _languageLoader: (lang: string | number | undefined | null) => LocalizationStrings;
  private _advancedMerger: (customStrings: string | Partial<LocalizationStrings> | null, targetLanguage?: string | number | null) => LocalizationStrings;
  private _currentLanguage: SupportedLanguage;

  constructor() {
    // Create language-aware loader
    this._languageLoader = createLanguageLoader({
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });

    // Create advanced merger with language map
    this._advancedMerger = createAdvancedLocalizationMerger(englishStrings, {
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });

    // Initialize with system language detection
    this._currentLanguage = detectSystemLanguage();
    this._strings = this._languageLoader(this._currentLanguage);
  }

  public init(
    context: ComponentFramework.Context<IInputs>,
    notifyOutputChanged: () => void,
    state: ComponentFramework.Dictionary,
    container: HTMLDivElement
  ): void {
    this._context = context;
    this.loadLocalizedStrings();
    this.renderLanguageSelector(container);
  }

  public updateView(context: ComponentFramework.Context<IInputs>): void {
    this._context = context;
    this.loadLocalizedStrings();
    this.renderUI();
  }

  private loadLocalizedStrings(): void {
    // Method 1: Basic language-aware loading
    const userLcid = this._context.userSettings.languageId;
    this._currentLanguage = getValidatedLanguage(userLcid);
    
    // Method 2: Advanced loading with custom strings
    const customStrings = this._context.parameters.customStrings?.raw || null;
    
    // Use advanced merger that considers both language and custom overrides
    this._strings = this._advancedMerger(customStrings, userLcid);

    // Method 3: With comprehensive validation (for production)
    /*
    const requiredKeys = ['ui', 'errors', 'status'];
    const { result, isValid, errors } = validateAndMergeStrings(
      this._languageLoader(userLcid),
      customStrings,
      requiredKeys
    );
    
    if (!isValid) {
      console.error('Localization validation failed:', errors);
      this.showLocalizationError(errors);
    }
    
    this._strings = result;
    */
  }

  private renderLanguageSelector(container: HTMLDivElement): void {
    const languageSelector = document.createElement('select');
    languageSelector.className = 'language-selector';
    
    // Add language options
    Object.values(LANGUAGES).forEach(lang => {
      const option = document.createElement('option');
      option.value = lang;
      option.textContent = getLanguageDisplayName(lang);
      option.selected = lang === this._currentLanguage;
      languageSelector.appendChild(option);
    });

    // Handle language change
    languageSelector.addEventListener('change', (e) => {
      const selectedLang = (e.target as HTMLSelectElement).value as SupportedLanguage;
      this.changeLanguage(selectedLang);
    });

    container.appendChild(languageSelector);
  }

  private changeLanguage(newLanguage: SupportedLanguage): void {
    this._currentLanguage = newLanguage;
    
    // Reload strings with new language
    const customStrings = this._context.parameters.customStrings?.raw || null;
    this._strings = this._advancedMerger(customStrings, newLanguage);
    
    // Re-render UI
    this.renderUI();
  }

  private renderUI(): void {
    // Use the merged strings in your UI
    const title = this._strings.ui.titles.processCheckpoint;
    const submitButton = this._strings.ui.buttons.submit;
    const errorMessage = this._strings.errors.general.unknownError;
    
    // Show current language in UI
    console.log(`Current language: ${getLanguageDisplayName(this._currentLanguage)}`);
    
    // Render your UI elements with localized strings
    // ... your rendering logic
  }

  private showLocalizationError(errors: string[]): void {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'localization-error';
    errorDiv.innerHTML = `
      <h3>${this._strings.ui.titles.errorTitle}</h3>
      <ul>
        ${errors.map(error => `<li>${error}</li>`).join('')}
      </ul>
    `;
    
    // Show error in UI
    console.error('Localization errors:', errors);
  }

  // Example of context-aware string loading
  private getContextualStrings(context: 'approval' | 'review' | 'general'): Partial<LocalizationStrings> {
    const contextualOverrides: Record<string, Partial<LocalizationStrings>> = {
      approval: {
        ui: {
          titles: {
            processCheckpoint: this._currentLanguage === LANGUAGES.VN 
              ? "Điểm Phê Duyệt" 
              : "Approval Checkpoint"
          },
          buttons: {
            submit: this._currentLanguage === LANGUAGES.VN 
              ? "Phê Duyệt" 
              : "Approve"
          }
        }
      },
      review: {
        ui: {
          titles: {
            processCheckpoint: this._currentLanguage === LANGUAGES.VN 
              ? "Điểm Xem Xét" 
              : "Review Checkpoint"
          }
        }
      },
      general: {}
    };

    return contextualOverrides[context] || {};
  }

  // Example manifest property configurations
  /*
  In your ControlManifest.Input.xml:
  
  <!-- Custom strings property -->
  <property name="customStrings" display-name-key="Custom_Strings" 
            description-key="Custom_Strings_Desc" of-type="Multiple" 
            usage="input" required="false">
    <value>
      {
        "ui": {
          "titles": {
            "processCheckpoint": "Custom Process Title"
          },
          "buttons": {
            "submit": "Custom Submit"
          }
        }
      }
    </value>
  </property>

  <!-- Language override property -->
  <property name="languageOverride" display-name-key="Language_Override" 
            description-key="Language_Override_Desc" of-type="Enum" 
            usage="input" required="false">
    <value name="auto" display-name-key="Auto_Detect">auto</value>
    <value name="en" display-name-key="English">en</value>
    <value name="vi" display-name-key="Vietnamese">vi</value>
  </property>
  */
}

// Example of creating a specialized control for different contexts
export class ApprovalProcessCheckpoint extends ProcessCheckpointControl {
  constructor() {
    super();
    // Apply approval-specific customizations
    this.applyApprovalSpecificStrings();
  }

  private applyApprovalSpecificStrings(): void {
    const approvalStrings: Partial<LocalizationStrings> = {
      ui: {
        titles: {
          processCheckpoint: this._currentLanguage === LANGUAGES.VN 
            ? "Phê Duyệt Tài Liệu" 
            : "Document Approval"
        },
        buttons: {
          approve: this._currentLanguage === LANGUAGES.VN 
            ? "Phê Duyệt" 
            : "Approve",
          reject: this._currentLanguage === LANGUAGES.VN 
            ? "Từ Chối" 
            : "Reject"
        }
      }
    };

    this._strings = this._advancedMerger(approvalStrings, this._currentLanguage);
  }
}

// Utility class for managing localization across multiple controls
export class LocalizationManager {
  private static _instance: LocalizationManager;
  private _languageLoader: (lang: string | number | undefined | null) => LocalizationStrings;
  private _currentLanguage: SupportedLanguage;

  private constructor() {
    this._languageLoader = createLanguageLoader({
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });
    this._currentLanguage = detectSystemLanguage();
  }

  public static getInstance(): LocalizationManager {
    if (!LocalizationManager._instance) {
      LocalizationManager._instance = new LocalizationManager();
    }
    return LocalizationManager._instance;
  }

  public setLanguage(language: SupportedLanguage): void {
    this._currentLanguage = language;
  }

  public getCurrentLanguage(): SupportedLanguage {
    return this._currentLanguage;
  }

  public getStrings(customStrings?: string | Partial<LocalizationStrings> | null): LocalizationStrings {
    const merger = createAdvancedLocalizationMerger(englishStrings, {
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });
    
    return merger(customStrings || null, this._currentLanguage);
  }
}