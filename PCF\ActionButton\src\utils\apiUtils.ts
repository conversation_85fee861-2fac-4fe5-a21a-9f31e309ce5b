/**
 * API utility functions for ActionButton PCF Control
 * Following SharedLibraries patterns and PCF-CodingRules.md standards
 * Uses XrmUtility ActionUtility for action execution
 */

import {
    createLanguageLoader,
    LANGUAGES
} from "../../../SharedLibraries/utils/LocalizationUtils";
import {
    ActionUtility,
    EntityUtils
} from "../../../SharedLibraries/utils/XrmUtility";
import type { ActionResponse, NestedObject } from "../../../SharedLibraries/types/CoreTypes";
import { englishStrings } from '../strings/en';
import { vietnameseStrings } from '../strings/vi';
import type { LocalizationStrings } from '../types/LocalizationTypes';
import type { ApiAction, ApiPayload, ActionButtonPayload, ApiResponse, ApiError } from '../types';

// Create language-aware loader with proper type casting
const languageLoader = createLanguageLoader({
    [LANGUAGES.ENG]: englishStrings,
    [LANGUAGES.VN]: vietnameseStrings
}) as (lang: string | number | undefined | null) => LocalizationStrings;

/**
 * Default action name for ActionButton operations
 */
const DEFAULT_ACTION_NAME = 'lok_ActionButtonOperation';

/**
 * Makes an action call using SharedLibraries XrmUtility
 * @param actionName - Custom action name
 * @param payload - Request payload
 * @param languageId - Language ID for localized messages
 * @param endpoint - Optional custom endpoint
 * @returns Promise<ApiResponse>
 */
export const callApi = (
    actionName: string,
    payload: ApiPayload,
    languageId?: string | number,
    endpoint?: string
): Promise<ApiResponse | ApiError> => {
    // Get localized strings
    const strings = languageLoader(languageId || LANGUAGES.ENG);

    // Convert ApiPayload to ActionButtonPayload for this project
    const actionButtonPayload: ActionButtonPayload = {
        Command: payload.action,
        Data: payload.id,
    };

    // Cast to NestedObject for XrmUtility
    const actionPayload: NestedObject = actionButtonPayload;

    // Execute action using SharedLibraries XrmUtility
    return ActionUtility.executeAction({
        actionName: actionName || DEFAULT_ACTION_NAME,
        payload: actionPayload,
        endpoint: endpoint,
        isBound: false,
    })
        .then((result: ActionResponse) => {
            if (result.success) {
                const successResponse: ApiResponse = {
                    success: true,
                    data: result.data,
                    message: result.message || getSuccessMessage(payload.action, strings),
                    value: result.value,
                };
                return successResponse;
            } else {
                const errorResponse: ApiError = {
                    success: false,
                    error: result.error || strings.ui.errors.apiError,
                    code: result.code || 'API_ERROR',
                };
                return errorResponse;
            }
        })
        .catch((error: unknown) => {
            // Handle different error types
            const apiError: ApiError = {
                success: false,
                error: error instanceof Error ? error.message : strings.ui.errors.unknownError,
                code: getErrorCode(error),
            };

            return apiError;
        });
};

// Removed isValidUrl - not needed with XrmUtility

/**
 * Gets success message based on action type
 * @param action - API action type
 * @param strings - Localized strings
 * @returns string
 */
const getSuccessMessage = (action: ApiAction, strings: typeof englishStrings): string => {
    switch (action) {
        case strings.api.actions.create:
            return strings.api.messages.createSuccess;
        case strings.api.actions.update:
            return strings.api.messages.updateSuccess;
        case strings.api.actions.remove:
            return strings.api.messages.removeSuccess;
        default:
            return strings.api.messages.createSuccess;
    }
};

/**
 * Gets error code from error object
 * @param error - Error object
 * @returns string
 */
const getErrorCode = (error: unknown): string => {
    if (error instanceof Error) {
        if (error.name === 'AbortError') {
            return 'TIMEOUT';
        }
        if (error.message.includes('HTTP')) {
            return 'HTTP_ERROR';
        }
        if (error.message.includes('network') || error.message.includes('fetch')) {
            return 'NETWORK_ERROR';
        }
    }
    return 'UNKNOWN_ERROR';
};

/**
 * Creates API payload for the specified action - matches README.md specification
 * @param action - API action type
 * @returns ApiPayload with real entity ID from XrmUtility
 */
export const createApiPayload = (action: ApiAction): ApiPayload => {
    return {
        action,
        id: EntityUtils.getEntityId(),
    };
};

/**
 * Gets default action name for ActionButton operations
 * @returns string
 */
export const getDefaultActionName = (): string => {
    return DEFAULT_ACTION_NAME;
};
