﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PowerAppsTargetsPath>$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\PowerApps</PowerAppsTargetsPath>
  </PropertyGroup>

  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" />
  <Import Project="$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Solution.props" Condition="Exists('$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Solution.props')" />

  <PropertyGroup>
    <ProjectGuid>b7a05156-d98b-4987-bcf5-4ed911b0b684</ProjectGuid>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <!--Remove TargetFramework when this is available in 16.1-->
    <TargetFramework>net462</TargetFramework>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>

  <PropertyGroup>
    <AutoDeploy>false</AutoDeploy>
    <SolutionRootPath>src</SolutionRootPath>
    <SolutionXmlPath>$(MSBuildThisFileDirectory)$(SolutionRootPath)\Other\Solution.xml</SolutionXmlPath>

    <BuildTimestamp>$([System.DateTime]::Now.ToString('yyyy-MM-dd_HHmmss'))</BuildTimestamp>
    <LogRoot>$(MSBuildProjectDirectory)\.logs\</LogRoot>
    <LogFile>$(LogRoot)PCFBuilder_$(BuildTimestamp).log</LogFile>

    <VersionRegex><![CDATA[^(0|(?:[1-9]\d*))(?:\.(0|(?:[1-9]\d*))(?:\.(0|(?:[1-9]\d*)))?(?:\.([0-9]+))?)$]]></VersionRegex>
  </PropertyGroup>

  <Choose>
    <When Condition=" '$(PackageType.ToLower())' == 'unmanaged' ">
      <PropertyGroup>
        <SolutionPackageType>Unmanaged</SolutionPackageType>
      </PropertyGroup>
    </When>
    <When Condition=" '$(PackageType.ToLower())' == 'managed' ">
      <PropertyGroup>
        <SolutionPackageType>Managed</SolutionPackageType>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <SolutionPackageType>Both</SolutionPackageType>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <Choose>
    <When Condition=" '$(BuildVersion)' != '' AND $([System.Text.RegularExpressions.Regex]::IsMatch('$(BuildVersion)', '$(VersionRegex)'))">
      <PropertyGroup>
        <Major>$([System.Text.RegularExpressions.Regex]::Match($(BuildVersion), $(VersionRegex)).Groups[1].Value)</Major>
        <Minor>$([System.Text.RegularExpressions.Regex]::Match($(BuildVersion), $(VersionRegex)).Groups[2].Value)</Minor>
        <Patch>$([System.Text.RegularExpressions.Regex]::Match($(BuildVersion), $(VersionRegex)).Groups[3].Value)</Patch>
        <Build>$([System.Text.RegularExpressions.Regex]::Match($(BuildVersion), $(VersionRegex)).Groups[4].Value)</Build>
        <SolutionVersion Condition=" '$(Major)' != '' AND '$(Minor)' != '' AND '$(Patch)' != '' AND '$(Build)' != '' ">$(Major).$(Minor).$(Patch).$(Build)</SolutionVersion>
        <SolutionVersion Condition=" '$(Major)' != '' AND '$(Minor)' != '' AND '$(Patch)' != '' AND '$(Build)' == '' ">$(Major).$(Minor).$(Patch).0</SolutionVersion>
        <SolutionVersion Condition=" '$(Major)' != '' AND '$(Minor)' != '' AND '$(Patch)' == '' AND '$(Build)' == '' ">$(Major).$(Minor).0.0</SolutionVersion>
        <SolutionVersion Condition=" '$(Major)' != '' AND '$(Minor)' == '' AND '$(Patch)' == '' AND '$(Build)' == '' ">$(Major).0.0.0</SolutionVersion>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <SolutionVersion>*******</SolutionVersion>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <Choose>
    <When Condition=" '$(SolutionVersion)' != '' AND '$(SolutionPackageType)' == 'Unmanaged' ">
      <PropertyGroup>
        <SolutionPackageZipFileName>$(MSBuildProjectName)_$(SolutionVersion.Replace(".", "_")).zip</SolutionPackageZipFileName>
      </PropertyGroup>
    </When>
    <When Condition=" '$(SolutionVersion)' != '' AND '$(SolutionPackageType)' == 'Managed' ">
      <PropertyGroup>
        <SolutionPackageZipFileName>$(MSBuildProjectName)_$(SolutionVersion.Replace(".", "_"))_managed.zip</SolutionPackageZipFileName>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <SolutionPackageZipFileName>$(MSBuildProjectName).zip</SolutionPackageZipFileName>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <PropertyGroup>
    <PcfBuildScript>
      <![CDATA[
        param(
          [string]$SolutionVersion,
          [string]$SolutionPackageType,
          [string]$ProjectFile,
          [string]$LogFile,
          [string]$SolutionXmlPath
        )

        # ===================================================================
        #                     FUNCTION DECLARATIONS
        # ===================================================================

        # Writes a detailed log message
        function Write-Log {
          param([string]$Message, [int]$Indent = 0)
          $prefix = " " * ($Indent * 2) # Create indentation
          $logDir = Split-Path -Path $LogFile -Parent
          if (-not (Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir | Out-Null
          }
          $logMessage = "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $prefix$Message"
          $logMessage | Out-File -FilePath $LogFile -Append
          Write-Host $logMessage
        }

        # This function ensures the XML file is always saved with UTF-8 encoding without a BOM (Byte Order Mark).
        function Save-XmlDocument {
          param(
            [System.Xml.XmlDocument]$Document,
            [string]$FilePath
          )

          $writerSettings = New-Object System.Xml.XmlWriterSettings
          $writerSettings.Indent = $true
          # Use UTF8Encoding($false) to specify no BOM.
          $writerSettings.Encoding = New-Object System.Text.UTF8Encoding($false) 
            
          $xmlWriter = [System.Xml.XmlWriter]::Create($FilePath, $writerSettings)
          try {
            $Document.Save($xmlWriter)
          }
          finally {
            if ($null -ne $xmlWriter) {
              $xmlWriter.Close()
            }
          }
        }

        # Updates the Solution.xml file
        function Update-SolutionXml {
          param(
            [string]$XmlPath, 
            [string]$Version, 
            [string]$PackageType
          )
            
          Write-Log "Updating Solution.xml..." -Indent 1
          $typeMap = @{"Managed"="1"; "Unmanaged"="0"; "Both"="2"}
          if (-not $typeMap.ContainsKey($PackageType)) { throw "Invalid type: $PackageType" }
          
          $managedValue = $typeMap[$PackageType]
          [xml]$solutionXml = Get-Content -Path $XmlPath -Raw
          $manifestNode = $solutionXml.SelectSingleNode("//SolutionManifest")
          if ($null -eq $manifestNode) { throw "<SolutionManifest> node not found." }

          $versionNode = $manifestNode.SelectSingleNode("Version")
          if ($null -eq $versionNode) { $versionNode = $solutionXml.CreateElement("Version"); $manifestNode.AppendChild($versionNode) }
          $versionNode.InnerText = $Version

          $managedNode = $manifestNode.SelectSingleNode("Managed")
          if ($null -eq $managedNode) { $managedNode = $solutionXml.CreateElement("Managed"); $manifestNode.AppendChild($managedNode) }
          $managedNode.InnerText = $managedValue
          
          Save-XmlDocument -Document $solutionXml -FilePath $XmlPath
          Write-Log "Solution.xml updated successfully." -Indent 2
        }

        # Synchronizes a single PCF control
        function Sync-PcfControl {
          param(
            [System.Xml.XmlNode]$ProjectRef,
            [string]$BaseDirectory
          )
          $pcfProjPath = $ProjectRef.Include
          Write-Log "Processing PCF Control: $pcfProjPath" -Indent 1
          
          $pcfProjectFullPath = Join-Path $BaseDirectory $pcfProjPath
          $pcfRootDirUnresolved = Split-Path -Path $pcfProjectFullPath -Parent
          # Resolve the path to its canonical (clean) form for logging
          $pcfRootDir = (Resolve-Path -LiteralPath $pcfRootDirUnresolved).Path

          $packageJsonPath = Join-Path $pcfRootDir "package.json"
          if (-not (Test-Path $packageJsonPath)) { throw "`package.json` not found at '$packageJsonPath'" }

          # Dynamically find ControlManifest.Input.xml
          Write-Log "Searching for 'ControlManifest.Input.xml' inside '$pcfRootDir'..." -Indent 2
          $manifestFile = Get-ChildItem -Path $pcfRootDir -Filter "ControlManifest.Input.xml" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
          if ($null -eq $manifestFile) { throw "'ControlManifest.Input.xml' not found in project '$pcfRootDir'" }
          
          $manifestPath = $manifestFile.FullName
          Write-Log "Found manifest at: $manifestPath" -Indent 2

          $packageJson = Get-Content $packageJsonPath | ConvertFrom-Json
          $pcfVersion = $packageJson.version
          if ($null -eq $pcfVersion -or $pcfVersion -eq "") {
            Write-Log "WARNING: 'version' property not found or is empty in '$packageJsonPath'. Skipping sync for this control." -Indent 2
            return # Exit
          }
          Write-Log "Found version '$pcfVersion' in package.json." -Indent 2

          [xml]$manifestXml = Get-Content $manifestPath -Raw
          if ($manifestXml.manifest.control.version -ne $pcfVersion) {
            $manifestXml.manifest.control.version = $pcfVersion
            Save-XmlDocument -Document $manifestXml -FilePath $manifestPath
            Write-Log "SUCCESS: Synced version to ControlManifest.Input.xml." -Indent 2
          } else {
            Write-Log "INFO: Version is already in sync." -Indent 2
          }
        }

        # ===================================================================
        #                         MAIN EXECUTION
        # ===================================================================

        try {
          # Sanitize input parameters to remove quotes
          $ProjectFile = $ProjectFile.Trim("'"); $LogFile = $LogFile.Trim("'"); $SolutionXmlPath = $SolutionXmlPath.Trim("'"); $SolutionVersion = $SolutionVersion.Trim("'"); $SolutionPackageType = $SolutionPackageType.Trim("'")

          Write-Log "========== PCF Build Tool Started =========="
          Write-Log "Solution Version: $SolutionVersion, Package Type: $SolutionPackageType"
          
          # Task 1: Update Solution.xml
          Update-SolutionXml -XmlPath $SolutionXmlPath -Version $SolutionVersion -PackageType $SolutionPackageType

          # Task 2: Synchronize PCF Controls
          Write-Log "Synchronizing PCF controls..."
          $projectDirectory = Split-Path -Path $ProjectFile -Parent
          [xml]$cdsprojXml = Get-Content $ProjectFile -Raw
          $ns = @{ "msbuild" = "http://schemas.microsoft.com/developer/msbuild/2003" }
          $pcfProjects = $cdsprojXml | Select-Xml -XPath "//msbuild:ProjectReference[contains(@Include, '.pcfproj')]" -Namespace $ns
          $pcfProjectsArray = @($pcfProjects)
          $projectCount = $pcfProjectsArray.Count
          if ($projectCount -gt 0) {
            Write-Log "Found $projectCount PCF project(s)."
            foreach ($project in $pcfProjectsArray) {
              Sync-PcfControl -ProjectRef $project.Node -BaseDirectory $projectDirectory
            }
          } else {
            Write-Log "Warning: No PCF project references found."
          }
          Write-Log "========== PCF Build Tool Finished Successfully =========="
        }
        catch {
          $errorMessage = $_.Exception.Message
          Write-Host "##vso[task.logissue type=error]PCF Build Tool error : $errorMessage"
          if ($LogFile) { Write-Log "ERROR: $errorMessage" }
          if ($LogFile) { Write-Log "========== PCF Build Tool Failed ==========" }
          exit 1
        }
      ]]>
    </PcfBuildScript>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.PowerApps.MSBuild.Solution" Version="1.*" />
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.0" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\.gitignore" />
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\bin\**" />
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\obj\**" />
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\*.cdsproj" />
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\*.cdsproj.user" />
    <ExcludeDirectories Include="$(MSBuildThisFileDirectory)\*.sln" />
  </ItemGroup>

  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)\**" Exclude="@(ExcludeDirectories)" />
    <Content Include="$(SolutionPackageZipFilePath)">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ActionButton\ActionButton.pcfproj" />
    <ProjectReference Include="..\ComboboxControl\ComboboxControl.pcfproj" />
    <ProjectReference Include="..\ProcessCheckpoint\ProcessCheckpoint.pcfproj" />
  </ItemGroup>

  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />
  <Import Project="$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Solution.targets" Condition="Exists('$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Solution.targets')" />

  <Target Name="RunPcfBuildTool">
    <PropertyGroup>
      <TempScriptFile>$(IntermediateOutputPath)PcfBuildScript.ps1</TempScriptFile>
    </PropertyGroup>
    
    <Message Text="Generating temporary PowerShell script at $(TempScriptFile)..." Importance="high" />
    <WriteLinesToFile File="$(TempScriptFile)" Lines="$(PcfBuildScript)" Overwrite="true" Encoding="Unicode" />
    
    <Message Text="Executing PCF Build Tool via temporary script file..." Importance="high" />
    <Exec Command="powershell.exe -NonInteractive -ExecutionPolicy Bypass -File &quot;$(TempScriptFile)&quot; -SolutionVersion '$(SolutionVersion)' -SolutionPackageType '$(SolutionPackageType)' -ProjectFile &quot;$(MSBuildProjectFullPath)&quot; -LogFile &quot;$(LogFile)&quot; -SolutionXmlPath &quot;$(SolutionXmlPath)&quot;" />
  </Target>

  <Target Name="CleanUpPcfScript">
    <Delete Files="$(IntermediateOutputPath)PcfBuildScript.ps1" Condition="Exists('$(IntermediateOutputPath)PcfBuildScript.ps1')" />
  </Target>

  <Target Name="CheckPacEnvironment">
    <PropertyGroup>
      <PacCommand>pac env who --json</PacCommand>
    </PropertyGroup>
    <Message Text="Checking for active PAC CLI environment authentication..." Importance="high" />
    <Exec Command="$(PacCommand)" ConsoleToMSBuild="true" StandardOutputImportance="low" IgnoreExitCode="false" />
  </Target>

  <Target Name="SetDeployParameters">
    <PropertyGroup>
      <PacCommand>pac solution import</PacCommand>
      
      <PacOptionPath>--path "bin\\Release\\$(SolutionPackageZipFileName)"</PacOptionPath>
      <PacOptionAsync>--async true</PacOptionAsync>
      <PacOptionStageAndUpgrade>--stage-and-upgrade true</PacOptionStageAndUpgrade>
      <PacOptionSkipDependencyCheck>--skip-dependency-check false</PacOptionSkipDependencyCheck>
      <PacOptionConvertToManaged>--convert-to-managed false</PacOptionConvertToManaged>
      <PacOptionMaxWaitTime>--max-async-wait-time 900</PacOptionMaxWaitTime>
      <PacOptionActivatePlugins>--activate-plugins true</PacOptionActivatePlugins>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SolutionPackageType)' == 'Managed'">
      <PacOptionForceOverwrite>--force-overwrite false</PacOptionForceOverwrite>
      <PacOptionPublishChanges>--publish-changes false</PacOptionPublishChanges>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SolutionPackageType)' == 'Unmanaged'">
      <PacOptionForceOverwrite>--force-overwrite true</PacOptionForceOverwrite>
      <PacOptionPublishChanges>--publish-changes true</PacOptionPublishChanges>
    </PropertyGroup>
  </Target>

  <Target Name="PreBuild" BeforeTargets="PreBuildEvent">
    <CallTarget Targets="RunPcfBuildTool" />
  </Target>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <PropertyGroup>
      <SolutionPackageZipFilePath Condition="'$(SolutionPackageZipFileName)' != ''">$(OutputPath)$(SolutionPackageZipFileName)</SolutionPackageZipFilePath>
      <!--Turn off auto deploy after build sucess-->
      <AutoDeploy>false</AutoDeploy>
    </PropertyGroup>
    <CallTarget Targets="CleanUpPcfScript" />
  </Target>

  <Target Name="DeploySolution" AfterTargets="Build" Condition="'$(AutoDeploy)'=='true'">
    <Exec Command="pac solution import --path bin\\Release\\$(SolutionPackageZipFileName) --async true --stage-and-upgrade true --force-overwrite false --publish-changes false --skip-dependency-check false --convert-to-managed false --max-async-wait-time 900 --activate-plugins true" YieldDuringToolExecution="True" ConsoleToMSBuild="true" StandardOutputImportance="high">
      <Output TaskParameter="ConsoleOutput" ItemName="OutputOfExec" />
	  </Exec>
  </Target>

</Project>
