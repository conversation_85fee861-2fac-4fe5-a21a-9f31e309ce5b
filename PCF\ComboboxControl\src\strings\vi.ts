/**
 * ComboboxControl Localization Strings - Vietnamese
 * Follows LocalizationUtils pattern for consistency
 */

import type { LocalizationStrings } from "../types/LocalizationTypes";

export const vietnameseStrings: LocalizationStrings = {
  ui: {
    labels: {
      attribute: 'Chọn một thuộc tính',
      stringAttribute: 'Chọn thuộc tính Dòng văn bản đơn',
      picklistAttribute: 'Chọn thuộc tính Lựa chọn',
      dateTimeAttribute: 'Chọn thuộc tính Ngày và giờ',
      statuscode: 'Chọn lý do trạng thái',
      statecode: 'Chọn trạng thái',
      error: 'Lỗi:',
    },
    messages: {
      loading: '<PERSON>ang tải...',
      selectBPF: 'Vui lòng chọn Quy trình Nghiệp vụ.',
    },
  },
  errors: {
    statecodeNotConfigured: 'Trường mã trạng thái chưa đượ<PERSON> cấu hình.',
    permissionDenied: '<PERSON>uy<PERSON><PERSON> bị từ chối: Bạn không có quyền truy cập tài nguyên này.',
    timeout: '<PERSON><PERSON><PERSON> cầu hết thời gian chờ sau 10 giây.',
    attributeNotFound: 'Không tìm thấy thuộc tính {fieldType} cho thực thể {entityName}.',
    optionsLoadFailed: 'Không thể tải tùy chọn: {error}.',
    bpfMissingPrimaryEntity: 'Quy trình nghiệp vụ {bpfId} không có thực thể chính được định nghĩa.',
    bpfNotFound: 'Không tìm thấy Quy trình Nghiệp vụ với ID {bpfId}.',
    entityNotFound: 'Không tìm thấy thực thể {entityName}.',
  },
  constants: {
    // PCF Compliance constants - same as English
    workflow: 'workflow',
    selectPrimaryEntity: '?$select=primaryentity',
    displayNameFormat: 'Display Name (logicalname)',
    freeformKey: 'freeform',
    alertRole: 'alert',
  },
};
