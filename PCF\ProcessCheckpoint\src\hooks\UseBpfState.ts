import * as React from "react";
import {
  type AttachedFile,
  type BpfStageInfo,
  BpfStatusType,
  type IBpfRepository,
  type ITimelineRepository,
  ReadOnlyReasonType,
} from "../types";

interface UseBpfStateProps {
  bpfRepository: IBpfRepository | null;
  timelineRepository: ITimelineRepository | null;
  targetStageId?: string | null;
  sourceControl?: string | null;
  setTextValue: React.Dispatch<React.SetStateAction<string>>;
  setAttachedFiles: React.Dispatch<React.SetStateAction<AttachedFile[]>>;
}

interface UseBpfStateResult {
  activeStageInfo: BpfStageInfo | null;
  selectedStageInfo: BpfStageInfo | null;
  processStatus: BpfStatusType | null;
  readOnlyReason: ReadOnlyReasonType | null;
  isLoadingBpfState: boolean;
  loadStageData: (stageId: string) => Promise<void>;
}

export const useBpfState = ({
  bpfRepository,
  timelineRepository,
  targetStageId,
  sourceControl,
  setTextValue,
  setAttachedFiles,
}: UseBpfStateProps): UseBpfStateResult => {
  const [isLoadingBpfState, setIsLoadingBpfState] = React.useState(true);
  const [readOnlyReason, setReadOnlyReason] =
    React.useState<ReadOnlyReasonType | null>(null);
  const [activeStageInfo, setActiveStageInfo] =
    React.useState<BpfStageInfo | null>(null);
  const [selectedStageInfo, setSelectedStageInfo] =
    React.useState<BpfStageInfo | null>(null);
  const [processStatus, setProcessStatus] =
    React.useState<BpfStatusType | null>(null);

  const resetInputFields = React.useCallback(() => {
    setTextValue("");
    setAttachedFiles([]);
  }, [setTextValue, setAttachedFiles]);

  const loadStageData = React.useCallback(
    (stageId: string): Promise<void> => {
      if (!timelineRepository) {
        resetInputFields();
        return Promise.resolve();
      }
      return timelineRepository
        .getLatestStageNote(stageId)
        .then((result) => {
          if (result.success && result.data) {
            const latestNote = result.data;
            setTextValue(latestNote.noteText ?? "");
            setAttachedFiles(latestNote.attachments ?? []);
          } else {
            resetInputFields();
          }
          return;
        })
        .catch(() => {
          resetInputFields();
        });
    },
    [resetInputFields, setTextValue, setAttachedFiles, timelineRepository],
  );

  React.useEffect(() => {
    let isMounted = true;

    const evaluateStageStatus = () => {
      if (!bpfRepository) {
        if (isMounted) {
          setReadOnlyReason(ReadOnlyReasonType.ERROR);
          resetInputFields();
        }
        setIsLoadingBpfState(false);
        return;
      }

      const currentActiveStageInfo = bpfRepository.getCurrentStageInfo();
      const currentSelectedStageInfo = bpfRepository.getSelectedStageInfo();
      const currentProcessStatus =
        bpfRepository.getStatusProcess() as BpfStatusType;

      setActiveStageInfo(currentActiveStageInfo);
      setSelectedStageInfo(currentSelectedStageInfo);
      setProcessStatus(currentProcessStatus);

      const stageIdToCheck = targetStageId ?? currentSelectedStageInfo?.stageId;
      const stageToCheck = stageIdToCheck
        ? bpfRepository.getStageInfoById(stageIdToCheck)
        : null;

      // Case 1: Process is finished or aborted. Always read-only.
      if (
        currentProcessStatus === BpfStatusType.FINISH ||
        currentProcessStatus === BpfStatusType.ABORT
      ) {
        if (isMounted) {
          setReadOnlyReason(ReadOnlyReasonType.FINISHED);
          resetInputFields();
        }
        setIsLoadingBpfState(false);
        return;
      }

      if (!currentActiveStageInfo || !stageToCheck) {
        if (isMounted) {
          setReadOnlyReason(ReadOnlyReasonType.ERROR);
          resetInputFields();
        }
        setIsLoadingBpfState(false);
        return;
      }

      // Case 2: Selected stage = Active stage
      // As per the defined logic, if the process is 'active', the active stage should always be editable.
      if (currentActiveStageInfo.stageId === stageToCheck.stageId) {
        if (isMounted) {
          setReadOnlyReason(null);
          setTextValue(sourceControl ?? "");
        }
        setIsLoadingBpfState(false);
        return;
      }

      // Case 3: Selected stage ≠ Active stage
      bpfRepository
        .getProcessStages()
        .then((pathResult) => {
          if (!isMounted) {
            return null;
          }
          if (!pathResult.success || !pathResult.data) {
            setReadOnlyReason(ReadOnlyReasonType.ERROR);
            setIsLoadingBpfState(false);
            return null;
          }
          const activePath = pathResult.data;
          const activeIndex = activePath.findIndex(
            (s) => s.stageId === currentActiveStageInfo.stageId,
          );
          const selectedIndex = activePath.findIndex(
            (s) => s.stageId === stageToCheck.stageId,
          );

          if (activeIndex !== -1 && selectedIndex !== -1) {
            if (selectedIndex < activeIndex) {
              // It's a previous stage, so it's read-only.
              setReadOnlyReason(ReadOnlyReasonType.PREVIOUS);
              resetInputFields();
            } else {
              // It's a next stage, so it's read-only and disabled.
              setReadOnlyReason(ReadOnlyReasonType.NEXT);
              resetInputFields();
            }
          }
          setIsLoadingBpfState(false);
          return null;
        })
        .catch(() => {
          if (isMounted) {
            setReadOnlyReason(ReadOnlyReasonType.ERROR);
            setIsLoadingBpfState(false);
          }
          return null;
        });
    };

    evaluateStageStatus();

    return () => {
      isMounted = false;
    };
  }, [
    bpfRepository,
    targetStageId,
    sourceControl,
    setTextValue,
  ]);

  return {
    activeStageInfo,
    selectedStageInfo,
    processStatus,
    readOnlyReason,
    isLoadingBpfState,
    loadStageData,
  };
};
