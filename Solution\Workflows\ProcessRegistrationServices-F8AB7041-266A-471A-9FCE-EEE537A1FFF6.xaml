<Activity x:Class="XrmWorkflowf8ab7041266a471a9fceeee537a1fff6" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" xmlns:mxs="clr-namespace:Microsoft.Xrm.Sdk;assembly=Microsoft.Xrm.Sdk, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" xmlns:mxsw="clr-namespace:Microsoft.Xrm.Sdk.Workflow;assembly=Microsoft.Xrm.Sdk.Workflow, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" xmlns:mxswa="clr-namespace:Microsoft.Xrm.Sdk.Workflow.Activities;assembly=Microsoft.Xrm.Sdk.Workflow, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" xmlns:srs="clr-namespace:System.Runtime.Serialization;assembly=System.Runtime.Serialization, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" xmlns:this="clr-namespace:" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="InputEntities" Type="InArgument(scg:IDictionary(x:String, mxs:Entity))" />
    <x:Property Name="CreatedEntities" Type="InArgument(scg:IDictionary(x:String, mxs:Entity))" />
    <x:Property Name="Request" Type="InArgument(x:String)">
      <x:Property.Attributes>
        <mxsw:ArgumentRequiredAttribute Value="False" />
        <mxsw:ArgumentTargetAttribute Value="False" />
        <mxsw:ArgumentDescriptionAttribute Value="Request" />
        <mxsw:ArgumentDirectionAttribute Value="Input" />
        <mxsw:ArgumentEntityAttribute Value="" />
      </x:Property.Attributes>
    </x:Property>
    <x:Property Name="Response" Type="OutArgument(x:String)">
      <x:Property.Attributes>
        <mxsw:ArgumentRequiredAttribute Value="False" />
        <mxsw:ArgumentTargetAttribute Value="False" />
        <mxsw:ArgumentDescriptionAttribute Value="Response" />
        <mxsw:ArgumentDirectionAttribute Value="Output" />
        <mxsw:ArgumentEntityAttribute Value="" />
      </x:Property.Attributes>
    </x:Property>
  </x:Members>
  <this:XrmWorkflowf8ab7041266a471a9fceeee537a1fff6.InputEntities>
    <InArgument x:TypeArguments="scg:IDictionary(x:String, mxs:Entity)" />
  </this:XrmWorkflowf8ab7041266a471a9fceeee537a1fff6.InputEntities>
  <this:XrmWorkflowf8ab7041266a471a9fceeee537a1fff6.CreatedEntities>
    <InArgument x:TypeArguments="scg:IDictionary(x:String, mxs:Entity)" />
  </this:XrmWorkflowf8ab7041266a471a9fceeee537a1fff6.CreatedEntities>
  <mva:VisualBasic.Settings>Assembly references and imported namespaces for internal implementation</mva:VisualBasic.Settings>
  <mxswa:Workflow />
</Activity>