import * as React from "react";
import {
  Button,
  MessageBar,
  MessageBarBody,
  MessageBarTitle,
} from "@fluentui/react-components";
import {
  getValidatedLanguage,
  createLanguageLoader,
  LANGUAGES,
  type SupportedLanguage,
} from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from "../strings/en";
import { vietnameseStrings } from "../strings/vi";
import { UI_COLORS, UI_DIMENSIONS, UI_SPACING, UI_TYPOGRAPHY } from "../types";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  language?: SupportedLanguage;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback && this.state.error) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent
            error={this.state.error}
            retry={this.handleRetry}
          />
        );
      }

      // Create language loader
      const languageLoader = createLanguageLoader({
        [LANGUAGES.ENG]: englishStrings,
        [LANGUAGES.VN]: vietnameseStrings
      });

      const strings = languageLoader(getValidatedLanguage(this.props.language));

      return (
        <div
          style={{
            padding: UI_SPACING.SPACING_L,
            maxWidth: UI_DIMENSIONS.CONFIRMATION_MODAL_MAX_WIDTH,
          }}
        >
          <MessageBar intent="error">
            <MessageBarBody>
              <MessageBarTitle>
                {strings.errors.errorBoundary.title}
              </MessageBarTitle>
              {strings.errors.errorBoundary.description}
              <div style={{ marginTop: UI_SPACING.SPACING_M }}>
                <Button
                  appearance="primary"
                  size="small"
                  onClick={this.handleRetry}
                >
                  {strings.ui.buttons.tryAgain}
                </Button>
              </div>
              {this.state.error && (
                <details
                  style={{
                    marginTop: UI_SPACING.SPACING_M,
                    fontSize: UI_TYPOGRAPHY.FONT_SIZE_SMALL,
                  }}
                >
                  <summary>{strings.ui.labels.errorDetails}</summary>
                  <pre
                    style={{
                      whiteSpace: "pre-wrap",
                      backgroundColor: UI_COLORS.background.CODE_BLOCK,
                      padding: UI_SPACING.SPACING_S,
                      borderRadius: UI_DIMENSIONS.DEFAULT_BORDER_RADIUS,
                      marginTop: UI_SPACING.SPACING_S,
                    }}
                  >
                    {this.state.error.message}
                    {this.state.error.stack}
                  </pre>
                </details>
              )}
            </MessageBarBody>
          </MessageBar>
        </div>
      );
    }

    return this.props.children;
  }
}

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>,
  language?: SupportedLanguage,
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} language={language}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName ?? Component.name ?? "Component"})`;
  return WrappedComponent;
};
