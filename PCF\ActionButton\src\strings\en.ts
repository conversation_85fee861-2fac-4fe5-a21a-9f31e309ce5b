/**
 * English localization strings for ActionButton PCF Control
 * Following SharedLibraries LocalizationUtils patterns
 */

import type { LocalizationStrings } from '../types/LocalizationTypes';

export const englishStrings: LocalizationStrings = {
    ui: {
        buttons: {
            create: "Apply Control",
            update: "Modify Control",
            remove: "Revert Control",
        },
        messages: {
            processing: "Processing your request...",
        },
        errors: {
            apiError: "Failed to complete the operation. Please try again.",
            unknownError: "An unexpected error occurred.",
        },
    },
    api: {
        actions: {
            create: "applycontrol",
            update: "modifycontrol",
            remove: "revertcontrol",
        },
        messages: {
            createSuccess: "Control applied successfully",
            updateSuccess: "Control modified successfully",
            removeSuccess: "Control reverted successfully",
        },
    },
};
