/**
 * Core Types - Fundamental interfaces used across SharedLibraries
 */
export interface NestedObject {
  [key: string]: string | NestedObject;
}

export interface Entity {
  [key: string]: unknown;
}

export interface ErrorObject extends Entity {
  status?: number;
  message?: string;
}

export interface RetrieveMultipleResponse<T extends Entity = Entity> {
  entities: T[];
  nextLink?: string;
  value?: T[];
}

export interface LookupValue {
  id: string;
  name?: string;
  entityType?: string;
}

export interface RepositoryResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface RepositoryOptions {
  timeout?: number;
  retryCount?: number;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ActionResponse {
  success: boolean;
  data?: unknown;
  message?: string;
  value?: string;
  error?: string;
  code?: string;
}

export interface ActionRequest {
  [key: string]: unknown;
  getMetadata: () => ActionMetadata;
}

export interface ActionMetadata {
  boundParameter: string | null;
  operationType: number;
  operationName: string;
  parameterTypes: Record<string, { typeName: string; structuralProperty: number }>;
}

export interface WebApiExecuteResponse {
  ok: boolean;
  status?: number;
  statusText?: string;
  json: () => Promise<unknown>;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl?: number;
}

export interface ICacheUtility<T> {
  get: (key: string) => T | null;
  set: (key: string, data: T, ttl?: number) => void;
  clear: (key: string) => void;
  clearAll: () => void;
  has: (key: string) => boolean;
  isExpired: (key: string) => boolean;
}
