# PCF-Components

Here is a collection of components I have created using the [PowerApps Component Framework](https://docs.microsoft.com/en-us/powerapps/developer/component-framework/overview). These are just examples of what can be done using the currently available tools.

## Pre-requisites

In order to build and deploy these to your CDS instance you'll need the following:

- [NodeJS & npm](https://nodejs.org/en/) (LTS version is recommended)
- [The PCF CLI](https://docs.microsoft.com/en-us/powerapps/developer/component-framework/create-custom-controls-using-pcf)
- Either:
  - [Visual Studio Code](https://code.visualstudio.com/) (with [.NET Core SDK](https://dotnet.microsoft.com/download))
  - [Visual Studio](https://visualstudio.microsoft.com/) (2019 or later)

## Build the PCF control

Once you've grabbed the code, navigate to the correct folder for the component you want (Example: **📁ZLK.PCFControls/ComboboxControl**) , and run the following command from the terminal:

```cmd
npm install
```

> **Note**
>
> If you receive the error `The term 'npm' is not recognized as the name of a cmdlet, function, script file, or operable program.`, make sure you have installed [node.js](https://nodejs.org/en/download/) (LTS version is recommended) and all other prerequisites.
>
> **Tip**
>
> If you receive the warn:
>
> ```cmd
> npm WARN deprecated @babel/plugin-proposal-nullish-coalescing-operator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
> npm WARN deprecated @babel/plugin-proposal-class-properties@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
> npm WARN deprecated @babel/plugin-proposal-object-rest-spread@7.20.7: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.
>
> ```
>
> You can open `package.json` and add:
>
> ```json
>     "devDependencies": {
>       ...
>     },
>     "overrides": {
>       "axios": "1.6.2"
>     }
>
> ```
>
> Then run the following command will fix it.
>
> ```cmd
> npm audit fix --force
> ```

## Build the CDS solution

From the **📁LOK.PCFControls/Deploy** folder, run the following command.

```cmd
msbuild /t:build /restore /p:Configuration=Release /p:BuildVersion=<BuildVersion> /p:PackageType=<PackageType>
```

Example:

```cmd
msbuild /t:build /restore /p:Configuration=Release /p:BuildVersion=******* /p:PackageType=Unmanaged
```

```cmd
msbuild /t:build /restore /p:PcfForceUpdate=true /p:Configuration=Release /p:BuildVersion=******* /p:PackageType=Unmanaged -verbosity:diagnostic /bl:msbuild.binlog -fileLoggerParameters:LogFile="msbuild.log";Verbosity=detailed;Encoding=UTF-8
```

The importable solution files can be found in **📁bin/Release**.

> **Note**
>
> eslint rules may impact your build, depending on how they have been configured. If you receive an error during build:
>
> ```cmd
> [12:58:30 PM] [build]  Failed:
> [pcf-1065] [Error] ESLint validation error:
> C:\project\LinearInput\LinearInputControl\index.ts
>   10:26  error  'EventListenerOrEventListenerObject' is not defined  no-undef
> ```
>
> Check your eslint rules in `.eslintrc.json` and set linting rules to `["warn"]`. For example, if you receive the error:
>
> ```error  'EventListenerOrEventListenerObject' is not defined  no-undef```
>
> Then you can open `.eslintrc.json` and edit the rules to add a `["warn"]` value for the rule `no-undef`:
>
> ```json
>     "rules": {
>       "no-unused-vars": "off",
>       "no-undef": ["warn"]
>     }
> ```
>
> With the eslint rules updated, your control should build cleanly.
>
> **Tip**
>
> If you want bypass run `npm install`
>
> Open file `<name of project>.pcfproj`
>
> Example:
>
> `ComboboxControl.pcfproj`
>
> Add `<PcfEnableAutoNpmInstall>false</PcfEnableAutoNpmInstall>` to `PropertyGroup`
>
> ```xml
> <PropertyGroup>
>   <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
>   <!--Remove TargetFramework when this is available in 16.1-->
>   <TargetFramework>net462</TargetFramework>
>   <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
>   <!--Turn off run command npm install-->
>   <PcfEnableAutoNpmInstall>false</PcfEnableAutoNpmInstall>
> </PropertyGroup>
> ```
>
> If you receive the deprecation warning:
>
> ```commandline
> [12:58:35 PM] [build]  Generating manifest types...
> DeprecationWarning: 'createInterfaceDeclaration' has been deprecated since v4.8.0. Decorators are no longer supported for this function. Callers should switch to an overload that does not accept a 'decorators' parameter.
> [12:58:35 PM] [build]  Generating design types...
> ```
>
> Run the following command will downgrade version `typerscript` to `4.7.4`
>
> ```cmd
> npm install typescript@4.7.4 --save-dev
> ```
>
> Then run the following command ```npm run build``` to rebuild.
>
> You can check list package with command:
>
> ```cmd
> npm list
> ```
>
> If you receive the deprecation warning:
>
> ```cmd
> WARNING in asset size limit: The following asset(s) exceed the recommended size limit (244 KiB).
> This can impact web performance.
> ```
>
> You can take advantage of tree-shaking (which only affects release/production builds) by updating your `tsconfig.json` to use the following module configuration inside the `compilerOptions` section:
>
> ```json
> "module": "es2015",
> "moduleResolution": "node"
> ```
>
> Example:
>
> ```json
> {
>   "extends": "./node_modules/pcf-scripts/tsconfig_base.json",
>   "compilerOptions": {
>     "typeRoots": ["node_modules/@types"],
>     "module": "es2015",
>     "moduleResolution": "node"
>   }
> }
> ```
>
> You can also use the following method to bypass check bundle size
>
> Open file `compileTask.js` in `node_modules\pcf-scripts\tasks` and find code `if (assets.length > 0 && assets[0].size > bundleSizeInBytes) {` then remove code
>
> If you receive the deprecation warning:
>
> ```cmd
> ERROR in ./LinearInput/index.js
> Module build failed (from ./node_modules/babel-loader/lib/index.js):
> Error: Cannot find module 'ajv/dist/compile/codegen'
> ```
>
> Run the following command will download `ajv`
>
> ```cmd
> npm install ajv --save-dev
> ```
>
> Then run the following command ```npm run build``` to rebuild.
>

## Create a new Component

Step 1: Create a new folder using the command `mkdir <folder name>` inside the sample component folder and navigate into the folder using the command `cd <folder name>`.

Step 2: Create a new project inside the folder using the following command:

```cmd
pac pcf init --namespace LokBiz.Controls --name <put component name here> --template <component type>
```

Example:

```cmd
pac pcf init --namespace LokBiz.Controls --name DemoPCF --template field
```

### Required Parameters

#### `--name` `-n`

The name for the component

**Note**: Only characters within the ranges [A - Z], [a - z] or [0 - 9] are allowed. The first character may not be a number.

##### `--template` `-t`

Choose a template for the component

Use one of these values:

- `field`
- `dataset`

### Optional Parameters

#### `--framework` `-fw`

The rendering framework for control. Default value is 'none' [none: HTML, react: React]

Use one of these values:

- `none`
- `react`

#### `--outputDirectory` `-o`

Output directory

Step 3: After the new project is created, from the **📁ZLK.PCFControls/Deploy** folder. You can add the reference using the following command:

```cmd
pac solution add-reference --path <Path to the root of the sample component>
```

Example:

```cmd
pac solution add-reference --path "..\DemoPCF\DemoPCF.pcfproj"
```

## Deploy

Import the solution generated in the previous step into your environment.
