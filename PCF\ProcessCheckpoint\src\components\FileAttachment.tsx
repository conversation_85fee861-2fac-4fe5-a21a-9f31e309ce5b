import * as React from "react";
import { <PERSON><PERSON>, Text, Tooltip } from "@fluentui/react-components";
import { UI_COLORS, UI_CURSORS } from "../../../SharedLibraries/types/Constants";
import { useFileAttachmentHandler } from "../hooks";
import type { LocalizationStrings } from "../types/LocalizationTypes";
import type {
  FileAttachmentConfig,
  ITimelineRepository,
} from "../types";
import type { AttachedFile } from "../../../SharedLibraries/types/FileTypes";
import {
  AttachIcon,
  DeleteIcon,
  DownloadIcon,
  getFileIcon,
} from "../../../SharedLibraries/components/FileIcons";
import {
  actionsStyle,
  dragActiveStyle,
  fileAttachmentContainerStyle,
  fileInfoStyle,
  fileItemStyle,
  fileListStyle,
  infoTextStyle,
  readOnlyContainerStyle,
  textStyles,
  utilityStyles,
} from "./styles";

export interface FileAttachmentProps {
  files: AttachedFile[];
  onFilesChange: (files: AttachedFile[]) => void;
  config: FileAttachmentConfig;
  readOnly?: boolean;
  timelineRepository?: ITimelineRepository | null;
  onValidationError?: (error: string) => void;
  strings: LocalizationStrings;
}

export const FileAttachment: React.FC<FileAttachmentProps> = ({
  files,
  onFilesChange,
  config,
  readOnly = false,
  timelineRepository,
  onValidationError,
  strings,
}) => {


  const {
    fileInputRef,
    dragActive,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    handleBrowseClick,
    handleKeyDown,
    handleFileInputChange,
    handleRemoveFile,
    handleDownloadFile,
    formatFileSize,
  } = useFileAttachmentHandler({
    files,
    onFilesChange,
    config,
    readOnly,
    timelineRepository: timelineRepository ?? null,
    onValidationError,
    strings,
  });

  const dropZoneStyle = React.useMemo(
    () => ({
      ...fileAttachmentContainerStyle,
      ...(dragActive ? dragActiveStyle : {}),
      width: "100%",
      cursor: readOnly ? UI_CURSORS.NOT_ALLOWED : UI_CURSORS.POINTER,
    }),
    [dragActive, readOnly],
  );

  const dropHandlers = !readOnly
    ? {
        onDrop: handleDrop,
        onDragOver: handleDragOver,
        onDragLeave: handleDragLeave,
      }
    : {};

  const hasFiles = files.length > 0;

  if (!config.enabled) {
    return null;
  }

  const hiddenInput = (
    <input
      ref={fileInputRef}
      type="file"
      multiple={false}
      accept={config.allowedTypes.map((type) => `.${type}`).join(",")}
      onChange={handleFileInputChange}
      style={utilityStyles.visuallyHidden}
      disabled={readOnly}
    />
  );

  return (
    <>
      {/* The dropzone area is always visible when not read-only to allow file replacement. */}
      {!readOnly && (
        <button
          type="button"
          style={dropZoneStyle}
          {...dropHandlers}
          onClick={handleBrowseClick}
          onKeyDown={handleKeyDown}
          aria-label={strings.ui.fileAttachment.dragDropText}
        >
          {hiddenInput}
          <AttachIcon title={strings.ui.buttons.attachFile} />
          <Text>
            {strings.ui.fileAttachment.dragDropText}{" "}
            <Text style={{ color: UI_COLORS.text.LINK, cursor: UI_CURSORS.POINTER, textDecoration: "underline" }}>
              {strings.ui.fileAttachment.browseText}
            </Text>
          </Text>
          <div style={infoTextStyle}>
            <div>
              {strings.ui.fileAttachment.maxSizeText}: {config.maxSizeInMB}MB
            </div>
            <div>
              {strings.ui.fileAttachment.allowedTypesText}:{" "}
              {config.allowedTypes.join(", ")}
            </div>
          </div>
        </button>
      )}

      {/* The list of attached files is displayed below the dropzone. */}
      {hasFiles ? (
        <div
          style={{
            ...(readOnly ? readOnlyContainerStyle : fileListStyle),
            marginTop: "1rem",
          }}
        >
          <Text weight="semibold">
            {strings.ui.fileAttachment.attachedFilesTitle}
          </Text>
          {files.map((file) => (
            <div key={file.id} style={fileItemStyle}>
              <div style={fileInfoStyle}>
                {getFileIcon(file.name, file.name)}
                <div>
                  <div style={textStyles.semibold}>{file.name}</div>
                  <div style={infoTextStyle}>
                    {formatFileSize(file.size)}
                  </div>
                </div>
              </div>
              <div style={actionsStyle}>
                <Tooltip
                  content={strings.ui.buttons.downloadFile}
                  relationship="label"
                >
                  <Button
                    appearance="subtle"
                    icon={<DownloadIcon title={strings.ui.buttons.downloadFile} />}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent file browser from opening
                      handleDownloadFile(file);
                    }}
                  />
                </Tooltip>
                {!readOnly && (
                  <Tooltip
                    content={strings.ui.buttons.removeFile}
                    relationship="label"
                  >
                    <Button
                      appearance="subtle"
                      icon={<DeleteIcon title={strings.ui.buttons.removeFile} />}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent file browser from opening
                        handleRemoveFile(file.id);
                      }}
                    />
                  </Tooltip>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        // This branch is only hit when !hasFiles.
        // If it's also readOnly, show the no files message.
        readOnly && (
          <div style={readOnlyContainerStyle}>
            <Text>{strings.ui.fileAttachment.noFilesText}</Text>
          </div>
        )
      )}
    </>
  );
};
