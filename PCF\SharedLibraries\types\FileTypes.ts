export interface AttachedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  content: string;
  uploadDate: Date;
}

export interface FileAttachmentConfig {
  enabled: boolean;
  allowedTypes: string[];
  maxSizeInMB: number;
  maxFiles: number;
}

export const DEFAULT_FILE_CONFIG: FileAttachmentConfig = {
  enabled: false,
  allowedTypes: [
    // Documents
    "pdf",
    "txt",

    // Microsoft Office
    "doc",
    "docx",
    "xls",
    "xlsx",
    "ppt",
    "pptx",

    // Project Management
    "mpp",
    "mppx",

    // Images
    "jpg",
    "jpeg",
    "png",
    "gif",

    // Archives
    "zip",
    "rar",
    "7z",

    // Email
    "msg",
  ],
  maxSizeInMB: 10,
  maxFiles: 1,
};

export const DEFAULT_FILE_TYPES_STRING =
  DEFAULT_FILE_CONFIG.allowedTypes.join(",");

export const parseFileTypes = (fileTypesString?: string): string[] => {
  if (!fileTypesString) return DEFAULT_FILE_CONFIG.allowedTypes;
  return fileTypesString
    .split(",")
    .map((type) => type.trim())
    .filter(Boolean);
};

/**
 * Validates a file against the provided configuration.
 * @param file The file to validate.
 * @param allowedTypes Array of allowed file extensions.
 * @param maxSizeInMB Maximum file size in MB.
 * @returns Validation result with success status and error message if any.
 */
export const validateFile = (
  file: File,
  allowedTypes: string[],
  maxSizeInMB: number
): { isValid: boolean; error?: string } => {
  const extension = file.name.split('.').pop()?.toLowerCase();
  
  if (!extension || !allowedTypes.includes(extension)) {
    return { 
      isValid: false, 
      error: `File type .${extension} is not allowed. Allowed types: ${allowedTypes.join(', ')}` 
    };
  }
  
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    return { 
      isValid: false, 
      error: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${maxSizeInMB}MB)` 
    };
  }
  
  return { isValid: true };
};

/**
 * Formats file size in bytes to human-readable format.
 * @param bytes File size in bytes.
 * @param decimals Number of decimal places (default: 2).
 * @returns Formatted file size string.
 */
export const formatFileSize = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
};

/**
 * Converts a File object to base64 string.
 * @param file The file to convert.
 * @returns Promise that resolves to base64 string.
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * Creates an AttachedFile object from a File.
 * @param file The File object.
 * @param id Optional ID for the attached file.
 * @returns Promise that resolves to AttachedFile object.
 */
export const createAttachedFile = async (
  file: File,
  id?: string
): Promise<AttachedFile> => {
  const content = await fileToBase64(file);
  
  return {
    id: id || generateFileId(),
    name: file.name,
    size: file.size,
    type: file.type,
    content,
    uploadDate: new Date()
  };
};

/**
 * Generates a unique ID for a file.
 * @returns A unique file ID.
 */
export const generateFileId = (): string => {
  return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Gets the file extension from a filename.
 * @param filename The filename.
 * @returns The file extension (without dot) or empty string if no extension.
 */
export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

/**
 * Gets the MIME type for a file extension.
 * @param extension The file extension (without dot).
 * @returns The MIME type or 'application/octet-stream' if unknown.
 */
export const getMimeType = (extension: string): string => {
  const mimeTypes: Record<string, string> = {
    // Documents
    pdf: 'application/pdf',
    txt: 'text/plain',
    
    // Microsoft Office
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    
    // Project Management
    mpp: 'application/vnd.ms-project',
    mppx: 'application/vnd.ms-project',
    
    // Images
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    
    // Archives
    zip: 'application/zip',
    rar: 'application/vnd.rar',
    '7z': 'application/x-7z-compressed',
    
    // Email
    msg: 'application/vnd.ms-outlook'
  };
  
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * Checks if a file type is an image.
 * @param extension The file extension (without dot).
 * @returns True if the file type is an image.
 */
export const isImageFile = (extension: string): boolean => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  return imageExtensions.includes(extension.toLowerCase());
};

/**
 * Checks if a file type is a document.
 * @param extension The file extension (without dot).
 * @returns True if the file type is a document.
 */
export const isDocumentFile = (extension: string): boolean => {
  const documentExtensions = ['pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  return documentExtensions.includes(extension.toLowerCase());
};

/**
 * Checks if a file type is an archive.
 * @param extension The file extension (without dot).
 * @returns True if the file type is an archive.
 */
export const isArchiveFile = (extension: string): boolean => {
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz'];
  return archiveExtensions.includes(extension.toLowerCase());
};
