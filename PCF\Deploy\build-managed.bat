@echo off

setlocal

:: Get path for current running script.
set RUN_MSBUILD_SCRIPT_PATH=%~dp0

set BUILD_VERSION=*******
if /i not "%~1" == "" (
  set BUILD_VERSION=%~1
)

set VS_BUILD_ARGS=/t:build /restore /p:Configuration=Release /p:BuildVersion=%BUILD_VERSION% /p:PackageType=Managed

for /F "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -nologo -latest -property installationPath`) do (
  set INSTALL_PATH=%%i
)

set MSBUILD_PATH="%INSTALL_PATH%\MSBuild\Current\Bin\MSBuild.exe"
if exist %MSBUILD_PATH% (
  goto :execute
)
set MSBUILD_PATH="%INSTALL_PATH%\MSBuild\Current\Bin\amd64\MSBuild.exe"
if exist %MSBUILD_PATH% (
  goto :execute
)
for /F "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -property installationVersion`) do (
  set VS_BUILD=%%i
)
set MSBUILD_PATH="%INSTALL_PATH%\MSBuild\%VS_BUILD:~0,2%.0\Bin\MSBuild.exe"
if exist %MSBUILD_PATH% (
  goto :execute
)

:execute
call %MSBUILD_PATH% %VS_BUILD_ARGS%

@echo on
