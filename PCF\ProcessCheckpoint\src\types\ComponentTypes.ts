import type { SupportedLanguage } from "../../../SharedLibraries/utils/LocalizationUtils";
import type { BpfActionHandlers, BpfButtonConfig } from "./BpfTypes";
import type { AttachedFile } from "./FileTypes";
import type { LocalizationStrings } from "./LocalizationTypes";
import type { IBpfRepository, ITimelineRepository } from "./RepositoryTypes";

export interface ProcessCheckpointProps {
  sourceControl: string;
  bpfRepository?: IBpfRepository | null;
  targetStageId?: string;
  language?: SupportedLanguage;
  enableFileAttachment?: boolean;
  allowedFileTypes?: string;
  maxFileSize?: number;
  customLocalizationStrings?: LocalizationStrings;
  requireFileAttachment?: boolean;
  customText?: string; // JSON string for custom localization overrides
}

export interface CommentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  value?: string;
  onChange?: (value: string) => void;
  bpfActionHandlers?: BpfActionHandlers;
  buttonConfig?: BpfButtonConfig;
  isProcessing?: boolean;
  readOnly?: boolean;
  enableFileAttachment?: boolean;
  allowedFileTypes?: string[];
  maxFileSize?: number;
  attachedFiles?: AttachedFile[];
  onFilesChange?: (files: AttachedFile[]) => void;
  timelineRepository?: ITimelineRepository | null;
  strings: LocalizationStrings;
}
