import type { CSSProperties } from "react";
import { UI_COLORS, UI_CURSORS, UI_DIMENSIONS, UI_TYPOGRAPHY } from "../../types";

export const inputStyle: CSSProperties = {
  width: "100%",
  height: "100%",
  minHeight: UI_DIMENSIONS.MIN_INPUT_HEIGHT,
  cursor: UI_CURSORS.POINTER,
  backgroundColor: UI_COLORS.background.CODE_BLOCK,
  border: "none",
  borderRadius: 0,
  fontSize: UI_TYPOGRAPHY.DEFAULT_FONT_SIZE,
  fontFamily: UI_TYPOGRAPHY.DEFAULT_FONT_FAMILY,
  padding: "1px 0px",
};
