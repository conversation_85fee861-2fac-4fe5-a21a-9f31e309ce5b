﻿<?xml version="1.0" encoding="utf-8"?>
<ImportExportXml version="9.2.25062.178" SolutionPackageVersion="9.2" languagecode="1033" generatedBy="CrmLive" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" OrganizationVersion="9.2.25062.178" OrganizationSchemaType="Standard" CRMServerServiceabilityVersion="9.2.25063.00176">
  <SolutionManifest>
    <UniqueName>c30seeds_ProcessConfig</UniqueName>
    <LocalizedNames>
      <LocalizedName description="Process Config" languagecode="1033" />
    </LocalizedNames>
    <Descriptions />
    <Version>*******</Version>
    <Managed>0</Managed>
    <Publisher>
      <UniqueName>c30seeds</UniqueName>
      <LocalizedNames>
        <LocalizedName description="30SEEDS SOLUTIONS" languagecode="1033" />
      </LocalizedNames>
      <Descriptions />
      <EMailAddress xsi:nil="true"></EMailAddress>
      <SupportingWebsiteUrl xsi:nil="true"></SupportingWebsiteUrl>
      <CustomizationPrefix>c30seeds</CustomizationPrefix>
      <CustomizationOptionValuePrefix>10000</CustomizationOptionValuePrefix>
      <Addresses>
        <Address>
          <AddressNumber>1</AddressNumber>
          <AddressTypeCode>1</AddressTypeCode>
          <City xsi:nil="true"></City>
          <County xsi:nil="true"></County>
          <Country>Viet Nam</Country>
          <Fax xsi:nil="true"></Fax>
          <FreightTermsCode xsi:nil="true"></FreightTermsCode>
          <ImportSequenceNumber xsi:nil="true"></ImportSequenceNumber>
          <Latitude xsi:nil="true"></Latitude>
          <Line1 xsi:nil="true"></Line1>
          <Line2 xsi:nil="true"></Line2>
          <Line3 xsi:nil="true"></Line3>
          <Longitude xsi:nil="true"></Longitude>
          <Name xsi:nil="true"></Name>
          <PostalCode xsi:nil="true"></PostalCode>
          <PostOfficeBox xsi:nil="true"></PostOfficeBox>
          <PrimaryContactName xsi:nil="true"></PrimaryContactName>
          <ShippingMethodCode>1</ShippingMethodCode>
          <StateOrProvince>Ho Chi Minh</StateOrProvince>
          <Telephone1 xsi:nil="true"></Telephone1>
          <Telephone2 xsi:nil="true"></Telephone2>
          <Telephone3 xsi:nil="true"></Telephone3>
          <TimeZoneRuleVersionNumber xsi:nil="true"></TimeZoneRuleVersionNumber>
          <UPSZone xsi:nil="true"></UPSZone>
          <UTCOffset xsi:nil="true"></UTCOffset>
          <UTCConversionTimeZoneCode xsi:nil="true"></UTCConversionTimeZoneCode>
        </Address>
        <Address>
          <AddressNumber>2</AddressNumber>
          <AddressTypeCode>1</AddressTypeCode>
          <City xsi:nil="true"></City>
          <County xsi:nil="true"></County>
          <Country xsi:nil="true"></Country>
          <Fax xsi:nil="true"></Fax>
          <FreightTermsCode xsi:nil="true"></FreightTermsCode>
          <ImportSequenceNumber xsi:nil="true"></ImportSequenceNumber>
          <Latitude xsi:nil="true"></Latitude>
          <Line1 xsi:nil="true"></Line1>
          <Line2 xsi:nil="true"></Line2>
          <Line3 xsi:nil="true"></Line3>
          <Longitude xsi:nil="true"></Longitude>
          <Name xsi:nil="true"></Name>
          <PostalCode xsi:nil="true"></PostalCode>
          <PostOfficeBox xsi:nil="true"></PostOfficeBox>
          <PrimaryContactName xsi:nil="true"></PrimaryContactName>
          <ShippingMethodCode>1</ShippingMethodCode>
          <StateOrProvince xsi:nil="true"></StateOrProvince>
          <Telephone1 xsi:nil="true"></Telephone1>
          <Telephone2 xsi:nil="true"></Telephone2>
          <Telephone3 xsi:nil="true"></Telephone3>
          <TimeZoneRuleVersionNumber xsi:nil="true"></TimeZoneRuleVersionNumber>
          <UPSZone xsi:nil="true"></UPSZone>
          <UTCOffset xsi:nil="true"></UTCOffset>
          <UTCConversionTimeZoneCode xsi:nil="true"></UTCConversionTimeZoneCode>
        </Address>
      </Addresses>
    </Publisher>
    <RootComponents>
      <RootComponent type="1" schemaName="c30seeds_processconfig" behavior="0" />
      <RootComponent type="1" schemaName="processstage" behavior="0" />
      <RootComponent type="29" id="{f8ab7041-266a-471a-9fce-eee537a1fff6}" behavior="0" />
      <RootComponent type="61" schemaName="c30seeds_/processConfig/icons/add.svg" behavior="0" />
      <RootComponent type="61" schemaName="c30seeds_/processConfig/icons/banned.svg" behavior="0" />
      <RootComponent type="61" schemaName="c30seeds_/processConfig/icons/icon.svg" behavior="0" />
      <RootComponent type="61" schemaName="c30seeds_/processConfig/processconfig.js" behavior="0" />
      <RootComponent type="91" id="{f3425d50-7db7-4002-b4f7-4a513041d19e}" schemaName="C30Seeds.Plugin.ProcessConfig, Version=*******, Culture=neutral, PublicKeyToken=e76ccfe895d6f227" behavior="0" />
      <RootComponent type="92" id="{47e62d54-a134-eb11-a813-000d3a085b0f}" behavior="0" />
      <RootComponent type="92" id="{59034ffe-8334-eb11-a813-000d3a0859e5}" behavior="0" />
      <RootComponent type="92" id="{87a15eec-8334-eb11-a813-000d3a0859e5}" behavior="0" />
    </RootComponents>
    <MissingDependencies>
      <MissingDependency>
        <Required type="66" schemaName="MscrmControls.FieldControls.ToggleControl" displayName="MscrmControls.FieldControls.ToggleControl" solution="BaseCustomControlsCore (9.0.2506.2009)">
          <package appName="Base Custom Controls" version="9.0.2506.2009">BaseCustomControlsCore (9.0.2506.2009)</package>
        </Required>
        <Dependent type="60" displayName="Information" parentDisplayName="Process Config" id="{24d7f159-3446-44db-9b0b-fae1b6abb8fd}" />
      </MissingDependency>
      <MissingDependency>
        <Required type="66" schemaName="zlb_LokBiz.Controls.ActionButton" displayName="zlb_LokBiz.Controls.ActionButton" solution="zlb_PCFControls (0.1.3.0)" />
        <Dependent type="60" displayName="Information" parentDisplayName="Process Config" id="{24d7f159-3446-44db-9b0b-fae1b6abb8fd}" />
      </MissingDependency>
      <MissingDependency>
        <Required type="66" schemaName="zlb_LokBiz.Controls.ComboboxControl" displayName="zlb_LokBiz.Controls.ComboboxControl" solution="zlb_PCFControls (0.1.3.0)" />
        <Dependent type="60" displayName="Information" parentDisplayName="Process Config" id="{24d7f159-3446-44db-9b0b-fae1b6abb8fd}" />
      </MissingDependency>
    </MissingDependencies>
  </SolutionManifest>
</ImportExportXml>