# Hướng Dẫn Sử Dụng PCF Rules

**Mục đích**: Hướng dẫn chi tiết cách sử dụng hệ thống PCF Rules để phát triển PCF controls hiệu quả.

**Phạm vi**: Tất cả developers làm việc với PCF controls trong dự án.

---

## 📚 **Tổng Quan Hệ Thống Rules**

### **Cấu Trúc Files Rules**

```text
rules/
├── PCF-QuickRef.md           # 🚀 Workflow hàng ngày (dùng nhiều nhất)
├── PCF-SmartDetection.md     # 🔍 Auto-detect terminal & package manager
├── PCF-DirectoryRules.md     # 📁 Navigation & file placement
├── PCF-ComplianceRules.md    # ✅ Zero-tolerance checks
├── PCF-BuildRules.md         # 🏗️ Build & quality assurance
└── PCF-CodingRules.md        # 💻 Coding standards & patterns
```

### **Mối Quan Hệ và Dependencies Giữa Các Rules**

```mermaid
graph TD
    H[HUONG-DAN-PCF-RULES.md<br/>📚 Master Guide] --> A[PCF-QuickRef.md<br/>🚀 Daily Entry Point]

    A --> B[PCF-SmartDetection.md<br/>🔍 Environment Foundation]
    A --> C[PCF-DirectoryRules.md<br/>📁 Navigation Foundation]
    A --> D[PCF-ComplianceRules.md<br/>✅ Zero-Tolerance Checks]
    A --> E[PCF-BuildRules.md<br/>🏗️ Build Verification]
    A --> F[PCF-ProjectAnalysis.md<br/>📊 Project Discovery]

    B --> C
    B --> E
    C --> G[PCF-CodingRules.md<br/>💻 Development Standards]
    D --> E
    G --> D
    F --> D
    F --> E
    F --> G

    style H fill:#e1f5fe
    style A fill:#f3e5f5
    style B fill:#fff3e0
    style C fill:#fff3e0
```

### **🔗 Chi Tiết Mối Quan Hệ**

#### **📊 Phân Cấp Dependencies**

**Level 1: Entry Points**

- **`HUONG-DAN-PCF-RULES.md`** - Master guide, điều hướng tất cả scenarios
- **`PCF-QuickRef.md`** - Main entry point cho daily workflow

**Level 2: Foundation (Prerequisites)**

- **`PCF-SmartDetection.md`** - Environment detection (required by ALL)
- **`PCF-DirectoryRules.md`** - Navigation setup (required by ALL)

**Level 3: Core Operations**

- **`PCF-ComplianceRules.md`** - Code quality enforcement
- **`PCF-BuildRules.md`** - Build and verification
- **`PCF-CodingRules.md`** - Development standards

**Level 4: Specialized Tools**

- **`PCF-ProjectAnalysis.md`** - Comprehensive project analysis

#### **🔄 Cross-References Map**

**PCF-QuickRef.md → Other Files:**

```markdown
- Directory issues → PCF-DirectoryRules.md
- Compliance violations → PCF-ComplianceRules.md
- Build failures → PCF-BuildRules.md
- Environment issues → PCF-SmartDetection.md
- Code patterns → PCF-CodingRules.md
```

**PCF-SmartDetection.md → Used By:**

```markdown
- PCF-QuickRef.md (simplified detection)
- PCF-DirectoryRules.md (auto-detection scripts)
- PCF-BuildRules.md (package manager detection)
- PCF-ProjectAnalysis.md (environment discovery)
```

**PCF-DirectoryRules.md → Integration:**

```markdown
- After navigation → PCF-QuickRef.md (verify setup)
- Coding issues → PCF-CodingRules.md
- Uses detection from → PCF-SmartDetection.md
```

**PCF-ComplianceRules.md → Flow:**

```markdown
- After fixing violations → PCF-BuildRules.md
- For coding standards → PCF-CodingRules.md
- Called from → PCF-QuickRef.md, PCF-ProjectAnalysis.md
```

**PCF-BuildRules.md → Dependencies:**

```markdown
- Package manager detection → PCF-SmartDetection.md
- Code quality issues → PCF-CodingRules.md
- Prerequisites → PCF-DirectoryRules.md
```

**PCF-CodingRules.md → Bidirectional:**

```markdown
- Compliance checking → PCF-ComplianceRules.md
- Build verification → PCF-BuildRules.md
- Referenced by → All development workflows
```

**PCF-ProjectAnalysis.md → Output References:**

```markdown
- Violations found → PCF-ComplianceRules.md
- Build issues → PCF-BuildRules.md
- Development work → PCF-CodingRules.md
- Daily workflow → PCF-QuickRef.md
```

### **🧩 Shared Components Across Rules**

#### **Common Scripts & Functions**

**1. Package Manager Detection** - Shared Implementation:

```markdown
📍 Master: PCF-SmartDetection.md (complete enhanced detection)
📍 Used by:
  - PCF-QuickRef.md (simplified version)
  - PCF-BuildRules.md (build-specific)
  - PCF-ProjectAnalysis.md (analysis version)
  - PCF-DirectoryRules.md (auto-detection scripts)
```

**2. Terminal Detection** - Cross-Platform Support:

```markdown
📍 Master: PCF-SmartDetection.md (detailed PowerShell/Bash)
📍 Used by:
  - PCF-DirectoryRules.md (navigation commands)
  - PCF-QuickRef.md (quick environment check)
  - All rule files (command syntax selection)
```

**3. Compliance Checks** - Standardized Validation:

```markdown
📍 Master: PCF-ComplianceRules.md (detailed rules & fixes)
📍 Referenced by:
  - PCF-QuickRef.md (quick compliance checks)
  - PCF-ProjectAnalysis.md (compliance scoring)
  - Post-refactor workflows (verification)
```

**4. File Navigation** - Consistent Path Handling:

```markdown
📍 Master: PCF-DirectoryRules.md (navigation & verification)
📍 Required by: ALL other rules (prerequisite)
📍 Ensures: Correct working directory for all operations
```

#### **🔄 Workflow Integration Patterns**

**Sequential Workflow (Must Follow Order):**

```text
1. PCF-SmartDetection.md → Environment setup
2. PCF-DirectoryRules.md → Navigation & verification
3. PCF-QuickRef.md → Daily workflow execution
4. (If issues) → Specific rule file → Back to QuickRef
```

**Parallel Usage (Can Use Independently):**

```text
- PCF-CodingRules.md → Throughout development
- PCF-ProjectAnalysis.md → Independent analysis tool
- HUONG-DAN-PCF-RULES.md → Reference guide anytime
```

**Conditional Branching (Based on Issues):**

```text
PCF-QuickRef.md detects issue type:
├── Directory issues → PCF-DirectoryRules.md
├── Compliance violations → PCF-ComplianceRules.md
├── Build failures → PCF-BuildRules.md
├── Environment problems → PCF-SmartDetection.md
└── Code quality → PCF-CodingRules.md
```

#### **📋 Shared Variables & Standards**

**Environment Variables (Set by PCF-SmartDetection.md):**

```powershell
$PKG_MGR = "pnpm|yarn|npm"           # Used by all build operations
$BUILD_CMD = "pnpm build|yarn build|npm run build"  # Used by build rules
$TERMINAL = "PowerShell|Bash"        # Used for command syntax
$PCF_FOLDER = "path\to\pcf\control"  # Used by all file operations
```

**Common Error Handling Pattern (All Files):**

```powershell
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ SUCCESS" -ForegroundColor Green
} else {
    Write-Host "❌ FAILED" -ForegroundColor Red
    exit 1
}
```

**Compliance Check Pattern (Standardized):**

```powershell
$violations = @(
    (Select-String '"[A-Za-z]' src\*.tsx -Recurse).Count,
    (Select-String "ReactDOM" src\*.tsx -Recurse).Count,
    # ... other checks
)
$total = ($violations | Measure-Object -Sum).Sum
```

---

## 🎯 **Kịch Bản Sử Dụng Chi Tiết**

### **🚀 Kịch Bản 1: Workflow Hàng Ngày**

**Khi nào dùng**: Mỗi khi bắt đầu làm việc với PCF project

**File sử dụng**: `PCF-QuickRef.md`

**Các bước thực hiện**:

1. **Mở terminal** (PowerShell hoặc Bash)
2. **Navigate đến repository root**
3. **Chạy 3-step workflow**:

   ```powershell
   # PowerShell
   Get-ChildItem -Recurse -Name "ControlManifest.Input.xml"
   Set-Location "path\to\pcf\folder"
   # Chạy compliance checks
   # Chạy build
   ```

**Kết quả mong đợi**:

- ✅ Tìm được PCF folder
- ✅ 0 violations trong compliance checks
- ✅ Build thành công

**Khi nào cần chuyển sang rule khác**:

- Nếu không tìm được PCF folder → `PCF-DirectoryRules.md`
- Nếu có violations → `PCF-ComplianceRules.md`
- Nếu build failed → `PCF-BuildRules.md`

### **🔍 Kịch Bản 2: Lần Đầu Setup Environment**

**Khi nào dùng**:

- Lần đầu clone repository
- Chuyển máy mới
- Có vấn đề với terminal commands

**File sử dụng**: `PCF-SmartDetection.md`

**Các bước thực hiện**:

1. **Xác định terminal type**:

   ```powershell
   # Kiểm tra PowerShell
   $PSVersionTable
   ```

   ```bash
   # Kiểm tra Bash
   echo $BASH_VERSION
   ```

2. **Chạy master detection script**:
   - Copy script từ `PCF-SmartDetection.md`
   - Chạy script phù hợp với terminal

3. **Verify environment variables được set**

**Kết quả mong đợi**:

- ✅ Terminal type detected
- ✅ Package manager detected
- ✅ PCF folder located
- ✅ Environment variables set

### **📁 Kịch Bản 3: Vấn Đề Navigation & File Placement**

**Khi nào dùng**:

- Không tìm được ControlManifest.Input.xml
- Tạo file sai thư mục
- Sai đường dẫn SharedLibraries
- Lỗi "Wrong working directory"

**File sử dụng**: `PCF-DirectoryRules.md`

**Triệu chứng thường gặp**:

```text
❌ ControlManifest.Input.xml not found
❌ SharedLibraries path not found
❌ Creating files in wrong location
❌ Import path errors
```

**Các bước khắc phục**:

1. **Chạy detection script** từ `PCF-DirectoryRules.md`
2. **Verify project structure**:

   ```text
   PCFControl/
   ├── ControlManifest.Input.xml  ← MUST exist
   ├── package.json
   ├── src/
   └── SharedLibraries/
   ```

3. **Fix import paths** nếu cần:

   ```typescript
   // ✅ CORRECT
   import { Entity } from '../../../SharedLibraries/types/CoreTypes';
   ```

**Khi nào chuyển sang rule khác**:

- Sau khi fix navigation → `PCF-QuickRef.md` để verify
- Nếu có coding issues → `PCF-CodingRules.md`

### **✅ Kịch Bản 4: Compliance Violations**

**Khi nào dùng**:

- Compliance checks trả về > 0 violations
- ESLint errors
- TypeScript errors
- Code review feedback

**File sử dụng**: `PCF-ComplianceRules.md`

**Các loại violations thường gặp**:

1. **Hard-coded strings**:

   ```typescript
   // ❌ Violation
   const message = "Loading data...";
   
   // ✅ Fix
   const message = res.ui.messages.loading;
   ```

2. **ReactDOM usage**:

   ```typescript
   // ❌ Violation
   import ReactDOM from 'react-dom';
   
   // ✅ Fix - Remove ReactDOM
   ```

3. **Missing React import**:

   ```typescript
   // ❌ Violation - Missing import
   
   // ✅ Fix
   import * as React from 'react';
   ```

4. **Wrong icon imports**:

   ```typescript
   // ❌ Violation
   import { SearchIcon } from '@fluentui/react-icons';
   
   // ✅ Fix
   import { SearchIcon } from '../../../SharedLibraries/icons/SearchIcon';
   ```

**Quy trình fix violations**:

1. **Chạy compliance check** để identify violations
2. **Fix từng violation** theo hướng dẫn trong file
3. **Re-run compliance check** cho đến khi 0 violations
4. **Chuyển sang build verification**

### **🏗️ Kịch Bản 5: Build Failures & Quality Issues**

**Khi nào dùng**:

- `npm run build` failed
- Bundle size quá lớn
- TypeScript compilation errors
- Performance issues

**File sử dụng**: `PCF-BuildRules.md`

**Các loại build failures thường gặp**:

1. **Package manager mismatch**:

   ```bash
   # ❌ Wrong
   npm run build  # Khi project dùng pnpm
   
   # ✅ Correct
   pnpm build     # Auto-detected từ pnpm-lock.yaml
   ```

2. **TypeScript errors**:

   ```powershell
   # Check TypeScript errors
   npx tsc --noEmit
   ```

3. **Bundle size quá lớn**:
   - Check import statements
   - Verify tree-shaking
   - Remove unused code

**Quy trình troubleshooting**:

1. **Run smart build script** từ file
2. **Check TypeScript compilation**
3. **Verify package manager detection**
4. **Clean up dead code** nếu cần
5. **Re-run build verification**

### **💻 Kịch Bản 6: Coding Standards & Patterns**

**Khi nào dùng**:

- Viết component mới
- Refactor existing code
- Code review preparation
- Performance optimization

**File sử dụng**: `PCF-CodingRules.md`

**Các pattern thường dùng**:

1. **Component template**:

   ```typescript
   import * as React from 'react';
   import { makeStyles, tokens } from '@fluentui/react-components';
   import { res } from '../strings/en';
   
   const useStyles = makeStyles({
       root: {
           padding: tokens.spacingVerticalM,
       },
   });
   
   export const MyComponent: React.FC<IProps> = (props) => {
       const styles = useStyles();
       return <div className={styles.root}>...</div>;
   };
   ```

2. **SharedLibraries integration**:

   ```typescript
   // API calls
   import { retrieveRecord } from '../../../SharedLibraries/utils/XrmUtility';
   
   // Types
   import type { Entity } from '../../../SharedLibraries/types/CoreTypes';
   
   // Constants
   import { UI_CONSTANTS } from '../../../SharedLibraries/types/Constants';
   ```

**Best practices checklist**:

- ✅ makeStyles for all styling
- ✅ No hard-coded strings
- ✅ Proper error handling
- ✅ Performance optimization
- ✅ TypeScript strict typing

### **🔄 Kịch Bản 7: Post-Refactor Workflow**

**Khi nào dùng**:

- Sau khi agent refactor code
- Sau khi merge pull request
- Sau khi update dependencies
- Sau khi apply new rules

**File sử dụng**: Tất cả rule files theo thứ tự

**Quy trình Post-Refactor**:

1. **Immediate Verification** (PCF-QuickRef.md):

   ```powershell
   # Chạy complete smart check ngay sau refactor
   Write-Host "=== Post-Refactor Verification ===" -ForegroundColor Yellow
   # Sử dụng smart complete check từ PCF-QuickRef.md
   ```

2. **Deep Compliance Check** (PCF-ComplianceRules.md):

   ```powershell
   # Kiểm tra chi tiết không có violations mới
   Write-Host "=== Deep Compliance Check ===" -ForegroundColor Yellow
   $violations = @(
       (Select-String '"[A-Za-z]' src\*.tsx -Recurse -ErrorAction SilentlyContinue).Count,
       (Select-String "ReactDOM" src\*.tsx -Recurse -ErrorAction SilentlyContinue).Count,
       (Select-String "@fluentui/react-icons" src\*.tsx -Recurse -ErrorAction SilentlyContinue).Count
   )
   $total = ($violations | Measure-Object -Sum).Sum
   if ($total -eq 0) { Write-Host "✅ No new violations" -ForegroundColor Green }
   else { Write-Host "❌ $total violations found" -ForegroundColor Red }
   ```

3. **Functional Testing**:

   ```powershell
   # Test build và basic functionality
   Write-Host "=== Functional Test ===" -ForegroundColor Yellow
   if (Test-Path "pnpm-lock.yaml") { pnpm build }
   elseif (Test-Path "yarn.lock") { yarn build }
   else { npm run build }

   if ($LASTEXITCODE -eq 0) {
       Write-Host "✅ Build successful - Ready for testing" -ForegroundColor Green
   } else {
       Write-Host "❌ Build failed - Need investigation" -ForegroundColor Red
   }
   ```

**Kết quả mong đợi**:

- ✅ All compliance checks pass
- ✅ No new violations introduced
- ✅ Build successful
- ✅ Functionality preserved

### **📊 Kịch Bản 8: Project Analysis & Tech Stack Discovery**

**Khi nào dùng**:

- Nhận project mới
- Onboarding vào existing codebase
- Technical due diligence
- Architecture review

**File sử dụng**: `PCF-ProjectAnalysis.md` (sẽ tạo riêng)

**Workflow Analysis**:

1. **Environment Discovery**:

   ```powershell
   Write-Host "=== Project Analysis Started ===" -ForegroundColor Yellow

   # Basic project structure
   Get-ChildItem -Name | Sort-Object

   # Package manager detection
   if (Test-Path "pnpm-lock.yaml") { Write-Host "📦 Package Manager: pnpm" }
   elseif (Test-Path "yarn.lock") { Write-Host "📦 Package Manager: yarn" }
   elseif (Test-Path "package-lock.json") { Write-Host "📦 Package Manager: npm" }

   # PCF detection
   $pcfManifests = Get-ChildItem -Recurse -Name "ControlManifest.Input.xml"
   Write-Host "🎛️ PCF Controls found: $($pcfManifests.Count)"
   ```

2. **Tech Stack Analysis**:

   ```powershell
   # Dependencies analysis từ package.json
   if (Test-Path "package.json") {
       $packageJson = Get-Content "package.json" | ConvertFrom-Json
       Write-Host "📋 Project: $($packageJson.name)"
       Write-Host "📝 Version: $($packageJson.version)"
       # Analyze dependencies...
   }
   ```

3. **Code Structure Analysis**:

   ```powershell
   # Source code analysis
   $tsFiles = Get-ChildItem -Recurse -Include "*.ts", "*.tsx" | Where-Object { $_.FullName -notmatch "node_modules" }
   Write-Host "📄 TypeScript files: $($tsFiles.Count)"

   $componentFiles = Get-ChildItem -Recurse -Include "*.tsx"
   Write-Host "🧩 React components: $($componentFiles.Count)"
   ```

**Output**: Tạo file báo cáo `PROJECT-ANALYSIS-REPORT.md`

### 🔄 Kịch Bản 7: Post-Refactor Workflow

**Khi nào dùng**:

- Sau khi agent refactor code
- Sau khi merge pull request
- Sau khi update dependencies
- Sau khi apply new rules

**File sử dụng**: Tất cả rule files theo thứ tự

**Quy trình Post-Refactor**:

1. **Immediate Verification** (PCF-QuickRef.md):

   ```powershell
   # Chạy complete smart check
   # Copy script từ PCF-QuickRef.md
   Write-Host "=== Post-Refactor Verification ===" -ForegroundColor Yellow
   # ... (full smart check script)
   ```

2. **Deep Compliance Check** (PCF-ComplianceRules.md):

   ```powershell
   # Detailed compliance verification
   Write-Host "=== Deep Compliance Check ===" -ForegroundColor Yellow

   # Check for new violations introduced
   $hardcodedStrings = (Select-String '"[A-Za-z]' src\*.tsx -Recurse).Count
   $reactDOMUsage = (Select-String "ReactDOM" src\*.tsx -Recurse).Count
   $fluentIcons = (Select-String "@fluentui/react-icons" src\*.tsx -Recurse).Count
   $missingReact = (Get-ChildItem src\*.tsx -Recurse | Where-Object { -not (Get-Content $_.FullName | Select-String "import.*React") }).Count

   Write-Host "Hard-coded strings: $hardcodedStrings" -ForegroundColor $(if($hardcodedStrings -eq 0){'Green'}else{'Red'})
   Write-Host "ReactDOM usage: $reactDOMUsage" -ForegroundColor $(if($reactDOMUsage -eq 0){'Green'}else{'Red'})
   Write-Host "Fluent icons: $fluentIcons" -ForegroundColor $(if($fluentIcons -eq 0){'Green'}else{'Red'})
   Write-Host "Missing React imports: $missingReact" -ForegroundColor $(if($missingReact -eq 0){'Green'}else{'Red'})
   ```

3. **Code Quality Assessment** (PCF-BuildRules.md):

   ```powershell
   # TypeScript compilation
   Write-Host "=== TypeScript Check ===" -ForegroundColor Yellow
   npx tsc --noEmit

   # ESLint check
   Write-Host "=== ESLint Check ===" -ForegroundColor Yellow
   npx eslint src --ext .ts,.tsx

   # Bundle size check
   Write-Host "=== Bundle Size Check ===" -ForegroundColor Yellow
   # Build and check output size
   ```

4. **Architecture Compliance** (PCF-CodingRules.md):

   ```powershell
   # Check SharedLibraries usage
   Write-Host "=== Architecture Check ===" -ForegroundColor Yellow

   # Verify imports from SharedLibraries
   $sharedLibImports = Select-String "from.*SharedLibraries" src\*.tsx -Recurse
   Write-Host "SharedLibraries imports found: $($sharedLibImports.Count)"

   # Check for proper component structure
   $makeStylesUsage = Select-String "makeStyles" src\*.tsx -Recurse
   Write-Host "makeStyles usage: $($makeStylesUsage.Count)"
   ```

5. **Functional Testing**:

   ```powershell
   # Test basic functionality
   Write-Host "=== Functional Test ===" -ForegroundColor Yellow

   # Build and verify no runtime errors
   if (Test-Path "pnpm-lock.yaml") { pnpm build }
   elseif (Test-Path "yarn.lock") { yarn build }
   else { npm run build }

   if ($LASTEXITCODE -eq 0) {
       Write-Host "✅ Build successful - Ready for testing" -ForegroundColor Green
   } else {
       Write-Host "❌ Build failed - Need investigation" -ForegroundColor Red
   }
   ```

**Kết quả mong đợi**:

- ✅ All compliance checks pass
- ✅ No new violations introduced
- ✅ Build successful
- ✅ Architecture standards maintained
- ✅ Functionality preserved

**Khi nào cần action**:

- Nếu có violations → Fix theo PCF-ComplianceRules.md
- Nếu build failed → Troubleshoot theo PCF-BuildRules.md
- Nếu architecture issues → Review theo PCF-CodingRules.md

### 📊 Kịch Bản 8: Project Analysis & Tech Stack Discovery

**Khi nào dùng**:

- Nhận project mới
- Onboarding vào existing codebase
- Technical due diligence
- Architecture review

**File sử dụng**: Tạo mới `PCF-ProjectAnalysis.md` (sẽ tạo sau)

**Workflow Analysis**:

1. **Environment Discovery**:

   ```powershell
   Write-Host "=== Project Analysis Started ===" -ForegroundColor Yellow

   # Basic project structure
   Write-Host "=== Project Structure ===" -ForegroundColor Cyan
   Get-ChildItem -Name | Sort-Object

   # Package manager detection
   if (Test-Path "pnpm-lock.yaml") { Write-Host "📦 Package Manager: pnpm" }
   elseif (Test-Path "yarn.lock") { Write-Host "📦 Package Manager: yarn" }
   elseif (Test-Path "package-lock.json") { Write-Host "📦 Package Manager: npm" }

   # PCF detection
   $pcfManifests = Get-ChildItem -Recurse -Name "ControlManifest.Input.xml"
   Write-Host "🎛️ PCF Controls found: $($pcfManifests.Count)"
   $pcfManifests | ForEach-Object { Write-Host "   - $_" }
   ```

2. **Tech Stack Analysis**:

   ```powershell
   # Dependencies analysis
   Write-Host "=== Tech Stack Analysis ===" -ForegroundColor Cyan

   if (Test-Path "package.json") {
       $packageJson = Get-Content "package.json" | ConvertFrom-Json

       Write-Host "📋 Project: $($packageJson.name)"
       Write-Host "📝 Version: $($packageJson.version)"
       Write-Host "📄 Description: $($packageJson.description)"

       # Dependencies
       if ($packageJson.dependencies) {
           Write-Host "🔗 Dependencies:"
           $packageJson.dependencies.PSObject.Properties | ForEach-Object {
               Write-Host "   - $($_.Name): $($_.Value)"
           }
       }

       # Dev Dependencies
       if ($packageJson.devDependencies) {
           Write-Host "🛠️ Dev Dependencies:"
           $packageJson.devDependencies.PSObject.Properties | ForEach-Object {
               Write-Host "   - $($_.Name): $($_.Value)"
           }
       }
   }
   ```

3. **Code Structure Analysis**:

   ```powershell
   # Source code analysis
   Write-Host "=== Code Structure Analysis ===" -ForegroundColor Cyan

   # TypeScript/JavaScript files
   $tsFiles = Get-ChildItem -Recurse -Include "*.ts", "*.tsx" | Where-Object { $_.FullName -notmatch "node_modules" }
   Write-Host "📄 TypeScript files: $($tsFiles.Count)"

   # Components analysis
   $componentFiles = Get-ChildItem -Recurse -Include "*.tsx" | Where-Object { $_.FullName -notmatch "node_modules" }
   Write-Host "🧩 React components: $($componentFiles.Count)"

   # Utility files
   $utilFiles = Get-ChildItem -Recurse -Path "*utils*" -Include "*.ts" | Where-Object { $_.FullName -notmatch "node_modules" }
   Write-Host "🔧 Utility files: $($utilFiles.Count)"

   # SharedLibraries detection
   if (Test-Path "SharedLibraries") {
       Write-Host "📚 SharedLibraries detected"
       $sharedFiles = Get-ChildItem -Recurse -Path "SharedLibraries" -Include "*.ts", "*.tsx"
       Write-Host "   - Shared files: $($sharedFiles.Count)"
   }
   ```

4. **Architecture Pattern Detection**:

   ```powershell
   # Architecture patterns
   Write-Host "=== Architecture Patterns ===" -ForegroundColor Cyan

   # PCF specific patterns
   $fluentUIUsage = Select-String "@fluentui/react-components" $tsFiles.FullName
   if ($fluentUIUsage) {
       Write-Host "🎨 UI Framework: Fluent UI React"
       Write-Host "   - Usage count: $($fluentUIUsage.Count)"
   }

   # State management
   $reactHooks = Select-String "useState|useEffect|useCallback|useMemo" $componentFiles.FullName
   if ($reactHooks) {
       Write-Host "⚡ State Management: React Hooks"
       Write-Host "   - Hook usage: $($reactHooks.Count)"
   }

   # API patterns
   $xrmUsage = Select-String "Xrm\." $tsFiles.FullName
   if ($xrmUsage) {
       Write-Host "🌐 API Integration: Dynamics 365 Xrm"
       Write-Host "   - Xrm calls: $($xrmUsage.Count)"
   }
   ```

**Output**: Tạo file báo cáo `PROJECT-ANALYSIS-REPORT.md` (template sẽ tạo sau)

---

## 🔄 **Workflow Tổng Hợp**

### **Quy Trình Hoàn Chỉnh Cho Mỗi Task**

```mermaid
flowchart TD
    A[Bắt đầu task] --> A1{Task type?}

    A1 -->|New project| B1[PCF-ProjectAnalysis.md<br/>Complete analysis]
    A1 -->|Existing project| B[PCF-QuickRef.md<br/>3-step workflow]
    A1 -->|Post-refactor| B2[Post-Refactor Workflow]

    B1 --> B1A[Generate analysis report]
    B1A --> B1B{Issues found?}
    B1B -->|Yes| E
    B1B -->|No| D

    B2 --> B2A[Immediate verification]
    B2A --> B2B[Deep compliance check]
    B2B --> B2C[Functional testing]
    B2C --> B2D{All pass?}
    B2D -->|Yes| O
    B2D -->|No| E

    B --> C{All checks pass?}
    C -->|Yes| D[Proceed with development]
    C -->|No| E{What failed?}

    E -->|Directory issues| F[PCF-DirectoryRules.md]
    E -->|Compliance violations| G[PCF-ComplianceRules.md]
    E -->|Build failures| H[PCF-BuildRules.md]
    E -->|Environment issues| I[PCF-SmartDetection.md]

    F --> J[Fix issues]
    G --> J
    H --> J
    I --> J

    J --> B

    D --> K[Follow PCF-CodingRules.md]
    K --> L[Final verification]
    L --> M[PCF-QuickRef.md<br/>Complete check]
    M --> N{All pass?}
    N -->|Yes| O[Task complete]
    N -->|No| E
```

### **🎯 Rule Selection Decision Tree**

```mermaid
flowchart TD
    START[Bắt đầu với vấn đề] --> TYPE{Loại vấn đề?}

    TYPE -->|Lần đầu setup| ENV[PCF-SmartDetection.md<br/>🔍 Environment Detection]
    TYPE -->|Không tìm được file| NAV[PCF-DirectoryRules.md<br/>📁 Navigation & File Placement]
    TYPE -->|Code violations| COMP[PCF-ComplianceRules.md<br/>✅ Zero-Tolerance Checks]
    TYPE -->|Build failed| BUILD[PCF-BuildRules.md<br/>🏗️ Build & Verification]
    TYPE -->|Code standards| CODE[PCF-CodingRules.md<br/>💻 Development Standards]
    TYPE -->|New project| ANALYSIS[PCF-ProjectAnalysis.md<br/>📊 Project Discovery]
    TYPE -->|Daily work| QUICK[PCF-QuickRef.md<br/>🚀 3-Step Workflow]

    ENV --> QUICK
    NAV --> QUICK
    COMP --> BUILD
    BUILD --> QUICK
    CODE --> COMP
    ANALYSIS --> COMP
    QUICK --> DONE[✅ Task Complete]

    style START fill:#e3f2fd
    style DONE fill:#e8f5e8
```

### **📋 Thứ Tự Ưu Tiên Khi Có Lỗi**

**Priority 1: Foundation Issues (Must Fix First)**

1. **🔍 Environment issues** → `PCF-SmartDetection.md`
   - Terminal detection failed
   - Package manager not found
   - Command syntax errors

2. **📁 Directory/Navigation** → `PCF-DirectoryRules.md`
   - ControlManifest.Input.xml not found
   - Wrong working directory
   - File placement errors

**Priority 2: Code Quality Issues**
3. **✅ Compliance violations** → `PCF-ComplianceRules.md`

- Hard-coded strings
- ReactDOM usage
- Missing imports
- Forbidden patterns

4. **🏗️ Build failures** → `PCF-BuildRules.md`
   - TypeScript errors
   - Package manager issues
   - Bundle size problems

**Priority 3: Development Standards**
5. **💻 Code quality** → `PCF-CodingRules.md`

- Component structure
- Styling patterns
- Architecture compliance

### **🔗 Rule Dependencies Matrix**

| Rule File | Prerequisites | Triggers | Outputs To |
|-----------|---------------|----------|------------|
| **PCF-SmartDetection.md** | None | Environment setup | All other rules |
| **PCF-DirectoryRules.md** | SmartDetection | Navigation needed | QuickRef, CodingRules |
| **PCF-QuickRef.md** | SmartDetection, DirectoryRules | Daily workflow | ComplianceRules, BuildRules |
| **PCF-ComplianceRules.md** | DirectoryRules | Violations found | BuildRules, CodingRules |
| **PCF-BuildRules.md** | SmartDetection, DirectoryRules | Build verification | QuickRef |
| **PCF-CodingRules.md** | DirectoryRules | Development work | ComplianceRules |
| **PCF-ProjectAnalysis.md** | SmartDetection | New project | All rules based on findings |

---

## 💡 **Tips & Best Practices**

### **🔗 Rule Linking Best Practices**

#### **Cho Developers**

1. **Luôn bắt đầu với PCF-QuickRef.md** - Entry point cho mọi workflow
2. **Bookmark rule dependencies** - Hiểu rõ rule nào dẫn đến rule nào
3. **Follow sequential workflow** - SmartDetection → DirectoryRules → QuickRef
4. **Use cross-references** - Khi gặp lỗi, follow link đến specific rule
5. **Understand shared components** - Package manager detection, compliance checks

#### **Rule Navigation Patterns**

```markdown
📍 Daily Work: HUONG-DAN-PCF-RULES.md → PCF-QuickRef.md
📍 First Time: HUONG-DAN-PCF-RULES.md → PCF-SmartDetection.md → PCF-QuickRef.md
📍 Troubleshooting: PCF-QuickRef.md → (specific issue) → Relevant rule file
📍 New Project: PCF-ProjectAnalysis.md → (based on findings) → Relevant rules
```

#### **Shared Component Usage**

1. **Package Manager Detection** - Sử dụng enhanced version từ SmartDetection
2. **Terminal Commands** - Always check terminal type trước khi run commands
3. **Compliance Checks** - Consistent pattern across all rules
4. **Error Handling** - Standardized success/failure reporting

### **Cho Team Leads**

#### **Rule System Management**

1. **Enforce rule dependencies** - Ensure team follows correct sequence
2. **Monitor cross-references** - Update links when rules change
3. **Standardize shared components** - Ensure consistent usage across team
4. **Track rule effectiveness** - Monitor which rules solve which problems
5. **Maintain rule documentation** - Keep cross-references updated

#### **Training & Onboarding**

```markdown
📚 Level 1: HUONG-DAN-PCF-RULES.md overview
📚 Level 2: PCF-QuickRef.md daily workflow
📚 Level 3: Specific rule files based on role
📚 Level 4: Understanding rule dependencies và cross-references
```

### **Cho DevOps/CI**

#### **Pipeline Integration**

1. **Sequential rule execution** - Follow dependency order in CI
2. **Shared variable management** - Use environment variables from SmartDetection
3. **Cross-platform support** - Implement both PowerShell và Bash versions
4. **Rule result aggregation** - Collect results from multiple rule files
5. **Dependency validation** - Ensure prerequisites met before running rules

#### **Monitoring & Maintenance**

```markdown
🔍 Monitor: Rule execution success rates
🔍 Track: Which rules are most frequently triggered
🔍 Analyze: Rule dependency bottlenecks
🔍 Optimize: Shared component performance
🔍 Update: Cross-references when rules evolve
```

---

## 🚨 **Troubleshooting Common Issues**

### **"ControlManifest.Input.xml not found"**

→ Sử dụng `PCF-DirectoryRules.md` section "Working Directory Verification"

### **"Build failed with wrong package manager"**

→ Sử dụng `PCF-SmartDetection.md` section "Package Manager Detection"

### **"Compliance violations found"**

→ Sử dụng `PCF-ComplianceRules.md` section tương ứng với violation type

### **"Import path errors"**

→ Sử dụng `PCF-CodingRules.md` section "SharedLibraries Integration"

### **"Terminal command syntax errors"**

→ Sử dụng `PCF-SmartDetection.md` section "Command Syntax Rules"

### **"Rule dependencies not clear"**

→ Reference dependency matrix trong `HUONG-DAN-PCF-RULES.md` section "Rule Dependencies Matrix"

### **"Don't know which rule to use"**

→ Sử dụng decision tree trong `HUONG-DAN-PCF-RULES.md` section "Rule Selection Decision Tree"

### **"Shared components not working"**

→ Check shared component documentation trong `HUONG-DAN-PCF-RULES.md` section "Shared Components Across Rules"

---

## 🔗 **Rule Linking Quick Reference**

### **📊 Rule Execution Order**

```mermaid
sequenceDiagram
    participant User
    participant Guide as HUONG-DAN-PCF-RULES.md
    participant Smart as PCF-SmartDetection.md
    participant Dir as PCF-DirectoryRules.md
    participant Quick as PCF-QuickRef.md
    participant Specific as Specific Rule File

    User->>Guide: Start with scenario
    Guide->>Smart: Environment setup
    Smart->>Dir: Navigation setup
    Dir->>Quick: Daily workflow
    Quick->>Specific: If issues found
    Specific->>Quick: After fixing
    Quick->>User: Task complete
```

### **🎯 Rule Selection Cheat Sheet**

| Tình Huống | Rule File | Dependency | Next Step |
|------------|-----------|------------|-----------|
| 🆕 Lần đầu setup | PCF-SmartDetection.md | None | → PCF-DirectoryRules.md |
| 📁 Không tìm được file | PCF-DirectoryRules.md | SmartDetection | → PCF-QuickRef.md |
| 🚀 Daily workflow | PCF-QuickRef.md | SmartDetection + DirectoryRules | → Specific rules if issues |
| ✅ Code violations | PCF-ComplianceRules.md | DirectoryRules | → PCF-BuildRules.md |
| 🏗️ Build failed | PCF-BuildRules.md | SmartDetection + DirectoryRules | → PCF-QuickRef.md |
| 💻 Code standards | PCF-CodingRules.md | DirectoryRules | → PCF-ComplianceRules.md |
| 📊 New project | PCF-ProjectAnalysis.md | SmartDetection | → Based on findings |
| 🔄 After refactor | Post-Refactor Workflow | All rules | → PCF-QuickRef.md |

### **🧩 Shared Component Quick Access**

| Component | Master Location | Used By | Purpose |
|-----------|----------------|---------|---------|
| Package Manager Detection | PCF-SmartDetection.md | All build operations | Auto-detect pnpm/yarn/npm |
| Terminal Detection | PCF-SmartDetection.md | All command operations | PowerShell vs Bash |
| Compliance Checks | PCF-ComplianceRules.md | QuickRef, ProjectAnalysis | Standardized validation |
| File Navigation | PCF-DirectoryRules.md | All file operations | Correct working directory |
| Error Handling | All files | Consistent pattern | Success/failure reporting |

---

## 📞 **Khi Nào Cần Hỗ Trợ**

**Tự giải quyết được**:

- Standard compliance violations
- Common build failures
- Directory navigation issues
- Package manager detection

**Cần hỗ trợ team**:

- New rule requirements
- Complex environment issues
- Performance optimization
- Architecture decisions

**Cần escalate**:

- Rule system bugs
- CI/CD integration issues
- Cross-platform compatibility
- Security concerns

---

## 📋 **Checklists Nhanh**

### **Daily Development Checklist**

```markdown
- [ ] Chạy PCF-QuickRef.md 3-step workflow
- [ ] Verify 0 compliance violations
- [ ] Confirm build success
- [ ] Check import paths đúng
- [ ] Follow coding templates
- [ ] Test functionality
```

### **Before Pull Request Checklist**

```markdown
- [ ] All compliance checks pass (0 violations)
- [ ] Build successful với correct package manager
- [ ] No hard-coded strings
- [ ] Proper error handling implemented
- [ ] Performance optimized
- [ ] Documentation updated
- [ ] Tests written/updated
```

### **Code Review Checklist**

```markdown
- [ ] Follows PCF-CodingRules.md templates
- [ ] Uses SharedLibraries correctly
- [ ] No forbidden patterns (ReactDOM, @fluentui/react-icons)
- [ ] Proper TypeScript typing
- [ ] makeStyles used for styling
- [ ] Localization implemented
- [ ] Error handling present
```

### **Troubleshooting Checklist**

```markdown
- [ ] Environment detected correctly (terminal + package manager)
- [ ] Working directory verified (ControlManifest.Input.xml exists)
- [ ] SharedLibraries path accessible
- [ ] All dependencies installed
- [ ] No TypeScript compilation errors
- [ ] ESLint passes
```

---

## 🎓 **Training Scenarios**

### **Scenario 1: New Developer Onboarding**

**Mục tiêu**: Làm quen với rule system

**Các bước**:

1. Clone repository
2. Chạy `PCF-SmartDetection.md` master script
3. Practice với `PCF-QuickRef.md` workflow
4. Tạo simple component theo `PCF-CodingRules.md`
5. Fix intentional violations trong test code

**Thời gian**: 2-3 giờ

### **Scenario 2: Cross-Platform Development**

**Mục tiêu**: Làm việc trên cả Windows (PowerShell) và Linux/Mac (Bash)

**Các bước**:

1. Test smart detection trên cả 2 platforms
2. Verify command syntax differences
3. Practice package manager detection
4. Ensure consistent results

**Thời gian**: 1 giờ

### **Scenario 3: Legacy Code Refactoring**

**Mục tiêu**: Áp dụng rules cho existing codebase

**Các bước**:

1. Run compliance checks trên legacy code
2. Identify và fix violations systematically
3. Refactor theo coding standards
4. Verify build và functionality

**Thời gian**: 4-6 giờ (depending on codebase size)

### **Scenario 4: Post-Refactor Verification**

**Mục tiêu**: Verify code quality sau khi agent refactor

**Các bước**:

1. Chạy immediate verification với PCF-QuickRef.md
2. Deep compliance check với PCF-ComplianceRules.md
3. Code quality assessment với PCF-BuildRules.md
4. Architecture compliance với PCF-CodingRules.md
5. Functional testing để ensure no regressions

**Thời gian**: 30-45 phút

**Key Points**:

- MANDATORY sau mọi agent refactor
- Focus on detecting new violations
- Ensure functionality preserved
- Verify architecture standards maintained

### **Scenario 5: New Project Analysis**

**Mục tiêu**: Hiểu rõ project structure và tech stack

**Các bước**:

1. Environment discovery (package manager, PCF controls, etc.)
2. Tech stack analysis (dependencies, tools, scripts)
3. Code structure analysis (files, components, utilities)
4. Architecture pattern detection (UI framework, state management)
5. Compliance assessment (violations, score)
6. Generate comprehensive analysis report

**Thời gian**: 15-30 phút

**Output**:

- `PROJECT-ANALYSIS-REPORT.md` với detailed findings
- Compliance score và recommendations
- Next steps guidance

**Key Benefits**:

- Quick understanding của unfamiliar codebase
- Identify potential issues early
- Baseline for improvement planning
- Documentation for team knowledge sharing

---

## 📊 **Metrics & KPIs**

### **Development Efficiency Metrics**

- **Setup Time**: Thời gian từ clone repo đến ready for development
- **Compliance Rate**: % commits pass compliance checks first time
- **Build Success Rate**: % builds succeed without manual intervention
- **Rule Adoption**: % developers sử dụng rule system correctly

### **Code Quality Metrics**

- **Violation Density**: Số violations per 1000 lines of code
- **Fix Time**: Thời gian trung bình để fix violations
- **Regression Rate**: % violations reintroduced after fixing
- **Standard Compliance**: % code follows templates và patterns

### **Team Productivity Metrics**

- **Onboarding Time**: Thời gian new developer productive
- **Code Review Time**: Thời gian trung bình cho code review
- **Bug Rate**: Số bugs related to rule violations
- **Knowledge Sharing**: % team members hiểu và apply rules correctly

---

## 🔮 **Future Enhancements**

### **Planned Improvements**

1. **Automated Rule Enforcement**:
   - Pre-commit hooks
   - CI/CD integration
   - Real-time IDE warnings

2. **Enhanced Detection**:
   - More package managers (bun, etc.)
   - Additional terminal types
   - Cloud development environments

3. **Better Developer Experience**:
   - VS Code extension
   - Interactive tutorials
   - Automated fixes

4. **Advanced Analytics**:
   - Rule usage statistics
   - Performance impact analysis
   - Team productivity insights

### **Feedback & Contributions**

**Cách contribute**:

1. Report issues với specific scenarios
2. Suggest improvements cho existing rules
3. Propose new rules cho emerging patterns
4. Share success stories và lessons learned

**Contact channels**:

- Team chat cho quick questions
- Issue tracker cho bugs/enhancements
- Documentation wiki cho knowledge sharing
- Regular team meetings cho major changes
