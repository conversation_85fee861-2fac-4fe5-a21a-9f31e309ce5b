import { But<PERSON>, Spinner } from "@fluentui/react-components";
import * as React from "react";
import type { LocalizationStrings } from "../types/LocalizationTypes";
import {
  BpfActionType,
  type BpfButtonConfig,
  UI_COLORS,
  UI_DIMENSIONS,
} from "../types";

interface ActionButtonProps {
  actionType: BpfActionType;
  onClick: (action: BpfActionType) => void;
  isProcessing: boolean;
  loadingAction: string | null;
  buttonLabel: string;
  loadingText: string;
  style?: React.CSSProperties;
  appearance?: "primary" | "secondary" | "outline" | "subtle" | "transparent";
}

const ActionButton: React.FC<ActionButtonProps> = ({
  actionType,
  onClick,
  isProcessing,
  loadingAction,
  buttonLabel,
  loadingText,
  style,
  appearance = "primary",
}) => {


  return (
    <Button
      appearance={appearance}
      onClick={() => onClick(actionType)}
      disabled={isProcessing}

      style={style}
    >
      {isProcessing && loadingAction === actionType ? (
        <>
          <Spinner size="tiny" /> {loadingText}
        </>
      ) : (
        buttonLabel
      )}
    </Button>
  );
};

const buttonStyles: Record<string, React.CSSProperties> = {
  [BpfActionType.REJECT]: {
    backgroundColor: UI_COLORS.button.REJECT_BACKGROUND,
    color: UI_COLORS.button.TEXT_WHITE,
    border: UI_DIMENSIONS.NO_BORDER,
  },
  [BpfActionType.ADVISE]: {
    backgroundColor: UI_COLORS.button.APPROVE_BACKGROUND,
    color: UI_COLORS.button.TEXT_WHITE,
    border: UI_DIMENSIONS.NO_BORDER,
  },
  [BpfActionType.APPROVE]: {
    backgroundColor: UI_COLORS.button.APPROVE_BACKGROUND,
    color: UI_COLORS.button.TEXT_WHITE,
    border: UI_DIMENSIONS.NO_BORDER,
  },
};

interface ActionButtonsProps {
  buttonConfig?: BpfButtonConfig;
  isProcessing: boolean;
  onAction: (action: BpfActionType) => void;
  loadingAction: string | null;
  strings: LocalizationStrings;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  buttonConfig,
  isProcessing,
  onAction,
  loadingAction,
  strings,
}) => {
  const actionButtonsConfig = React.useMemo(
    () => [
      {
        actionType: BpfActionType.REJECT,
        show: buttonConfig?.showReject,
        label: strings.ui.buttons.reject,
        loadingText: strings.messages.loading.rejecting,
      },
      {
        actionType: BpfActionType.SUBMIT,
        show: buttonConfig?.showSubmit,
        label: strings.ui.buttons.submit,
        loadingText: strings.messages.loading.submitting,
      },
      {
        actionType: BpfActionType.ADVISE,
        show: buttonConfig?.showAdvise,
        label: strings.ui.buttons.advise,
        loadingText: strings.messages.loading.advising,
      },
      {
        actionType: BpfActionType.APPROVE,
        show: buttonConfig?.showApprove,
        label: strings.ui.buttons.approve,
        loadingText: strings.messages.loading.approving,
      },
    ],
    [buttonConfig, strings],
  );

  return (
    <>
      {actionButtonsConfig.map(
        (btn) =>
          btn.show && (
            <ActionButton
              key={btn.actionType}
              actionType={btn.actionType}
              onClick={onAction}
              isProcessing={isProcessing}
              loadingAction={loadingAction}
              buttonLabel={btn.label}
              loadingText={btn.loadingText}
              style={buttonStyles[btn.actionType]}
            />
          ),
      )}
    </>
  );
};
