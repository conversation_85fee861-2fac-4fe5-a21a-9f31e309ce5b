import * as React from 'react';
import {
    Combobox,
    FluentProvider,
    Option,
    makeStyles,
    webLightTheme,
} from '@fluentui/react-components';
import type {
    ComboboxOpenChangeData,
    ComboboxOpenEvents,
    ComboboxProps,
    OptionOnSelectData,
    SelectionEvents,
} from '@fluentui/react-components';
import {
  createLanguageLoader,
  LANGUAGES
} from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from '../strings/en';
import { vietnameseStrings } from '../strings/vi';
import type { LocalizationStrings } from '../types/LocalizationTypes';

// Create language-aware loader
const languageLoader = createLanguageLoader({
  [LANGUAGES.ENG]: englishStrings,
  [LANGUAGES.VN]: vietnameseStrings
});
import type { IInputs } from '../generated/ManifestTypes';
import { retrieveRecord, fetchEntityDefinitions, type AttributeMetadata, type Entity } from '../../../SharedLibraries/utils/XrmUtility';
import type { ErrorObject } from '../../../SharedLibraries/types/CoreTypes';
import { FieldTypes, FieldTypeConfig } from '../../../SharedLibraries/types/MetadataTypes';
import type { IRuntimeMetadata, FieldType } from '../../../SharedLibraries/types/MetadataTypes';



// Define props for the React component
export interface IComboboxComponentProps {
    context: ComponentFramework.Context<IInputs>;
    value: number | string | null;
    fieldType: FieldType;
    onChange: (newValue: number | string | null) => void;
    disabled?: boolean;
}

// Define a simple interface for the options state
interface ISelectOption {
    key: number | string;
    text: string;
}

// Interface for OptionSet Option from EntityDefinitions API
interface IOptionSetOption {
    Value: number;
    State?: number; // For StatusAttributeMetadata - indicates which state this status belongs to
    Label?: {
        UserLocalizedLabel?: {
            Label: string;
        };
    };
}

// Interface for EntityDefinitions response structure
interface IEntityDefinitionsResponse {
    value: IAttributeMetadataResponse[];
}

interface IAttributeMetadataResponse {
    LogicalName: string;
    OptionSet?: {
        Options: IOptionSetOption[];
    };
}

// Constants
const MAX_ATTRIBUTES = 100;
const CACHE_KEY_SEPARATOR = '_';

// Cache for metadata to avoid repeated API calls
const metadataCache = new Map<string, Record<number | string, IRuntimeMetadata>>();

// Cache utilities
const CacheUtils = {
    getCacheKey: (entityName: string, fieldType: string) => `${entityName}${CACHE_KEY_SEPARATOR}${fieldType}`,

    get: (entityName: string, fieldType: string) => {
        const cacheKey = CacheUtils.getCacheKey(entityName, fieldType);
        return metadataCache.get(cacheKey);
    },

    set: (entityName: string, fieldType: string, data: Record<number | string, IRuntimeMetadata>) => {
        const cacheKey = CacheUtils.getCacheKey(entityName, fieldType);
        metadataCache.set(cacheKey, data);
    },

    clear: (entityName: string, fieldType: string) => {
        const cacheKey = CacheUtils.getCacheKey(entityName, fieldType);
        metadataCache.delete(cacheKey);
    },

    clearAll: () => {
        metadataCache.clear();
    }
};

// Utility functions
const AttributeUtils = {
    isSystemAttribute: (logicalName: string) => {
        // Filter out system attributes that start with underscore
        if (logicalName.startsWith('_')) {
            return true;
        }

        // Specific system attributes to exclude (as requested)
        const excludedAttributes = [
            'createdonbehalfby',
            'exchangerate',
            'importsequencenumber',
            'modifiedonbehalfby',
            'overriddencreatedon',
            'processid',
            'stageid',
            'timezoneruleversionnumber',
            'traversedpath',
            'utcconversiontimezonecode',
            'versionnumber'
        ];

        if (excludedAttributes.includes(logicalName.toLowerCase())) {
            return true;
        }

        // Filter out common system attribute patterns
        const systemPatterns = [
            /yominame$/i,          // Japanese phonetic names (e.g., createdbyyominame)
            /^created(by|on)$/i,    // Created system fields (exact match)
            /^modified(by|on)$/i,   // Modified system fields (exact match)
            /^owner(id)?$/i,        // Owner system fields (exact match)
            /^statecode$/i,         // State code (usually handled separately)
            /^statuscode$/i         // Status code (usually handled separately)
        ];

        return systemPatterns.some(pattern => pattern.test(logicalName));
    },

    isBaseCurrencyAttribute: (attr: AttributeMetadata) => {
        // Filter out Money attributes that end with "_base"
        return attr.AttributeType === 'Money' && attr.LogicalName.endsWith('_base');
    },

    isValidAttribute: (attr: AttributeMetadata) => {
        // Filter out Virtual and complex attribute types
        const excludedAttributeTypes = [
            'Virtual',
            'CalendarRules',
            'ManagedProperty',
            'EntityName',
            'PartyList',
            'Image'
        ];

        if (excludedAttributeTypes.includes(attr.AttributeType)) {
            return false;
        }

        return true;
    },

    sortAttributes: (attributes: IRuntimeMetadata[]) => {
        // Sort attributes by display name for better user experience
        return attributes.sort((a, b) => {
            const nameA = a.displayName.toLowerCase();
            const nameB = b.displayName.toLowerCase();
            return nameA.localeCompare(nameB);
        });
    },

    getDisplayName: (attr: AttributeMetadata) => {
        // Try to get localized display name first
        if (attr.DisplayName?.UserLocalizedLabel?.Label) {
            return attr.DisplayName.UserLocalizedLabel.Label;
        }
        // Fallback to LogicalName if no display name

        // Fallback: format LogicalName nicely
        return attr.LogicalName
            .replace(/([a-z])([A-Z])/g, '$1 $2') // camelCase to spaces
            .replace(/_/g, ' ') // underscores to spaces
            .replace(/\b\w/g, (l: string) => l.toUpperCase()); // capitalize first letters
    }
};

// Expose cache clearing function for debugging (only in development)
if (typeof window !== 'undefined') {
    (window as { clearPCFMetadataCache?: () => void }).clearPCFMetadataCache = CacheUtils.clearAll;
}

const useStyles = makeStyles({
    wrapper: {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        gap: '4px',
        boxSizing: 'border-box',
        // Responsive layout support
        minWidth: 0, // Prevent overflow in flex containers
        flex: 1, // Take available space
    },
    controlContainer: {
        position: 'relative',
        width: '100%',
        minWidth: 0, // Prevent overflow
        '&.loading': {
            animation: 'pulse 1.5s infinite',
        },
    },
    combobox: {
        width: '100%',
        minHeight: '36px',
        minWidth: 0, // Prevent overflow in flex layouts
        maxWidth: '100%', // Ensure it doesn't exceed container

        '& .fui-Combobox__input': {
            cursor: 'text', // Changed from 'pointer' to 'text' for better UX
            minHeight: '36px',
            fontSize: '14px',
            padding: '8px 12px',
            width: '100%',
            boxSizing: 'border-box',
            // Fix for typing issues
            '&:focus': {
                cursor: 'text',
            },
        },
        '& .fui-Combobox__expandIcon': {
            cursor: 'pointer',
            fontSize: '16px',
            color: '#605e5c',
            flexShrink: 0, // Prevent icon from shrinking
            '&:hover': {
                color: '#0078d4',
            },
        },
        '& .fui-Combobox__listbox': {
            maxHeight: '300px',
            overflowY: 'auto',
            border: '1px solid #d1d1d1',
            borderRadius: '4px',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
            minWidth: '100%', // Ensure dropdown is at least as wide as input
        },
    },
    errorText: {
        color: '#d13438',
        fontSize: '12px',
        padding: '4px 0px',
        marginTop: '4px',
        wordBreak: 'break-word', // Handle long error messages
        lineHeight: '1.4',
    },
    // Responsive utilities for form layouts
    responsiveContainer: {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        minWidth: 0,

        // Support for horizontal (left) alignment in forms
        '@media (min-width: 768px)': {
            '&.horizontal-layout': {
                flexDirection: 'row',
                alignItems: 'flex-start',
                gap: '12px',

                '& .field-label': {
                    minWidth: '120px',
                    maxWidth: '200px',
                    flexShrink: 0,
                    paddingTop: '8px', // Align with input
                },

                '& .field-control': {
                    flex: 1,
                    minWidth: 0,
                    maxWidth: 'calc(100% - 220px)', // Account for label width
                },
            },
        },
    },
    '@keyframes pulse': {
        '0%': { backgroundColor: 'transparent' },
        '50%': { backgroundColor: '#f0f0f0' },
        '100%': { backgroundColor: 'transparent' },
    },
});

// Map field types to their corresponding labels for cleaner rendering
const getFieldTypeLabels = (strings: LocalizationStrings): Record<FieldType, string> => ({
    [FieldTypes.ALL]: strings.ui.labels.attribute,
    [FieldTypes.STRING]: strings.ui.labels.stringAttribute,
    [FieldTypes.PICKLIST]: strings.ui.labels.picklistAttribute,
    [FieldTypes.DATETIME]: strings.ui.labels.dateTimeAttribute,
    [FieldTypes.STATECODE]: strings.ui.labels.statecode,
    [FieldTypes.STATUSCODE]: strings.ui.labels.statuscode,
});

export const DynamicCombobox: React.FC<IComboboxComponentProps> = (props) => {
    const { context, value, fieldType, onChange, disabled } = props;
    const { bpfLookupField, statecodeField } = context.parameters;

    // Get localized strings - use English as default, can be enhanced to support language parameter
    const strings = React.useMemo(() => languageLoader('en'), []);
    const fieldTypeLabels = React.useMemo(() => getFieldTypeLabels(strings), [strings]);


    const styles = useStyles();

    const [options, setOptions] = React.useState<ISelectOption[]>([]);
    const [_rawAttributeOptions, setRawAttributeOptions] = React.useState<Record<number | string, IRuntimeMetadata> | null>(null);
    const [isLoading, setIsLoading] = React.useState<boolean>(true);
    const [_entityLogicalName, setEntityLogicalName] = React.useState<string | null>(null);
    const [errorMessage, setErrorMessage] = React.useState<string | null>(null);
    const [isValueInvalid, setIsValueInvalid] = React.useState<boolean>(false);
    const [isOpen, setIsOpen] = React.useState<boolean>(false);

    // Freeform combobox states
    const [matchingOptions, setMatchingOptions] = React.useState<ISelectOption[]>([]);
    const [customSearch, setCustomSearch] = React.useState<string | undefined>();

    /**
     * Transform and filter raw metadata options into a displayable format
     */
    const processAndFilterOptions = React.useCallback((
        rawOptions: Record<number | string, IRuntimeMetadata>,
        currentFieldType: FieldType,
        stateCode: number | undefined
    ): ISelectOption[] => {
        const allOptions = Object.values(rawOptions);
        if (FieldTypeConfig[currentFieldType].category === 'attribute') {
            // Filter out system attributes (AttributeType filtering is now done server-side in OData query)
            const filteredOptions = allOptions.filter(option => !option.key.toString().startsWith('_'));
            const sortedOptions = AttributeUtils.sortAttributes(filteredOptions);

            return sortedOptions
                .map(option => ({
                    key: option.key,
                    text: option.displayName
                }))
                .slice(0, MAX_ATTRIBUTES); // Limit attributes for performance
        }
        const filteredOptions = currentFieldType === FieldTypes.STATUSCODE && stateCode !== undefined
            ? allOptions.filter(option => option.state === stateCode)
            : allOptions;
        return filteredOptions.map(option => ({
            key: option.key,
            text: option.displayName
        }));
    }, []);

    /**
     * Validate configuration and check for required fields
     */
    const validateConfiguration = React.useCallback((): string | null => {
        if (fieldType === FieldTypes.STATUSCODE && statecodeField.raw === null) {
            return strings.errors.statecodeNotConfigured;
        }
        return null;
    }, [fieldType, statecodeField.raw, strings]);

    /**
     * Get the BPF ID from the lookup field
     */
    const getBpfId = React.useCallback((): string | null => {
        return bpfLookupField.raw?.[0]?.id || null;
    }, [bpfLookupField.raw]);

    /**
     * Simple inline error handler for better tree-shaking
     */
    const handleWebApiError = React.useCallback((error: unknown): string => {
        if (typeof error === 'object' && error !== null) {
            const errorObj = error as ErrorObject;
            if ('status' in error && typeof errorObj.status === 'number' && errorObj.status === 403) {
                return strings.errors.permissionDenied;
            }
            if ('message' in error && typeof errorObj.message === 'string') {
                return strings.errors.optionsLoadFailed.replace('{error}', errorObj.message);
            }
        }
        if (error instanceof Error) {
            return strings.errors.optionsLoadFailed.replace('{error}', error.message);
        }
        return strings.errors.optionsLoadFailed.replace('{error}', String(error));
    }, [strings]);



    /**
     * Fetch BPF primary entity with centralized error handling
     */
    const fetchBpfPrimaryEntity = React.useCallback((bpfId: string): Promise<string> => {
        return retrieveRecord(strings.constants.workflow, bpfId, strings.constants.selectPrimaryEntity)
            .then((workflowRecord: Entity) => {
                const entityName = workflowRecord.primaryentity as string;
                if (!entityName) {
                    throw new Error(strings.errors.bpfMissingPrimaryEntity.replace('{bpfId}', bpfId));
                }
                return entityName;
            })
            .catch(error => {
                throw new Error(handleWebApiError(error));
            });
    }, [handleWebApiError, strings]);



    /**
     * Fetch entity metadata with centralized error handling and caching
     * Uses appropriate helper functions based on field type
     */
    const fetchEntityMetadata = React.useCallback((entityName: string): Promise<Record<number | string, IRuntimeMetadata>> => {
        // Return cached data if available
        const cachedData = CacheUtils.get(entityName, fieldType);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }

        if (FieldTypeConfig[fieldType].category === 'attribute') {
            // Build OData query with server-side AttributeType filtering for better performance
            const baseSelect = 'LogicalName,DisplayName';
            const attributeSelect = 'LogicalName,DisplayName,AttributeType,MetadataId';
            const baseFilter = 'IsValidForRead eq true and IsLogical eq false';

            let query: string;
            if (FieldTypeConfig[fieldType].attributeTypeFilter) {
                // Filter by specific attribute type (string, picklist, datetime)
                const attributeTypeFilter = FieldTypeConfig[fieldType].attributeTypeFilter;
                query = `?$select=${baseSelect}&$expand=Attributes($select=${attributeSelect};$filter=${baseFilter} and AttributeType eq Microsoft.Dynamics.CRM.AttributeTypeCode'${attributeTypeFilter}')`;
            } else {
                // No specific type filter (for 'all' fieldType)
                query = `?$select=${baseSelect}&$expand=Attributes($select=${attributeSelect};$filter=${baseFilter})`;
            }

            const dataPromise = fetchEntityDefinitions(entityName, query)
                .then((entityDefinition) => {
                    if (!entityDefinition) {
                        throw new Error(strings.errors.entityNotFound.replace('{entityName}', entityName));
                    }

                    const attributes = entityDefinition.Attributes;

                    if (!attributes || attributes.length === 0) {
                        throw new Error(`No readable attributes found for entity ${entityName}`);
                    }

                    const result: Record<string, IRuntimeMetadata> = {};
                    let attributeCount = 0;

                    // Process attributes using for...of loop instead of forEach
                    for (const attr of attributes) {
                        if (attributeCount >= MAX_ATTRIBUTES) break;

                        // Skip system attributes, base currency attributes, and invalid attributes
                        if (!AttributeUtils.isSystemAttribute(attr.LogicalName) &&
                            !AttributeUtils.isBaseCurrencyAttribute(attr) &&
                            AttributeUtils.isValidAttribute(attr)) {
                            const displayName = AttributeUtils.getDisplayName(attr);
                            // Format: res.displayNameFormat for attributes
                            const formattedDisplayName = `${displayName} (${attr.LogicalName})`;

                            result[attr.LogicalName] = {
                                key: attr.LogicalName,
                                displayName: formattedDisplayName
                            };
                            attributeCount++;
                        }
                    }

                    if (Object.keys(result).length === 0) {
                        throw new Error(`No valid attributes found for entity ${entityName}`);
                    }

                    // Cache the result
                    CacheUtils.set(entityName, fieldType, result);
                    return result as Record<number | string, IRuntimeMetadata>;
                })
                .catch((error: unknown) => {
                    // Clear cache on error to allow retry
                    CacheUtils.clear(entityName, fieldType);
                    throw new Error(handleWebApiError(error));
                });

            return dataPromise
                .catch(error => {
                    // Clear cache on error to allow retry
                    CacheUtils.clear(entityName, fieldType);
                    throw new Error(handleWebApiError(error));
                });
        } else {
            // Use direct fetchEntityDefinitions for state/status metadata
            const attributeType = fieldType === 'statecode' ? 'StateAttributeMetadata' : 'StatusAttributeMetadata';
            const query = `/Attributes/Microsoft.Dynamics.CRM.${attributeType}?$filter=LogicalName eq '${fieldType}'&$select=LogicalName&$expand=OptionSet`;

            const dataPromise = fetchEntityDefinitions(entityName, query)
                .then((response) => {
                    // Response structure: { value: [...] } when using /Attributes path
                    const responseData = response as IEntityDefinitionsResponse;
                    const attributes = responseData?.value || [];
                    const attributeMetadata = attributes.find((attr: IAttributeMetadataResponse) => attr?.LogicalName === fieldType);

                    if (!attributeMetadata?.OptionSet?.Options) {
                        throw new Error(strings.errors.attributeNotFound.replace('{fieldType}', fieldType).replace('{entityName}', entityName));
                    }

                    // For statuscode, cache ALL options without filtering by statecode
                    // Filtering will be done at the component level when rendering
                    const allOptions = attributeMetadata.OptionSet.Options;

                    const result = allOptions.reduce((acc: Record<number, IRuntimeMetadata>, option: IOptionSetOption) => {
                        acc[option.Value] = {
                            key: option.Value,
                            displayName: option.Label?.UserLocalizedLabel?.Label || option.Value.toString(),
                            state: option.State // Include state info for debugging
                        };
                        return acc;
                    }, {} as Record<number, IRuntimeMetadata>) || {};

                    // Cache the result
                    CacheUtils.set(entityName, fieldType, result);
                    return result;
                })
                .catch(error => {
                    // Clear cache on error to allow retry
                    CacheUtils.clear(entityName, fieldType);
                    throw new Error(handleWebApiError(error));
                });

            return dataPromise
                .catch(error => {
                    // Clear cache on error to allow retry
                    CacheUtils.clear(entityName, fieldType);
                    throw new Error(handleWebApiError(error));
                });
        }
    }, [fieldType, handleWebApiError, strings, statecodeField?.raw]);

    /**
     * Reset component state to initial values
     */
    const resetComponentState = React.useCallback((errorMsg: string | null = null) => {
        setOptions([]);
        setRawAttributeOptions(null);
        setEntityLogicalName(null);
        setErrorMessage(errorMsg);
        setIsValueInvalid(false);
        setIsLoading(false);
        onChange(null);
    }, [onChange]);

    /**
     * Update component with successful data
     */
    const updateComponentWithData = React.useCallback((
        entityName: string,
        optionsRecord: Record<number | string, IRuntimeMetadata>
    ) => {
        setEntityLogicalName(entityName);
        setRawAttributeOptions(optionsRecord);
        
        const statecodeValue = statecodeField?.raw ?? undefined;
        const finalOptions = processAndFilterOptions(optionsRecord, fieldType, statecodeValue);
        setOptions(finalOptions);
        
        const isValueNowValid = value === null || finalOptions.some(o => o.key === value);
        setIsValueInvalid(!isValueNowValid);
        
        setErrorMessage(null);
        setIsLoading(false);
    }, [statecodeField?.raw, processAndFilterOptions, fieldType, value]);

    /**
     * Main data loading orchestrator with centralized promise chain management
     */
    const loadOptionsData = React.useCallback(() => {
        // Validate configuration first
        const configError = validateConfiguration();
        if (configError) {
            resetComponentState(configError);
            return Promise.resolve(null)
                .catch(() => {
                    return null;
                });
        }

        // Check for BPF selection
        const bpfId = getBpfId();
        if (!bpfId) {
            resetComponentState(strings.ui.messages.selectBPF);
            return Promise.resolve(null)
                .catch(() => {
                    return null;
                });
        }

        // Set loading state
        setIsLoading(true);
        setErrorMessage(null);

        // Main promise chain with centralized error handling
        return fetchBpfPrimaryEntity(bpfId)
            .then(entityName => Promise.all([Promise.resolve(entityName), fetchEntityMetadata(entityName)]))
            .then(([entityName, optionsRecord]) => {
                updateComponentWithData(entityName, optionsRecord);
                return { entityName, optionsRecord };
            })
            .catch(error => {
                resetComponentState(handleWebApiError(error));
                return null;
            });
    }, [
        validateConfiguration,
        getBpfId,
        fetchBpfPrimaryEntity,
        fetchEntityMetadata,
        updateComponentWithData,
        resetComponentState,
        handleWebApiError
    ]);

    /**
     * Effect hook for data loading with dependency management
     */
    React.useEffect(() => {
        loadOptionsData()
            .catch(error => {
                resetComponentState(handleWebApiError(error));
                return null;
            });
    }, [loadOptionsData, handleWebApiError]);

    /**
     * Effect hook to handle statecode changes for statuscode field type
     * When statecode changes, we need to re-filter statuscode options
     */
    React.useEffect(() => {
        // Only handle statecode changes for statuscode field type
        if (fieldType !== FieldTypes.STATUSCODE) {
            return;
        }

        // Skip if component is still loading initial data
        if (isLoading) {
            return;
        }

        // If we have raw attribute options cached, we can filter them locally
        // instead of making a new API call
        if (_rawAttributeOptions && _entityLogicalName) {
            const statecodeValue = statecodeField?.raw ?? undefined;
            const finalOptions = processAndFilterOptions(_rawAttributeOptions, fieldType, statecodeValue);
            setOptions(finalOptions);

            // Check if current value is still valid with new options
            const isValueNowValid = value === null || finalOptions.some(o => o.key === value);
            setIsValueInvalid(!isValueNowValid);

            // If current value is invalid, clear it
            if (!isValueNowValid && value !== null) {
                onChange(null);
            }
        } else {
            // If no cached data, reload from server
            loadOptionsData()
                .catch(error => {
                    resetComponentState(handleWebApiError(error));
                    return null;
                });
        }
    }, [statecodeField?.raw, fieldType, _rawAttributeOptions, _entityLogicalName, processAndFilterOptions, value, onChange, loadOptionsData, resetComponentState, handleWebApiError, isLoading]);

    // Removed complex theme injection - back to simple styling

    /**
     * Handle option selection
     */
    const onOptionSelect = React.useCallback((event: SelectionEvents, data: OptionOnSelectData): void => {
        event.stopPropagation();
        const newValue = data.optionValue ? (FieldTypeConfig[fieldType].storage === 'string' ? data.optionValue : Number(data.optionValue)) : null;
        onChange(newValue);
        setIsOpen(false);
    }, [onChange, fieldType]);

    // Removed container click handlers - let Combobox handle its own interactions

    /**
     * Synchronizes the internal open state
     */
    const handleOpenChange = React.useCallback((_event: ComboboxOpenEvents, data: ComboboxOpenChangeData) => {
        setIsOpen(data.open);
    }, []);

    /**
     * Get the display text for the selected option
     */
    const selectedOptionText = React.useMemo(() => {
        return options.find(o => o.key === value)?.text || '';
    }, [options, value]);

    const comboboxLabel = React.useMemo(() => fieldTypeLabels[fieldType], [fieldType]);

    const placeholderText = React.useMemo(() => {
        return isLoading ? strings.ui.messages.loading : comboboxLabel;
    }, [isLoading, comboboxLabel, strings]);

    // Update matching options when options change (for freeform)
    React.useEffect(() => {
        setMatchingOptions([...options]);
    }, [options]);

    // Get current field type configuration
    const fieldConfig = React.useMemo(() => FieldTypeConfig[fieldType], [fieldType]);
    const isFreeform = fieldConfig.category === 'attribute';

    // Freeform handlers - Fixed typing issues
    const onFreeformChange: ComboboxProps['onChange'] = React.useCallback((event) => {
        if (!isFreeform) return;

        // Get the raw input value without trimming to preserve user typing
        const inputValue = event.target.value;

        // Update custom search immediately to preserve typing
        setCustomSearch(inputValue);

        // Filter matches based on trimmed value for better matching
        const trimmedValue = inputValue.trim();
        const matches = options.filter(
            (option) => option.text.toLowerCase().indexOf(trimmedValue.toLowerCase()) === 0
        );
        setMatchingOptions(matches);

        // Don't clear customSearch if user is actively typing
        if (trimmedValue.length === 0) {
            setCustomSearch(undefined);
            setMatchingOptions([...options]); // Show all options when empty
        }
    }, [isFreeform, options]);

    const onFreeformOptionSelect: ComboboxProps['onOptionSelect'] = React.useCallback((_event, data) => {
        if (!isFreeform) return;

        // Handle option selection more carefully to preserve user input
        const matchingOption = data.optionText && options.find(opt => opt.text === data.optionText);
        if (matchingOption) {
            // Clear custom search when selecting a real option
            setCustomSearch(undefined);
            onChange(matchingOption.key);
        } else if (data.optionText) {
            // Preserve custom text input
            setCustomSearch(data.optionText);
            onChange(data.optionText);
        } else {
            // Fallback - preserve current custom search if available
            const currentValue = customSearch || '';
            setCustomSearch(currentValue);
            onChange(currentValue || null);
        }
        setIsOpen(false);
    }, [isFreeform, options, onChange, customSearch]);

    // Removed autocomplete logic - back to simple combobox

    /**
     * Render the freeform combobox for attributes
     */
    const renderFreeformCombobox = React.useCallback(() => (
        <Combobox
            freeform
            aria-label={comboboxLabel}
            placeholder={placeholderText}
            onChange={onFreeformChange}
            onOptionSelect={onFreeformOptionSelect}
            disabled={disabled || isLoading}
            className={styles.combobox}
            open={isOpen}
            onOpenChange={handleOpenChange}
            aria-invalid={isValueInvalid}
            value={customSearch || selectedOptionText}
        >
            {customSearch ? (
                <Option key={strings.constants.freeformKey} text={customSearch}>
                    Search for "{customSearch}"
                </Option>
            ) : null}
            {matchingOptions.map((option: ISelectOption) => (
                <Option key={option.key} value={String(option.key)}>
                    {option.text}
                </Option>
            ))}
        </Combobox>
    ), [
        comboboxLabel,
        placeholderText,
        onFreeformChange,
        onFreeformOptionSelect,
        disabled,
        isLoading,
        styles.combobox,
        isOpen,
        handleOpenChange,
        isValueInvalid,
        customSearch,
        selectedOptionText,
        matchingOptions
    ]);

    /**
     * Render the standard combobox for optionsets
     */
    const renderStandardCombobox = React.useCallback(() => (
        <Combobox
            value={selectedOptionText}
            onOptionSelect={onOptionSelect}
            disabled={disabled || isLoading}
            className={styles.combobox}
            open={isOpen}
            onOpenChange={handleOpenChange}
            placeholder={placeholderText}
            aria-invalid={isValueInvalid}
            aria-label={comboboxLabel}
        >
            {options.map((option: ISelectOption) => (
                <Option key={option.key} value={String(option.key)}>
                    {option.text}
                </Option>
            ))}
        </Combobox>
    ), [
        selectedOptionText,
        onOptionSelect,
        disabled,
        isLoading,
        styles,
        isOpen,
        handleOpenChange,
        isValueInvalid,
        options,
        placeholderText,
        comboboxLabel
    ]);

    /**
     * Render the appropriate combobox based on field type
     */
    const renderCombobox = React.useCallback(() => {
        return isFreeform ? renderFreeformCombobox() : renderStandardCombobox();
    }, [isFreeform, renderFreeformCombobox, renderStandardCombobox]);

    /**
     * Render the main content
     */
    const renderContent = React.useCallback(() => (
        <div className={styles.wrapper}>
            <div className={`${styles.controlContainer} ${isLoading ? 'loading' : ''}`}>
                {renderCombobox()}
            </div>
            {errorMessage && (
                <div className={styles.errorText} role={strings.constants.alertRole}>
                    {errorMessage}
                </div>
            )}
        </div>
    ), [
        styles,
        isLoading,
        errorMessage,
        renderCombobox
    ]);

    // Removed autocomplete effects

    return (
        <FluentProvider theme={webLightTheme} style={{ width: '100%' }}>
            {renderContent()}
        </FluentProvider>
    );
};