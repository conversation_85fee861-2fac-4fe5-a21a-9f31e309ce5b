﻿<?xml version="1.0" encoding="utf-8"?>
<forms xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <systemform>
    <formid>{24d7f159-3446-44db-9b0b-fae1b6abb8fd}</formid>
    <IntroducedVersion>*******</IntroducedVersion>
    <FormPresentation>1</FormPresentation>
    <FormActivationState>1</FormActivationState>
    <form shownavigationbar="true" showImage="false" maxWidth="1920" headerdensity="HighWithControls">
      <tabs>
        <tab verticallayout="true" id="{f8ddddc2-8fd4-47ce-bd53-e11b4b414308}" IsUserDefined="1" name="tab_general" locklevel="0" expanded="true" showlabel="true">
          <labels>
            <label description="General" languagecode="1033" />
          </labels>
          <columns>
            <column width="100%">
              <sections>
                <section showlabel="false" showbar="false" IsUserDefined="0" id="{325a12a6-f4b4-4386-98d3-c23427110bd9}" name="tab_general_section_information" columns="111">
                  <labels>
                    <label description="General" languagecode="1033" />
                  </labels>
                  <rows>
                    <row>
                      <cell id="{6086254d-6b51-2342-b8da-aa46eb7092d9}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Process" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_processid" classid="{270BD3DB-D9AF-4782-9025-509E298DEC0A}" datafieldname="c30seeds_processid" disabled="false" uniqueid="{0c14e972-11c7-00e2-0495-ac251ab9e391}">
                          <parameters>
                            <AutoResolve>true</AutoResolve>
                            <DisableMru>false</DisableMru>
                            <DisableQuickFind>false</DisableQuickFind>
                            <DisableViewPicker>true</DisableViewPicker>
                            <DefaultViewId>{3747C515-CEF5-4489-8B5E-050C6078ABD3}</DefaultViewId>
                            <AllowFilterOff>false</AllowFilterOff>
                          </parameters>
                        </control>
                      </cell>
                      <cell id="{fe81031f-1310-4731-a1c0-f2c55e5f3581}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Enable Save Stage" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_isstagesaved" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_isstagesaved" disabled="false" uniqueid="{57083634-57ca-40b2-8fc7-1e1bf667262b}" />
                      </cell>
                      <cell id="{88eb3071-ba65-a7a3-42d2-8d7420ca9ca0}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Enable Save Approver" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_isapproversaved" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_isapproversaved" disabled="false" uniqueid="{21a3bb27-5842-be19-5523-618de9c83c85}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{4e38ba40-8066-06c7-4678-2ad5908dc2eb}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Roles" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_roles" classid="{4273EDBD-AC1D-40d3-9FB2-095C621B552D}" datafieldname="c30seeds_roles" disabled="false" />
                      </cell>
                      <cell id="{44c0b496-f70b-41dc-81c8-fc4fc1fb2134}" locklevel="0" colspan="1" rowspan="1" visible="false" showlabel="true">
                        <labels>
                          <label description="Stage Column" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_stagecol" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_stagecol" disabled="false" uniqueid="{c86bc93d-83a6-42ed-9557-8554b1c421bd}" />
                      </cell>
                      <cell id="{d8ad87ed-c083-7e3b-43ca-eec6b2d936a6}" showlabel="true" locklevel="0" visible="false" colspan="1" rowspan="1">
                        <labels>
                          <label description="Approver Column" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_approvercol" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_approvercol" disabled="false" uniqueid="{2a536541-aa18-4d64-ad1e-6d6fc08b46c7}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{6b7e2265-9234-4918-acc3-bec7fae22456}">
                        <labels>
                          <label description="Name" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_name" classid="{4273EDBD-AC1D-40d3-9FB2-095C621B552D}" datafieldname="c30seeds_name" />
                      </cell>
                      <cell locklevel="0" id="{4b0e407d-bf79-45f3-860f-9ea9e6d06b76}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                      <cell id="{7e1c91be-9d6d-dc45-54e3-cee980838a50}" showlabel="true" locklevel="0" visible="false" colspan="1" rowspan="1">
                        <labels>
                          <label description="Approve Date Column" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_approvedatecol" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_approvedatecol" disabled="false" uniqueid="{2b14b643-2297-4928-8640-f4c391fc9ff1}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{01ce75ab-0ec7-173b-c13b-ea9ee50824c9}" showlabel="true" locklevel="0">
                        <labels>
                          <label description="Final step" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_finalstep" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_finalstep" disabled="false" uniqueid="{03037ad8-409c-3d00-1dc0-67832cd93884}" />
                      </cell>
                      <cell locklevel="0" id="{2e59995f-c96b-4136-b4f4-06fbd1d4d8d0}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                      <cell locklevel="0" id="{6759395d-4e73-4666-9b3b-487026a82d3c}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                    </row>
                    <row>
                      <cell id="{c53685a8-9ab3-4188-be0f-1b1e13a72f89}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Send Email ?" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_issendemail" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_issendemail" disabled="false" uniqueid="{51f38d82-7593-41b7-92d1-186c77f25e64}" />
                      </cell>
                      <cell id="{32a6143d-ab56-a071-1768-2c68168bd227}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                      <cell locklevel="0" id="{49b91f63-3971-44c5-b6cc-b011b0277fdd}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                    </row>
                    <row>
                      <cell id="{40c1d322-7c02-3011-9675-2084498a6163}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="With Mobile" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_withmobile" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_withmobile" disabled="false" uniqueid="{e8d02a33-7833-056d-e81d-ce9d7828b23d}" />
                      </cell>
                      <cell id="{20011349-769a-ed82-100c-933cc18bc823}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                      <cell locklevel="0" id="{4b8e5711-9e5e-4055-bb23-f1728fee61fc}" showlabel="false">
                        <labels>
                          <label description="" languagecode="1033" />
                        </labels>
                      </cell>
                    </row>
                  </rows>
                </section>
                <section name="tab_general_section_setupstep" showlabel="true" showbar="false" locklevel="0" id="{cd9b40c3-8464-25c2-2484-5c518baae736}" IsUserDefined="0" layout="varwidth" columns="11" labelwidth="115" celllabelalignment="Left" celllabelposition="Top">
                  <labels>
                    <label description="Setup Step" languagecode="1033" />
                  </labels>
                  <rows>
                    <row>
                      <cell id="{87cd2775-43b7-0d7d-91aa-4b46ac35596b}" showlabel="true" locklevel="0">
                        <labels>
                          <label description="Process Current Stage" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_processstageid" classid="{270BD3DB-D9AF-4782-9025-509E298DEC0A}" datafieldname="c30seeds_processstageid" disabled="false" uniqueid="{180ce0b9-565e-ea11-25c3-2d22177aecbb}">
                          <parameters>
                            <AutoResolve>true</AutoResolve>
                            <DisableMru>false</DisableMru>
                            <DisableQuickFind>false</DisableQuickFind>
                            <DisableViewPicker>false</DisableViewPicker>
                            <DefaultViewId>{1BCC6C47-0426-490F-A7FC-1A53AB5C03F3}</DefaultViewId>
                            <FilterRelationshipName>process_processstage</FilterRelationshipName>
                            <DependentAttributeName>c30seeds_processconfig.c30seeds_processid</DependentAttributeName>
                            <DependentAttributeType>workflow</DependentAttributeType>
                            <AllowFilterOff>true</AllowFilterOff>
                          </parameters>
                        </control>
                      </cell>
                      <cell id="{5095ac47-918c-3998-9365-12dd84102196}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Process Previous Stage" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_processpreviousstageid" classid="{270BD3DB-D9AF-4782-9025-509E298DEC0A}" datafieldname="c30seeds_processpreviousstageid" disabled="false" uniqueid="{28ce2391-4259-2dbb-e211-c44649e977a0}">
                          <parameters>
                            <AutoResolve>true</AutoResolve>
                            <DisableMru>false</DisableMru>
                            <DisableQuickFind>false</DisableQuickFind>
                            <DisableViewPicker>false</DisableViewPicker>
                            <DefaultViewId>{1BCC6C47-0426-490F-A7FC-1A53AB5C03F3}</DefaultViewId>
                            <FilterRelationshipName>process_processstage</FilterRelationshipName>
                            <DependentAttributeName>c30seeds_processconfig.c30seeds_processid</DependentAttributeName>
                            <DependentAttributeType>workflow</DependentAttributeType>
                            <AllowFilterOff>true</AllowFilterOff>
                          </parameters>
                        </control>
                      </cell>
                    </row>
                    <row>
                      <cell id="{5fa10909-7fcd-43ef-8005-f7869b79d9fb}" locklevel="0" colspan="1" rowspan="1" showlabel="true">
                        <labels>
                          <label description="Set Current StateCode" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_statecode" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_statecode" disabled="false" uniqueid="{b5684e88-adac-4308-9676-2cbea21aeac3}" />
                      </cell>
                      <cell id="{2c98d0fd-80b0-4eb1-9d06-790a8cf17571}" locklevel="0" colspan="1" rowspan="1" showlabel="true">
                        <labels>
                          <label description="Set Previous StateCode" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_statecodeprevious" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_statecodeprevious" disabled="false" uniqueid="{a2ec661d-b739-4c9f-bd93-adf692c8c148}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{3717bb3e-2e32-7be7-7545-165bc10c482b}" showlabel="true" locklevel="0">
                        <labels>
                          <label description="Set Current StatusCode" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_statuscode" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_statuscode" disabled="false" uniqueid="{ac2e4c88-db46-4e2c-93a1-20c66e8c1e47}" />
                      </cell>
                      <cell id="{ba258530-12ce-399a-9377-1c3be483d454}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Set Previous StatusCode" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_statuscodeprevious" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_statuscodeprevious" disabled="false" uniqueid="{c39f543b-e4ce-455d-ac62-3096730b72ee}" />
                      </cell>
                    </row>
                  </rows>
                </section>
                <section name="tab_general_section_pcf" id="d3382dc7-79b6-48c2-9055-ea9bb69b77d1" IsUserDefined="0" locklevel="0" showlabel="true" showbar="false" layout="varwidth" celllabelalignment="Left" celllabelposition="Left" columns="1" labelwidth="115">
                  <labels>
                    <label description="Config for PCF" languagecode="1033" />
                  </labels>
                  <rows>
                    <row>
                      <cell id="{c36fdd43-4447-490b-a592-86bf990c28d2}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Control Id" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_pcfcontrolid" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_pcfcontrolid" disabled="false" uniqueid="{016ebb26-1c30-4348-b1e5-b413b6e1bdc8}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{b0c5b653-7e17-4a1f-85e5-e68f21418c42}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="PCF Target Column" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_pcfboundfield" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_pcfboundfield" disabled="false" uniqueid="{d5e65861-5a9b-4306-818e-09a3c3581dd7}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{21d36a00-fe5c-4839-bb23-1116a9c50a07}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Language" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_pcflanguage" classid="{671A9387-CA5A-4D1E-8AB7-06E39DDCF6B5}" datafieldname="c30seeds_pcflanguage" disabled="false" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{b2e9eeb1-7acc-4c2e-bbc5-0b12e0b7139c}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Enable File Attachment" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_enablefileattachment" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_enablefileattachment" disabled="false" uniqueid="{0552f1b5-5f89-4074-b791-6db14c7ff013}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{b631ba0c-9be7-45e3-8e31-4e26343a2b15}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Max File Size (MB)" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_maxfilesize" classid="{C6D124CA-7EDA-4A60-AEA9-7FB8D318B68F}" datafieldname="c30seeds_maxfilesize" disabled="false" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{444e810c-10d2-4c26-881b-d2b3df74f386}" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Allow File Types" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_allowedfiletypes" classid="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}" datafieldname="c30seeds_allowedfiletypes" disabled="false" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{ec9da491-10c1-c9ae-da18-50cdd141a1c6}" showlabel="true" locklevel="0" colspan="1" rowspan="1">
                        <labels>
                          <label description="Check document" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_checkdoc" classid="{F9A8A302-114E-466A-B582-6771B2AE0D92}" datafieldname="c30seeds_checkdoc" disabled="false" uniqueid="{b5809eb2-7ae1-aa6c-a7be-4082c62295e9}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{0ca0bc03-96c5-4215-b0f3-ce44c1c1b3d6}" locklevel="0" colspan="1" rowspan="5">
                        <labels>
                          <label description="Custom Text" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_pcfcustomtext" classid="{E0DECE4B-6FC8-4A8F-A065-082708572369}" datafieldname="c30seeds_pcfcustomtext" disabled="false" />
                      </cell>
                    </row>
                    <row />
                    <row />
                    <row />
                    <row />
                  </rows>
                </section>
                <section name="tab_general_section_hidden" showlabel="false" showbar="false" locklevel="0" id="{985069ac-2b1d-2ed3-1be4-76cbce325ce0}" IsUserDefined="0" layout="varwidth" columns="1" labelwidth="115" celllabelalignment="Left" celllabelposition="Left" visible="false" availableforphone="false">
                  <labels>
                    <label description="Hidden" languagecode="1033" />
                  </labels>
                  <rows>
                    <row>
                      <cell id="{c900a217-284d-b489-e8d8-a857645e35dc}" showlabel="true" locklevel="0" visible="false" colspan="1" rowspan="1">
                        <labels>
                          <label description="Set Approval Current Status" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_approvalstatus" classid="{C6D124CA-7EDA-4a60-AEA9-7FB8D318B68F}" datafieldname="c30seeds_approvalstatus" disabled="false" uniqueid="{4ebbb847-56a7-a003-59eb-95c4c0905b8e}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{2ba52604-1466-ce9b-5d60-aa0e5c29cecb}" showlabel="true" locklevel="0" colspan="1" rowspan="1" visible="false">
                        <labels>
                          <label description="Set Approval Previous Status" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_approvalstatusprevious" classid="{C6D124CA-7EDA-4a60-AEA9-7FB8D318B68F}" datafieldname="c30seeds_approvalstatusprevious" disabled="false" uniqueid="{799846de-4449-4121-bea5-9b8191dd03ba}" />
                      </cell>
                    </row>
                    <row>
                      <cell id="{28d12812-badd-cc18-9599-1e502ac736e0}" showlabel="false" locklevel="0" visible="false">
                        <labels>
                          <label description="Registered" languagecode="1033" />
                        </labels>
                        <control id="c30seeds_registered" classid="{67FAC785-CD58-4f9f-ABB3-4B7DDC6ED5ED}" datafieldname="c30seeds_registered" disabled="true" uniqueid="{2d546a9e-b12a-1a92-0d6e-38c63cacd9ba}" />
                      </cell>
                    </row>
                  </rows>
                </section>
              </sections>
            </column>
          </columns>
        </tab>
      </tabs>
      <Navigation>
        <NavBar />
        <NavBarAreas>
          <NavBarArea Id="Info">
            <Titles>
              <Title LCID="1033" Text="Common" />
            </Titles>
          </NavBarArea>
          <NavBarArea Id="Sales">
            <Titles>
              <Title LCID="1033" Text="Sales" />
            </Titles>
          </NavBarArea>
          <NavBarArea Id="Service">
            <Titles>
              <Title LCID="1033" Text="Service" />
            </Titles>
          </NavBarArea>
          <NavBarArea Id="Marketing">
            <Titles>
              <Title LCID="1033" Text="Marketing" />
            </Titles>
          </NavBarArea>
          <NavBarArea Id="ProcessCenter">
            <Titles>
              <Title LCID="1033" Text="Process Sessions" />
            </Titles>
          </NavBarArea>
        </NavBarAreas>
      </Navigation>
      <controlDescriptions>
        <controlDescription forControl="{03037ad8-409c-3d00-1dc0-67832cd93884}">
          <customControl id="{67FAC785-CD58-4f9f-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_finalstep</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_finalstep</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_finalstep</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_finalstep</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{b5809eb2-7ae1-aa6c-a7be-4082c62295e9}">
          <customControl id="{67FAC785-CD58-4f9f-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_checkdoc</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_checkdoc</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_checkdoc</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_checkdoc</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{e8d02a33-7833-056d-e81d-ce9d7828b23d}">
          <customControl id="{67FAC785-CD58-4f9f-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_withmobile</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_withmobile</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_withmobile</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_withmobile</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{21a3bb27-5842-be19-5523-618de9c83c85}">
          <customControl id="{67FAC785-CD58-4f9f-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_isapproversaved</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_isapproversaved</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_isapproversaved</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value>c30seeds_isapproversaved</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{57083634-57ca-40b2-8fc7-1e1bf667262b}">
          <customControl id="{67FAC785-CD58-4F9F-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_isstagesaved</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_isstagesaved</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_isstagesaved</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_isstagesaved</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{0552f1b5-5f89-4074-b791-6db14c7ff013}">
          <customControl id="{67FAC785-CD58-4F9F-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_enablefileattachment</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_enablefileattachment</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_enablefileattachment</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_enablefileattachment</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{51f38d82-7593-41b7-92d1-186c77f25e64}">
          <customControl id="{67FAC785-CD58-4F9F-ABB3-4B7DDC6ED5ED}">
            <parameters>
              <datafieldname>c30seeds_issendemail</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_issendemail</value>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_issendemail</value>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="MscrmControls.FieldControls.ToggleControl">
            <parameters>
              <value type="TwoOptions">c30seeds_issendemail</value>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{b5684e88-adac-4308-9676-2cbea21aeac3}">
          <customControl id="{C6D124CA-7EDA-4A60-AEA9-7FB8D318B68F}">
            <parameters>
              <datafieldname>c30seeds_statecode</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{a2ec661d-b739-4c9f-bd93-adf692c8c148}">
          <customControl id="{C6D124CA-7EDA-4A60-AEA9-7FB8D318B68F}">
            <parameters>
              <datafieldname>c30seeds_statecodeprevious</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statecodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statecode</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{ac2e4c88-db46-4e2c-93a1-20c66e8c1e47}">
          <customControl id="{C6D124CA-7EDA-4A60-AEA9-7FB8D318B68F}">
            <parameters>
              <datafieldname>c30seeds_statuscode</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecode</statecodeField>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecode</statecodeField>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscode</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecode</statecodeField>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{c39f543b-e4ce-455d-ac62-3096730b72ee}">
          <customControl id="{C6D124CA-7EDA-4A60-AEA9-7FB8D318B68F}">
            <parameters>
              <datafieldname>c30seeds_statuscodeprevious</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecodeprevious</statecodeField>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecodeprevious</statecodeField>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_statuscodeprevious</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">statuscode</fieldType>
              <statecodeField type="Whole.None">c30seeds_statecodeprevious</statecodeField>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{d5e65861-5a9b-4306-818e-09a3c3581dd7}">
          <customControl id="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}">
            <parameters>
              <datafieldname>c30seeds_pcfboundfield</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfboundfield</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfboundfield</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfboundfield</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{2a536541-aa18-4d64-ad1e-6d6fc08b46c7}">
          <customControl id="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}">
            <parameters>
              <datafieldname>c30seeds_approvercol</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvercol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvercol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvercol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{2b14b643-2297-4928-8640-f4c391fc9ff1}">
          <customControl id="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}">
            <parameters>
              <datafieldname>c30seeds_approvedatecol</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvedatecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">datetime</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvedatecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">datetime</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_approvedatecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">datetime</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{c86bc93d-83a6-42ed-9557-8554b1c421bd}">
          <customControl id="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}">
            <parameters>
              <datafieldname>c30seeds_stagecol</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_stagecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_stagecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ComboboxControl">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_stagecol</sourceControl>
              <bpfLookupField type="Lookup.Simple">c30seeds_processid</bpfLookupField>
              <fieldType type="Enum" static="true">string</fieldType>
            </parameters>
          </customControl>
        </controlDescription>
        <controlDescription forControl="{016ebb26-1c30-4348-b1e5-b413b6e1bdc8}">
          <customControl id="{4273EDBD-AC1D-40D3-9FB2-095C621B552D}">
            <parameters>
              <datafieldname>c30seeds_pcfcontrolid</datafieldname>
            </parameters>
          </customControl>
          <customControl formFactor="0" name="zlb_LokBiz.Controls.ActionButton">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfcontrolid</sourceControl>
              <apiEndpoint type="SingleLine.Text" static="true">c30seeds_RegistrationServices</apiEndpoint>
            </parameters>
          </customControl>
          <customControl formFactor="2" name="zlb_LokBiz.Controls.ActionButton">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfcontrolid</sourceControl>
              <apiEndpoint type="SingleLine.Text" static="true">c30seeds_RegistrationServices</apiEndpoint>
            </parameters>
          </customControl>
          <customControl formFactor="1" name="zlb_LokBiz.Controls.ActionButton">
            <parameters>
              <sourceControl type="SingleLine.Text">c30seeds_pcfcontrolid</sourceControl>
              <apiEndpoint type="SingleLine.Text" static="true">c30seeds_RegistrationServices</apiEndpoint>
            </parameters>
          </customControl>
        </controlDescription>
      </controlDescriptions>
      <header id="{f6475234-2ffc-4705-b3fe-9fef0afd4f62}" celllabelposition="Top" columns="111" labelwidth="115" celllabelalignment="Left">
        <rows>
          <row>
            <cell id="{6cc49724-3e45-453e-9f0b-b668d4fac057}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
            <cell id="{7326d2d5-45b1-4abc-b707-0f4d8a7b4376}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
            <cell id="{3b36936b-4c5b-4be3-8587-d3bf1df5014e}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
          </row>
        </rows>
      </header>
      <footer id="{ad08ea80-93a5-4cab-95c3-82ea476fbada}" celllabelposition="Top" columns="111" labelwidth="115" celllabelalignment="Left">
        <rows>
          <row>
            <cell id="{ae515077-89ec-45f3-9eab-139b23d713cc}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
            <cell id="{ecba9bda-38f8-4c26-a997-c9d85be46ee0}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
            <cell id="{239fc3b9-8c9c-470c-a4cd-bb646278acde}" showlabel="false">
              <labels>
                <label description="" languagecode="1033" />
              </labels>
            </cell>
          </row>
        </rows>
      </footer>
      <formLibraries>
        <Library name="c30seeds_/processConfig/processconfig.js" libraryUniqueId="{70c1dbec-45bf-4c07-9d1e-04d1cca811e3}" />
      </formLibraries>
      <events>
        <event name="onload" application="false" active="false">
          <Handlers>
            <Handler functionName="C30Seeds.ProcessConfig.Form.formOnLoad" libraryName="c30seeds_/processConfig/processconfig.js" handlerUniqueId="{52e33d8d-4b72-4b09-a144-3a599c449926}" enabled="true" parameters="" passExecutionContext="true" />
          </Handlers>
        </event>
        <event name="onchange" application="false" active="false" attribute="c30seeds_isapproversaved">
          <Handlers>
            <Handler functionName="C30Seeds.ProcessConfig.Form.saveInfoApproverOnChange" libraryName="c30seeds_/processConfig/processconfig.js" handlerUniqueId="{1f16ffc6-73a2-452a-9e6f-700cdb368691}" enabled="true" parameters="" passExecutionContext="true" />
          </Handlers>
        </event>
        <event name="onchange" application="false" active="false" attribute="c30seeds_isstagesaved">
          <Handlers>
            <Handler functionName="C30Seeds.ProcessConfig.Form.saveStageNameOnChange" libraryName="c30seeds_/processConfig/processconfig.js" handlerUniqueId="{b8273480-3349-4049-a669-5099456b817f}" enabled="true" parameters="" passExecutionContext="true" />
          </Handlers>
        </event>
      </events>
      <DisplayConditions Order="0" FallbackForm="true">
        <Everyone />
      </DisplayConditions>
    </form>
    <IsCustomizable>1</IsCustomizable>
    <CanBeDeleted>1</CanBeDeleted>
    <LocalizedNames>
      <LocalizedName description="Information" languagecode="1033" />
    </LocalizedNames>
    <Descriptions>
      <Description description="A form for this entity." languagecode="1033" />
    </Descriptions>
  </systemform>
</forms>