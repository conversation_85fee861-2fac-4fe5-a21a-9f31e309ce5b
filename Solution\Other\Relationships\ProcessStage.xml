﻿<?xml version="1.0" encoding="utf-8"?>
<EntityRelationships xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <EntityRelationship Name="c30seeds_processstage_c30seeds_processconfig">
    <EntityRelationshipType>OneToMany</EntityRelationshipType>
    <IsCustomizable>1</IsCustomizable>
    <IntroducedVersion>*******</IntroducedVersion>
    <IsHierarchical>0</IsHierarchical>
    <ReferencingEntityName>c30seeds_processconfig</ReferencingEntityName>
    <ReferencedEntityName>ProcessStage</ReferencedEntityName>
    <CascadeAssign>NoCascade</CascadeAssign>
    <CascadeDelete>RemoveLink</CascadeDelete>
    <CascadeArchive>NoCascade</CascadeArchive>
    <CascadeReparent>NoCascade</CascadeReparent>
    <CascadeShare>NoCascade</CascadeShare>
    <CascadeUnshare>NoCascade</CascadeUnshare>
    <CascadeRollupView>NoCascade</CascadeRollupView>
    <IsValidForAdvancedFind>1</IsValidForAdvancedFind>
    <ReferencingAttributeName>c30seeds_processstageid</ReferencingAttributeName>
    <RelationshipDescription>
      <Descriptions>
        <Description description="Unique identifier for Process Stage associated with Process Config." languagecode="1033" />
      </Descriptions>
    </RelationshipDescription>
    <EntityRelationshipRoles>
      <EntityRelationshipRole>
        <NavPaneDisplayOption>UseCollectionName</NavPaneDisplayOption>
        <NavPaneArea>Details</NavPaneArea>
        <NavPaneOrder>10000</NavPaneOrder>
        <NavigationPropertyName>c30seeds_processstageid</NavigationPropertyName>
        <CustomLabels>
          <CustomLabel description="" languagecode="1033" />
        </CustomLabels>
        <RelationshipRoleType>1</RelationshipRoleType>
      </EntityRelationshipRole>
      <EntityRelationshipRole>
        <NavigationPropertyName>c30seeds_processstage_c30seeds_processconfig</NavigationPropertyName>
        <RelationshipRoleType>0</RelationshipRoleType>
      </EntityRelationshipRole>
    </EntityRelationshipRoles>
  </EntityRelationship>
  <EntityRelationship Name="c30seeds_processstage_c30seeds_processconfig_processpreviousstageid">
    <EntityRelationshipType>OneToMany</EntityRelationshipType>
    <IsCustomizable>1</IsCustomizable>
    <IntroducedVersion>*******</IntroducedVersion>
    <IsHierarchical>0</IsHierarchical>
    <ReferencingEntityName>c30seeds_processconfig</ReferencingEntityName>
    <ReferencedEntityName>ProcessStage</ReferencedEntityName>
    <CascadeAssign>NoCascade</CascadeAssign>
    <CascadeDelete>RemoveLink</CascadeDelete>
    <CascadeArchive>NoCascade</CascadeArchive>
    <CascadeReparent>NoCascade</CascadeReparent>
    <CascadeShare>NoCascade</CascadeShare>
    <CascadeUnshare>NoCascade</CascadeUnshare>
    <CascadeRollupView>NoCascade</CascadeRollupView>
    <IsValidForAdvancedFind>1</IsValidForAdvancedFind>
    <ReferencingAttributeName>c30seeds_processpreviousstageid</ReferencingAttributeName>
    <RelationshipDescription>
      <Descriptions>
        <Description description="" languagecode="1033" />
      </Descriptions>
    </RelationshipDescription>
    <EntityRelationshipRoles>
      <EntityRelationshipRole>
        <NavPaneDisplayOption>UseCollectionName</NavPaneDisplayOption>
        <NavPaneArea>Details</NavPaneArea>
        <NavPaneOrder>10000</NavPaneOrder>
        <NavigationPropertyName>c30seeds_processpreviousstageid</NavigationPropertyName>
        <RelationshipRoleType>1</RelationshipRoleType>
      </EntityRelationshipRole>
      <EntityRelationshipRole>
        <NavigationPropertyName>c30seeds_processstage_c30seeds_processconfig_processpreviousstageid</NavigationPropertyName>
        <RelationshipRoleType>0</RelationshipRoleType>
      </EntityRelationshipRole>
    </EntityRelationshipRoles>
  </EntityRelationship>
  <EntityRelationship Name="processstage_parentprocessstage">
    <EntityRelationshipType>OneToMany</EntityRelationshipType>
    <IsCustomizable>0</IsCustomizable>
    <IntroducedVersion>*******</IntroducedVersion>
    <IsHierarchical>1</IsHierarchical>
    <ReferencingEntityName>ProcessStage</ReferencingEntityName>
    <ReferencedEntityName>ProcessStage</ReferencedEntityName>
    <CascadeAssign>NoCascade</CascadeAssign>
    <CascadeDelete>Cascade</CascadeDelete>
    <CascadeArchive>NoCascade</CascadeArchive>
    <CascadeReparent>NoCascade</CascadeReparent>
    <CascadeShare>NoCascade</CascadeShare>
    <CascadeUnshare>NoCascade</CascadeUnshare>
    <CascadeRollupView>NoCascade</CascadeRollupView>
    <ReferencingAttributeName>ParentProcessStageId</ReferencingAttributeName>
    <RelationshipDescription>
      <Descriptions>
        <Description description="The parent stage for the parameter." languagecode="1033" />
      </Descriptions>
    </RelationshipDescription>
  </EntityRelationship>
</EntityRelationships>