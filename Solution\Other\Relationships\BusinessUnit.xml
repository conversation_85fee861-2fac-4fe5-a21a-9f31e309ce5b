﻿<?xml version="1.0" encoding="utf-8"?>
<EntityRelationships xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <EntityRelationship Name="business_unit_c30seeds_processconfig">
    <EntityRelationshipType>OneToMany</EntityRelationshipType>
    <IsCustomizable>1</IsCustomizable>
    <IntroducedVersion>*******</IntroducedVersion>
    <IsHierarchical>0</IsHierarchical>
    <ReferencingEntityName>c30seeds_processconfig</ReferencingEntityName>
    <ReferencedEntityName>BusinessUnit</ReferencedEntityName>
    <CascadeAssign>NoCascade</CascadeAssign>
    <CascadeDelete>Restrict</CascadeDelete>
    <CascadeArchive>Restrict</CascadeArchive>
    <CascadeReparent>NoCascade</CascadeReparent>
    <CascadeShare>NoCascade</CascadeShare>
    <CascadeUnshare>NoCascade</CascadeUnshare>
    <ReferencingAttributeName>OwningBusinessUnit</ReferencingAttributeName>
    <RelationshipDescription>
      <Descriptions>
        <Description description="Unique identifier for the business unit that owns the record" languagecode="1033" />
      </Descriptions>
    </RelationshipDescription>
  </EntityRelationship>
</EntityRelationships>