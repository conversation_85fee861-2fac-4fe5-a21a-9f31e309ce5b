import * as React from "react";

import { DEFAULT_VALUES } from "../../SharedLibraries/types/Constants";
import { DEFAULT_FILE_TYPES_STRING } from "../../SharedLibraries/types/FileTypes";
import {
  createAdvancedLocalizationMerger,
  createLanguageLoader,
  getValidatedLanguage,
  LANGUAGES
} from "../../SharedLibraries/utils/LocalizationUtils";
import { withErrorBoundary } from "./components/ErrorBoundary";
import { ProcessCheckpointControl } from "./components/ProcessCheckpointControl";
import type { IInputs, IOutputs } from "./generated/ManifestTypes";
import { englishStrings } from "./strings/en";
import { vietnameseStrings } from "./strings/vi";
import { createBpfRepository } from "./repositories";
import type { IBpfRepository } from "./types";
import type { LocalizationStrings } from "./types/LocalizationTypes";

export class ProcessCheckpoint
  implements ComponentFramework.ReactControl<IInputs, IOutputs>
{
  private notifyOutputChanged: () => void;
  private sourceControl: string | undefined;
  private bpfRepository: IBpfRepository | undefined;
  private memoizedStrings: LocalizationStrings | undefined;
  private lastLanguage: string | undefined;
  private lastCustomText: string | null | undefined;
  private languageLoader: (lang: string | number | undefined | null) => LocalizationStrings;
  private advancedMerger: (customStrings: string | Partial<LocalizationStrings> | null, targetLanguage?: string | number | null) => LocalizationStrings;

  constructor() {
    // Create language-aware loader
    this.languageLoader = createLanguageLoader({
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });

    // Create advanced merger with language map
    this.advancedMerger = createAdvancedLocalizationMerger(englishStrings, {
      [LANGUAGES.ENG]: englishStrings,
      [LANGUAGES.VN]: vietnameseStrings
    });
  }

  public init(
    _context: ComponentFramework.Context<IInputs>,
    notifyOutputChanged: () => void,
    _state: ComponentFramework.Dictionary,
  ): void {
    this.notifyOutputChanged = notifyOutputChanged;
  }

  public updateView(
    context: ComponentFramework.Context<IInputs>,
  ): React.ReactElement {
    this.sourceControl = context.parameters.sourceControl?.raw ?? "";
    const lang = context.parameters.language?.raw;
    const enableFiles = context.parameters.enableFileAttachment?.raw ?? false;
    const requireFiles = context.parameters.requireFileAttachment?.raw ?? false;
    const fileTypes =
      context.parameters.allowedFileTypes?.raw ?? DEFAULT_FILE_TYPES_STRING;
    const maxSize =
      context.parameters.maxFileSize?.raw ?? DEFAULT_VALUES.MAX_FILE_SIZE;
    const customTextJson = context.parameters.customText?.raw;

    // Use parameter language first, fallback to user settings language (following LocalizationUtils.Example.ts pattern)
    const language = getValidatedLanguage(lang ?? context.userSettings.languageId);

    if (
      !this.bpfRepository ||
      !this.memoizedStrings ||
      this.lastLanguage !== language ||
      this.lastCustomText !== customTextJson
    ) {
      // Use advanced merger that considers both language and custom overrides
      this.memoizedStrings = this.advancedMerger(customTextJson, language);
      this.lastLanguage = language;
      this.lastCustomText = customTextJson;
      this.bpfRepository = createBpfRepository({}, this.memoizedStrings);
    }
    const SafeApp = withErrorBoundary(
      ProcessCheckpointControl,
      undefined,
      language,
    );

    return React.createElement(SafeApp, {
      sourceControl: this.sourceControl,
      bpfRepository: this.bpfRepository,
      language: language,
      enableFileAttachment: enableFiles,
      requireFileAttachment: requireFiles,
      allowedFileTypes: fileTypes,
      maxFileSize: maxSize,
      customLocalizationStrings: this.memoizedStrings,
    });
  }

  public getOutputs(): IOutputs {
    return {
      sourceControl: this.sourceControl ?? "",
    };
  }

  public destroy(): void {
    // Add code to cleanup control if necessary
  }
}
