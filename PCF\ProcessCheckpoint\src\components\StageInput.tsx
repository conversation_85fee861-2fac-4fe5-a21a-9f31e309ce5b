import * as React from "react";
import { Input } from "@fluentui/react-components";
import { UI_COLORS, UI_CURSORS, ReadOnlyReasonType } from "../types";
import type { LocalizationStrings } from "../types/LocalizationTypes";
import { inputStyle } from "./styles";

interface StageInputProps {
  value: string;
  readOnlyReason: ReadOnlyReasonType | null;
  onClick: (e: React.MouseEvent) => void;
  textPlaceHolder?: string;
  isLoading?: boolean;
  strings: LocalizationStrings;
}

export const StageInput: React.FC<StageInputProps> = ({
  value,
  readOnlyReason,
  onClick,
  textPlaceHolder,
  isLoading,
  strings,
}) => {
  const isReadOnly = readOnlyReason !== null;

  const getDisplayPlaceholder = (): string => {
    if (isLoading) {
      return strings.ui.placeholders.loadingStageData;
    }
    if (!isReadOnly) {
      return textPlaceHolder ?? "";
    }

    if (value && value !== strings.ui.placeholders.loadingStageData) {
      return "";
    }
    switch (readOnlyReason) {
      case ReadOnlyReasonType.NEXT:
        return strings.ui.placeholders.stageNotActive;
      case ReadOnlyReasonType.ERROR:
        return strings.ui.placeholders.errorLoadingStage;
      case ReadOnlyReasonType.PREVIOUS:
      case ReadOnlyReasonType.FINISHED:
      case ReadOnlyReasonType.COMPLETED:
        return strings.ui.placeholders.clickToViewComment;
      default:
        return "";
    }
  };

  const dynamicInputStyle = React.useMemo(() => {
    const isEffectivelyReadOnly = isLoading ?? isReadOnly;
    return {
      ...inputStyle,
      borderBottom: `1px solid ${isEffectivelyReadOnly ? UI_COLORS.border.READ_ONLY : UI_COLORS.text.SECONDARY}`,
      cursor: (readOnlyReason === ReadOnlyReasonType.NEXT
        ? UI_CURSORS.NOT_ALLOWED
        : UI_CURSORS.POINTER) as string,
      color: (isEffectivelyReadOnly
        ? UI_COLORS.text.READ_ONLY
        : UI_COLORS.text.DEFAULT) as string,
    };
  }, [isLoading, isReadOnly, readOnlyReason]);

  return (
    <Input
      value={value}
      style={dynamicInputStyle}
      onClick={onClick}
      placeholder={getDisplayPlaceholder()}
      readOnly
    />
  );
};
