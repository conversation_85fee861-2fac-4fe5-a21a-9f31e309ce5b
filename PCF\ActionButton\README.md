# 📋 ActionButton PCF Control

> **Power Apps Component Framework (PCF) control for dynamic button actions with custom API integration**

---

## 🎯 Overview

**ActionButton** is a PCF control designed for providing dynamic button functionality within a Dynamics 365 form by interacting with custom APIs. This control binds to a single text field and provides a dynamic UI to create, update, or remove values through API calls.

## 🚀 Key Features

- **Dynamic Button Display**: Shows a "Create" button when the bound field is empty; displays "Update" and "Remove" buttons once a value is present.
- **Custom API Integration**: Calls custom APIs for create, update, and remove operations with a structured payload.
- **Fluent UI v9**: Leverages Fluent UI v9 and `makeStyles` for consistent, modern styling.
- **Bound to Single Text Field**: Seamlessly integrates with a single text field in Dynamics 365.
- **Responsive UI**: Updates dynamically based on field value changes (e.g., via form refresh or ribbon updates).
- **Error Handling**: Provides feedback on API call success or failure.

## 🛠️ Integration with Custom APIs

The control interacts with Dynamics 365 custom actions using the standard WebAPI pattern. ActionButton defines its own payload structure:

```json
{
  "Command": "<create | update | remove>",
  "Data": "<Real entity ID from formContext.data.entity.getId()>"
}
```

**Integration Pattern:**

- Uses SharedLibraries `XrmUtility.ActionUtility` for action execution
- SharedLibraries provides flexible `ActionPayload` interface for different projects
- ActionButton defines `ActionButtonPayload` with Command/Data structure
- Real entity ID obtained from `EntityUtils.getEntityId()` (SharedLibraries)
- Follows Dynamics 365 `Xrm.WebApi.online.execute()` pattern with proper metadata

### API Endpoints

- **Create API**: Generates a new value and updates the bound field.
- **Update API**: Modifies the existing value or related data.
- **Remove API**: Clears the value from the bound field.

## 🏗️ Technical Architecture

This control implements a **React Functional Component** with a 3-layer architecture for clean separation of concerns:

- **Presentation Layer**: Renders Fluent UI v9 buttons styled with `makeStyles`.
- **Logic Layer**: Handles API calls, payload construction, and field value monitoring.
- **Data Layer**: Manages interaction with the Dynamics 365 context and bound field.

Key benefits include modularity, maintainability, and seamless integration with Dynamics 365.

## 📁 Project Structure

```txt
ActionButton/
├── src/
│   ├── components/
│   │   └── DynamicButtonComponent.tsx  # Main React component with dynamic button logic
│   ├── generated/
│   │   └── ManifestTypes.d.ts         # Auto-generated types from ControlManifest.Input.xml
│   ├── ControlManifest.Input.xml      # Control manifest configuration
│   └── index.ts                       # PCF control entry point
├── package.json                       # Dependencies and scripts
├── tsconfig.json                      # TypeScript configuration
└── README.md                          # This documentation
```

## 🔧 Configuration & Deployment

Add the control to a single text field on your Dynamics 365 form and configure the properties in this order:

### Property Configuration

1. **`sourceControl` (bound, required)**:
   - Binds to a single text field to store the control value.
   - Ensure the field is of type "Single Line of Text" in Dynamics 365.

2. **`apiEndpoint` (input, optional)**:
   - Specifies the custom API endpoint base URL (if not hardcoded).
   - Default: Uses predefined API endpoints in the control.

### PCF Manifest Properties

```xml
<!-- Core Properties -->
<property name="sourceControl" display-name-key="Data Field" usage="bound" required="true" />
<property name="apiEndpoint" display-name-key="API Endpoint" usage="input" required="false" />
```

### Configuration Examples

#### Basic Configuration

```javascript
sourceControl: "new_actionfield"
apiEndpoint: ""
```

#### Advanced Configuration

```javascript
sourceControl: "new_actionfield"
apiEndpoint: "https://yourorg.api.crm.dynamics.com/api/data/v9.2/"
```

## 📋 Usage Scenarios

### **Scenario 1: Creating a New Value**

**Setup:**

- Add the ActionButton control to a single text field (e.g., `new_actionfield`).
- Ensure the field is empty initially.
- Configure the form to allow refresh on field changes.

**Workflow:**

1. **Display Create Button** → The control shows a "Create" button when the field is empty.
2. **Click Create** → Triggers a custom API call with `action: "create"`.
3. **Value Updated** → The API updates the field with a new value, and the UI refreshes to show "Update" and "Remove" buttons.

### **Scenario 2: Updating or Removing a Value**

**Setup:**

- The `new_actionfield` contains an existing value.
- The control is added to the field.

**Workflow:**

1. **Display Update/Remove Buttons** → The control shows "Update" and "Remove" buttons.
2. **Click Update** → Triggers a custom API call with `action: "update"`.
3. **Click Remove** → Triggers a custom API call with `action: "remove"`.

## 🚀 How to Use

### End User Experience

#### Creating a Value

1. **Click Create Button** → Initiates API call to generate a value.
2. **API Success** → Field updates with new value; UI shows "Update" and "Remove" buttons.
3. **API Failure** → Displays error message (e.g., "Failed to create value").

#### Updating or Removing a Value

1. **Click Update Button** → Initiates API call to update the value or related data.
2. **Click Remove Button** → Clears the value from the field and refreshes the UI to show "Create" button.

## 📚 Additional Resources

- [Rule.md](../../../Rule.md) - Comprehensive PCF development guidelines and patterns
- [Setup.md](../../../Setup.md) - Project setup and initialization guide
- [SharedLibraries](../../../SharedLibraries/) - Reusable utilities and components
- [Fluent UI v9 Documentation](https://react.fluentui.dev/) - Official Fluent UI documentation

## 🎉 Conclusion

The ActionButton PCF Control provides a seamless way to manage dynamic button actions in Dynamics 365 forms through custom API integration.

**Key Benefits:**

- 🎯 **Dynamic UI** with conditional button rendering based on field state.
- 📝 **Custom API Integration** for flexible action management.
- 🔄 **Responsive Updates** via form refresh or ribbon updates.
- 📎 **Fluent UI v9 Styling** for a modern, consistent look.
- 🌐 **TypeScript Support** for robust development.
- 🛡️ **Error Handling** for reliable user feedback.