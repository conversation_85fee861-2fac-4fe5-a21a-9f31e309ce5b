import * as React from "react";
import { COMPONENT_CONSTANTS, CSS_CONSTANTS, HTML_CONSTANTS } from "../types/Constants";

const IconWrapper = ({ children }: { children: React.ReactNode }) => (
  <div
    style={{
      width: "24px",
      height: "24px",
      flexShrink: 0,
      display: HTML_CONSTANTS.display.flex,
      alignItems: CSS_CONSTANTS.alignItems.center,
      justifyContent: CSS_CONSTANTS.justifyContent.center,
    }}
  >
    {children}
  </div>
);

interface IconProps {
  title?: string;
  color?: string;
}

// --- File Type Icons ---
export const DocumentIcon = ({ title = "Document", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM13 9V3.5L18.5 9H13z" />
    </svg>
  </IconWrapper>
);

export const PdfIcon = ({ title = "PDF Document", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM9.5 15.5H8V13h1.5v2.5zm3.5-1c0 .83-.67 1.5-1.5 1.5h-1.5v-4H12c.83 0 1.5.67 1.5 1.5v1zm-1.5-2.5h-1v1h1v-1zm4.5 2.5h-1.5V13H16v2.5z" />
    </svg>
  </IconWrapper>
);

export const TextIcon = ({ title = "Text Document", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM15 18H9v-2h6v2zm0-4H9v-2h6v2zm-2-4V3.5L18.5 9H13z" />
    </svg>
  </IconWrapper>
);

export const ImageIcon = ({ title = "Image", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
    </svg>
  </IconWrapper>
);

export const VideoIcon = ({ title = "Video", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z" />
    </svg>
  </IconWrapper>
);

export const AudioIcon = ({ title = "Audio", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z" />
    </svg>
  </IconWrapper>
);

export const ArchiveIcon = ({ title = "Archive", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M20.54 5.23l-1.39-1.68C18.88 3.21 18.47 3 18 3H6c-.47 0-.88.21-1.16.55L3.46 5.23C3.17 5.57 3 6.02 3 6.5V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6.5c0-.48-.17-.93-.46-1.27zM12 17.5L6.5 12H10v-2h4v2h3.5L12 17.5zM5.12 5l.81-1h12l.94 1H5.12z" />
    </svg>
  </IconWrapper>
);

export const AttachIcon = ({ title = "Attach File", color = "currentColor" }: IconProps) => (
  // biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code
  <svg
    width="16"
    height="16"
    xmlns={COMPONENT_CONSTANTS.svg.xmlns}
    viewBox="0 0 16 16"
    fill={color}
    aria-label={title}
  >
    <path d="M11.5 1a3.5 3.5 0 0 1 3.5 3.5v7a3.5 3.5 0 0 1-7 0v-6a2.5 2.5 0 0 1 5 0v5.5a.5.5 0 0 1-1 0v-5.5a1.5 1.5 0 0 0-3 0v6a2.5 2.5 0 0 0 5 0v-7A2.5 2.5 0 0 0 11.5 2a2.5 2.5 0 0 0-2.5 2.5v7a3.5 3.5 0 0 1-7 0v-7a.5.5 0 0 1 1 0v7a2.5 2.5 0 0 0 5 0v-7A3.5 3.5 0 0 1 11.5 1z" />
  </svg>
);

export const DeleteIcon = ({ title = "Delete", color = "currentColor" }: IconProps) => (
  // biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code
  <svg
    width="16"
    height="16"
    xmlns={COMPONENT_CONSTANTS.svg.xmlns}
    viewBox="0 0 16 16"
    fill={color}
    aria-label={title}
  >
    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z" />
    <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z" />
  </svg>
);

export const DownloadIcon = ({ title = "Download", color = "currentColor" }: IconProps) => (
  // biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code
  <svg
    width="16"
    height="16"
    xmlns={COMPONENT_CONSTANTS.svg.xmlns}
    viewBox="0 0 16 16"
    fill={color}
    aria-label={title}
  >
    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z" />
    <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z" />
  </svg>
);

export const DefaultFileIcon = ({ title = "File", color = "currentColor" }: IconProps) => (
  <IconWrapper>
    {/* biome-ignore lint/a11y/noSvgWithoutTitle: Library shared code */}
    <svg
      width="24"
      height="24"
      xmlns={COMPONENT_CONSTANTS.svg.xmlns}
      viewBox="0 0 24 24"
      fill={color}
      aria-label={title}
    >
      <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM13 9V3.5L18.5 9H13z" />
    </svg>
  </IconWrapper>
);

// File type detection utility
export const getFileIcon = (fileName: string, title?: string, color?: string) => {
  const extension = fileName.toLowerCase().split('.').pop() || '';

  switch (extension) {
    case 'pdf':
      return <PdfIcon title={title} color={color} />;
    case 'txt':
    case 'md':
    case 'rtf':
      return <TextIcon title={title} color={color} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
    case 'webp':
      return <ImageIcon title={title} color={color} />;
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'flv':
    case 'webm':
      return <VideoIcon title={title} color={color} />;
    case 'mp3':
    case 'wav':
    case 'flac':
    case 'aac':
    case 'ogg':
      return <AudioIcon title={title} color={color} />;
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return <ArchiveIcon title={title} color={color} />;
    case 'doc':
    case 'docx':
    case 'odt':
      return <DocumentIcon title={title} color={color} />;
    default:
      return <DefaultFileIcon title={title} color={color} />;
  }
};
