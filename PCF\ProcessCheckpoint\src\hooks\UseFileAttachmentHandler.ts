import * as React from "react";
import {
  DEFAULT_VALUES,
  FILE_CONSTANTS,
  KEYBOARD_KEYS,
  type AttachedFile,
  type FileAttachmentConfig,
  type ITimelineRepository,
} from "../types";
import type { LocalizationStrings } from "../types/LocalizationTypes";

interface UseFileAttachmentHandlerProps {
  files: AttachedFile[];
  onFilesChange: (files: AttachedFile[]) => void;
  config: FileAttachmentConfig;
  readOnly: boolean;
  timelineRepository: ITimelineRepository | null;
  onValidationError?: (error: string) => void;
  strings: LocalizationStrings;
}

interface FileAttachmentState {
  dragActive: boolean;
}

interface FileAttachmentAction { type: "SET_DRAG_ACTIVE"; payload: boolean };

const fileAttachmentReducer = (
  state: FileAttachmentState,
  action: FileAttachmentAction,
): FileAttachmentState => {
  switch (action.type) {
    case "SET_DRAG_ACTIVE":
      return { ...state, dragActive: action.payload };
    default:
      return state;
  }
};

export const useFileAttachmentHandler = ({
  files,
  onFilesChange,
  config,
  readOnly,
  timelineRepository,
  onValidationError,
  strings,
}: UseFileAttachmentHandlerProps) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [state, dispatch] = React.useReducer(fileAttachmentReducer, {
    dragActive: false,
  });

  const formatFileSize = React.useCallback((bytes: number): string => {
    if (bytes === 0) return strings.ui.fileAttachment.fileSizeUnits.zeroBytes;
    const k = FILE_CONSTANTS.BYTES_PER_KB;
    const sizes = [
      strings.ui.fileAttachment.fileSizeUnits.bytes,
      strings.ui.fileAttachment.fileSizeUnits.kb,
      strings.ui.fileAttachment.fileSizeUnits.mb,
      strings.ui.fileAttachment.fileSizeUnits.gb,
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(FILE_CONSTANTS.FILE_SIZE_DECIMAL_PLACES))} ${sizes[i]}`;
  }, [strings]);

  const validateFile = React.useCallback((file: File): string | null => {
    const maxSizeBytes = config.maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return strings.messages.error.fileSizeExceeded.replace(
        "{0}",
        config.maxSizeInMB.toString(),
      );
    }

    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    if (!fileExtension || !config.allowedTypes.includes(fileExtension)) {
      return strings.messages.error.fileTypeNotAllowed;
    }

    return null;
  }, [config, strings]);

  const handleFileSelect = React.useCallback((selectedFiles: FileList) => {
    if (selectedFiles.length === 0) {
      return;
    }

    const file = selectedFiles[0]; // Only process the first file for single attachment
    const validationError = validateFile(file);

    if (validationError) {
      if (onValidationError) {
        onValidationError(validationError);
      }
      return;
    }

    const reader = new FileReader();

    reader.onload = () => {
      const base64Content = reader.result as string;
      const content = base64Content.split(",")[1];
      const updatedFile: AttachedFile = {
        id: DEFAULT_VALUES.GUID_EMPTY,
        name: file.name,
        size: file.size,
        type: file.type,
        content: content,
        uploadDate: new Date(),
      };
      onFilesChange([updatedFile]); // Replace existing file(s) with the new one
    };

    reader.onerror = () => {
      onValidationError?.(strings.messages.error.fileUploadFailed);
      onFilesChange([]); // Clear files on error
    }

   reader.readAsDataURL(file);
  }, [validateFile, onValidationError, onFilesChange, strings]);

  const handleDrop = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    dispatch({ type: "SET_DRAG_ACTIVE", payload: false });

    if (readOnly) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  }, [readOnly, handleFileSelect]);

  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!readOnly) {
      dispatch({ type: "SET_DRAG_ACTIVE", payload: true });
    }
  }, [readOnly]);

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    dispatch({ type: "SET_DRAG_ACTIVE", payload: false });
  }, []);

  const handleBrowseClick = React.useCallback(() => {
    if (!readOnly && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [readOnly]);

  const handleKeyDown = React.useCallback((e: React.KeyboardEvent) => {
    if (e.key === KEYBOARD_KEYS.ENTER || e.key === KEYBOARD_KEYS.SPACE) {
      e.preventDefault();
      handleBrowseClick();
    }
  }, [handleBrowseClick]);

  const handleFileInputChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileSelect(selectedFiles);
    }
    e.target.value = "";
  }, [handleFileSelect]);

  const handleRemoveFile = React.useCallback((fileId: string) => {
    if (!readOnly) {
      const updatedFiles = files.filter((file) => file.id !== fileId);
      onFilesChange(updatedFiles);
    }
  }, [readOnly, files, onFilesChange]);

  const downloadFile = React.useCallback((dataUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = dataUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  const handleDownloadFile = React.useCallback((file: AttachedFile) => {
    if (file.id === DEFAULT_VALUES.GUID_EMPTY) {
      const fileType = file.type ?? "application/octet-stream";
      downloadFile(`data:${fileType};base64,${file.content ?? ""}`, file.name);
    } else {
      if (timelineRepository) {
        void timelineRepository
          .getAnnotationDocumentBody(file.id)
          .then((result) => {
            if (result.success && result.data) {
              const { documentbody, filename, mimetype } = result.data;
              downloadFile(`data:${mimetype};base64,${documentbody}`, filename);
            } else if (onValidationError) {
              onValidationError(
                result.error ?? strings.messages.error.operationFailed,
              );
            }
            return;
          })
          .catch((_error) => {
            if (onValidationError) {
              onValidationError(strings.messages.error.operationFailed);
            }
          });
      }
   }
  }, [downloadFile, timelineRepository, onValidationError, strings]);

  return {
    fileInputRef,
    dragActive: state.dragActive,
    handleDrop,
    handleDragOver,
    handleDragLeave,
    handleBrowseClick,
    handleKeyDown,
    handleFileInputChange,
    handleRemoveFile,
    handleDownloadFile,
    formatFileSize,
    validateFile,
  };

};