import type { LocalizationStrings } from "../types/LocalizationTypes";
import type {
  BpfStageInfo,
  BpfStatusType,
} from "../types";
import type {
  BpfCollection,
  BpfStage,
  EntityInfo,
  IBpfRepository,
  RepositoryOptions,
  RepositoryResult,
} from "../../../SharedLibraries/types/RepositoryTypes";
import {
  getProcessStatus,
  setProcessStatus,
  getSelectedStage,
  getActiveStage,
  getActivePath,
  moveNextStage,
  movePreviousStage,
  getEntityId,
  getEntityLogicalName,
  getEntityMetadata,
} from "../../../SharedLibraries/utils/XrmUtility";

export class BpfRepository implements IBpfRepository {
  private readonly options: RepositoryOptions;
  private strings: LocalizationStrings;

  constructor(options: RepositoryOptions = {}, strings: LocalizationStrings) {
    this.options = {
      timeout: 5000,
      retryCount: 3,
      ...options,
    };
    this.strings = strings;
  }

  private handleRepositoryError<T>(
    error: unknown,
    defaultMessage: string,
  ): RepositoryResult<T> {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: errorMessage || defaultMessage };
  }

  // Helper method to ensure we have a Promise
  private ensurePromise<T>(fn: () => T | Promise<T>): Promise<T> {
    try {
      const result = fn();
      return Promise.resolve(result);
    } catch (error) {
      const errorObj =
        error instanceof Error ? error : new Error(String(error));
      return Promise.reject(errorObj);
    }
  }

  public getStatusProcess(): string | null {
    try {
      return getProcessStatus();
    } catch (_error) {
      // This is a low-level Xrm error, which will be caught by the higher-level handleRepositoryError
      return null;
    }
  }

  public setStatusProcess(
    status: BpfStatusType,
  ): Promise<RepositoryResult<boolean>> {
    return this.ensurePromise(() => setProcessStatus(status))
      .then(() => ({ success: true, data: true }))
      .catch((error) =>
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
  }

  public getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>> {
    try {
      const collection = getActivePath();

      if (!collection) {
        return Promise.resolve(
          this.handleRepositoryError(
            null,
            this.strings.errors.general.missingBpfStagesCollection,
          ),
        );
      }

      const stages = this.extractStagesFromCollection(collection);
      const stageInfos = stages.map((stage, index) => ({
        stageId: stage.getId(),
        stageName: stage.getName(),
        isFirstStage: index === 0,
        isLastStage: index === stages.length - 1,
        canMoveNext: index < stages.length - 1,
        canMovePrevious: index > 0,
      }));

      return Promise.resolve({
        success: true,
        data: stageInfos,
      });
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public getCurrentStageInfo(): BpfStageInfo | null {
    return this.getStageInfoInternal("active");
  }

  public getSelectedStageInfo(): BpfStageInfo | null {
    return this.getStageInfoInternal("selected");
  }

  public getStageInfoById(stageId: string): BpfStageInfo | null {
    return this.getStageInfoInternal("id", stageId);
  }

  public moveToNextStage(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      if (!stage?.canMoveNext) {
        return Promise.resolve(
          this.handleRepositoryError(
            null,
            this.strings.errors.general.cannotMoveNextStage,
          ),
        );
      }

      return this.ensurePromise(() => moveNextStage())
        .then(() => ({ success: true, data: true }))
        .catch((error) =>
          this.handleRepositoryError(
            error,
            this.strings.errors.general.unknownError,
          ),
        );
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public moveToPreviousStage(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      if (!stage?.canMovePrevious) {
        return Promise.resolve(
          this.handleRepositoryError(
            null,
            this.strings.errors.general.cannotMovePreviousStage,
          ),
        );
      }

      return this.ensurePromise(() => movePreviousStage())
        .then(() => ({ success: true, data: true }))
        .catch((error) =>
          this.handleRepositoryError(
            error,
            this.strings.errors.general.unknownError,
          ),
        );
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public canMoveToNext(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      return Promise.resolve({
        success: true,
        data: stage?.canMoveNext ?? false,
      });
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public canMoveToPrevious(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      return Promise.resolve({
        success: true,
        data: stage?.canMovePrevious ?? false,
      });
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public isFirstStage(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      return Promise.resolve({
        success: true,
        data: stage?.isFirstStage ?? false,
      });
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public isLastStage(): Promise<RepositoryResult<boolean>> {
    try {
      const stage = this.getCurrentStageInfo();
      return Promise.resolve({
        success: true,
        data: stage?.isLastStage ?? false,
      });
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  public getEntityInfo(): Promise<RepositoryResult<EntityInfo>> {
    try {
      const entityId = getEntityId().replace(/[{}]/g, "");
      const entityName = getEntityLogicalName();

      if (!entityId || !entityName) {
        return Promise.resolve(
          this.handleRepositoryError<EntityInfo>(
            null,
            this.strings.errors.general.entityInfoIncomplete,
          ),
        );
      }

      return this.ensurePromise(() => getEntityMetadata(entityName))
        .then((meta) => {
          if (!meta?.EntitySetName) {
            return this.handleRepositoryError<EntityInfo>(
              null,
              this.strings.errors.general.entityMetadataIncomplete,
            );
          }

          const entityInfo: EntityInfo = {
            entityId,
            entityLogicalName: entityName,
            entitySetName: meta.EntitySetName,
            objectTypeCode: meta.ObjectTypeCode ?? 0,
          };

          return {
            success: true,
            data: entityInfo,
          };
        })
        .catch((error) =>
          this.handleRepositoryError<EntityInfo>(
            error,
            this.strings.errors.general.unknownError,
          ),
        );
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError<EntityInfo>(
          error,
          this.strings.errors.general.unknownError,
        ),
      );
    }
  }

  private getStageInfoInternal(
    type: "active" | "selected" | "id",
    stageId?: string,
  ): BpfStageInfo | null {
    try {
      let targetStage: BpfStage | null = null;
      const collection = getActivePath();

      switch (type) {
        case "active":
          targetStage = getActiveStage();
          break;
        case "selected":
          targetStage = getSelectedStage();
          break;
        case "id":
          if (!stageId) return null;
          if (collection) {
            targetStage =
              this.extractStagesFromCollection(collection).find(
                (s) => s.getId() === stageId,
              ) ?? null;
          }
          break;
      }

      if (!targetStage || !collection) {
        return null;
      }

      const stages = this.extractStagesFromCollection(collection);
      if (stages.length === 0) {
        return null;
      }

      const index = stages.findIndex(
        (stage) => stage.getId() === targetStage.getId(),
      );

      if (index === -1) {
        return null;
      }

      return {
        stageId: targetStage.getId(),
        stageName: targetStage.getName(),
        isFirstStage: index === 0,
        isLastStage: index === stages.length - 1,
        canMoveNext: index < stages.length - 1,
        canMovePrevious: index > 0,
      };
    } catch {
      return null;
    }
  }

  private extractStagesFromCollection(collection: BpfCollection): BpfStage[] {
    let stages: BpfStage[] = [];

    try {
      if (typeof collection.getAll === "function") {
        const allStages = collection.getAll();
        if (Array.isArray(allStages)) {
          return allStages;
        }
      }

      if (typeof collection.forEach === "function") {
        collection.forEach((stage: BpfStage) => {
          if (stage) {
            stages.push(stage);
          }
        });
        return stages;
      }

      if (
        typeof collection.getLength === "function" &&
        typeof collection.getByIndex === "function"
      ) {
        const length = collection.getLength();
        for (let i = 0; i < length; i++) {
          const stage = collection.getByIndex(i);
          if (stage) {
            stages.push(stage);
          }
        }
        return stages;
      }
    } catch (_error) {
      stages = [];
    }

    return stages;
  }
}
