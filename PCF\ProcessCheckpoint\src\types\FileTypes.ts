// Re-export shared file types for backward compatibility
export type {
  AttachedFile,
  FileAttachmentConfig,
} from "../../../SharedLibraries/types/FileTypes";

export {
  DEFAULT_FILE_CONFIG,
  DEFAULT_FILE_TYPES_STRING,
  parseFileTypes,
  validateFile,
  formatFileSize,
  fileToBase64,
  createAttachedFile,
  generateFileId,
  getFileExtension,
  getMimeType,
  isImageFile,
  isDocumentFile,
  isArchiveFile,
} from "../../../SharedLibraries/types/FileTypes";

// Add project-specific file types here if needed
