/**
 * ComboboxControl Localization Strings - English
 * Follows LocalizationUtils pattern for consistency
 */

import type { LocalizationStrings } from "../types/LocalizationTypes";

export const englishStrings: LocalizationStrings = {
  ui: {
    labels: {
      attribute: 'Select an attribute',
      stringAttribute: 'Select a Single line of text attribute',
      picklistAttribute: 'Select a Choice attribute',
      dateTimeAttribute: 'Select a Date and time attribute',
      statuscode: 'Select a status reason',
      statecode: 'Select a state',
      error: 'Error:',
    },
    messages: {
      loading: 'Loading...',
      selectBPF: 'Please select a Business Process Flow.',
    },
  },
  errors: {
    statecodeNotConfigured: 'Statecode field is not configured.',
    permissionDenied: 'Permission denied: You do not have access to this resource.',
    timeout: 'Request timed out after 10 seconds.',
    attributeNotFound: 'Attribute {fieldType} not found for entity {entityName}.',
    optionsLoadFailed: 'Failed to load options: {error}.',
    bpfMissingPrimaryEntity: 'BPF {bpfId} does not have a primary entity defined.',
    bpfNotFound: 'Business Process Flow with ID {bpfId} not found.',
    entityNotFound: 'Entity {entityName} not found.',
  },
  constants: {
    // PCF Compliance constants - moved from SharedLibraries/Constants
    workflow: 'workflow',
    selectPrimaryEntity: '?$select=primaryentity',
    displayNameFormat: 'Display Name (logicalname)',
    freeformKey: 'freeform',
    alertRole: 'alert',
  },
};


