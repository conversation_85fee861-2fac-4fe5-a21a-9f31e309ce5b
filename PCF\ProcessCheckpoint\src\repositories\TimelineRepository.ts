import type { LocalizationStrings } from "../types/LocalizationTypes";
import type {
  BpfActionName,
} from "../types";
import type {
  AttachedFile,
  DynamicsNote,
  RetrieveMultipleResponse,
  EntityInfo,
  ITimelineRepository,
  RepositoryOptions,
  RepositoryResult,
  TimelineConfig,
  TimelineNote,
} from "../../../SharedLibraries/types/RepositoryTypes";
import {
  BPF_NOTE_ACTIONS,
  VALIDATION,
} from "../types";
import {
  DEFAULT_TIMELINE_CONFIG,
} from "../../../SharedLibraries/types/RepositoryTypes";
import {
  createRecord,
  retrieveMultipleRecords,
  retrieveRecord,
} from "../../../SharedLibraries/utils/XrmUtility";

interface NoteData {
  subject: string;
  notetext: string;
  [key: string]: unknown;
}

export class TimelineRepository implements ITimelineRepository {
  public readonly options: RepositoryOptions;
  private entityInfo: EntityInfo;
  private timelineConfig: TimelineConfig;
  private strings: LocalizationStrings;

  constructor(
    entityInfo: EntityInfo,
    timelineConfig: TimelineConfig = DEFAULT_TIMELINE_CONFIG,
    options: RepositoryOptions = {},
    strings: LocalizationStrings,
  ) {
    this.entityInfo = entityInfo;
    this.timelineConfig = timelineConfig;
    this.options = {
      timeout: 5000,
      retryCount: 3,
      ...options,
    };
    this.strings = strings;
  }

  private handleRepositoryError<T>(
    error: unknown,
    defaultMessage = this.strings.errors.general.unknownError,
  ): RepositoryResult<T> {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: errorMessage || defaultMessage };
  }

  public createNote(
    entityId: string,
    subject: string,
    noteText: string,
    attachedFiles?: AttachedFile[],
  ): Promise<RepositoryResult<string>> {
    try {
      const noteData = this.buildNoteData(
        entityId,
        subject,
        noteText,
        attachedFiles?.[0],
      );

      return this.executeCreateNote(noteData)
        .then((result) => ({
          success: result.success,
          data: result.data,
        }))
        .catch((error) => this.handleRepositoryError(error));
    } catch (error) {
      return Promise.resolve(this.handleRepositoryError(error));
    }
  }

  public getNotesBySubject(
    entityId: string,
    subjectPattern: string,
  ): Promise<RepositoryResult<TimelineNote[]>> {
    try {
      const fetchXml = this.buildSubjectFetchXml(entityId, subjectPattern);

      return this.executeFetchXml(fetchXml)
        .then((result) => {
          if (!result?.entities || !Array.isArray(result.entities)) {
            return {
              success: true,
              data: [],
            };
          }

          const timelineNotes = result.entities.map((note) =>
            this.convertToTimelineNote(note),
          );

          return {
            success: true,
            data: timelineNotes,
          };
        })
        .catch((error) => this.handleRepositoryError(error));
    } catch (error) {
      return Promise.resolve(this.handleRepositoryError(error));
    }
  }

  public createStageNote(
    stageId: string,
    comment: string,
    action: BpfActionName,
    attachedFiles?: AttachedFile[],
  ): Promise<RepositoryResult<string>> {
    try {
      const subject = this.generateStageSubject(
        stageId,
        action,
        attachedFiles?.[0],
      );
      const noteText =
        attachedFiles?.length && comment === ""
          ? `${this.strings.ui.fileAttachment.fileAttachmentPrefix} ${attachedFiles[0].name}`
          : comment;
      return this.createNote(
        this.entityInfo.entityId,
        subject,
        noteText,
        attachedFiles,
      );
    } catch (error) {
      return Promise.resolve(this.handleRepositoryError(error));
    }
  }

  public getLatestStageNote(
    stageId: string,
  ): Promise<RepositoryResult<TimelineNote | null>> {
    try {
      return this.getStageNotes(stageId)
        .then((result) => {
          if (!result.success || !result.data || result.data.length === 0) {
            return {
              success: true,
              data: null,
            };
          }

          const latest = result.data[0];

          // Return the entire latest note object
          return { success: true, data: latest };
        })
        .catch((error) => this.handleRepositoryError(error));
    } catch (error) {
      return Promise.resolve(this.handleRepositoryError(error));
    }
  }

  public getStageNotes(
    stageId: string,
  ): Promise<RepositoryResult<TimelineNote[]>> {
    try {
      const shortId = this.getShortStageId(stageId);
      const subjectPattern = `${this.timelineConfig.subjectPrefix}_${shortId}_%`;

      return this.getNotesBySubject(this.entityInfo.entityId, subjectPattern);
    } catch (error) {
      return Promise.resolve(this.handleRepositoryError(error));
    }
  }

  public getAnnotationDocumentBody(annotationId: string): Promise<
    RepositoryResult<{
      documentbody: string;
      filename: string;
      mimetype: string;
    }>
  > {
    try {
      return retrieveRecord(
        "annotation",
        annotationId,
        "?$select=documentbody,filename,mimetype",
      )
        .then((result) => {
          const note = result as DynamicsNote;
          if (!note.documentbody) {
            return {
              success: false,
              error: this.strings.messages.error.fileUploadFailed,
            };
          }
          return {
            success: true,
            data: {
              documentbody: note.documentbody,
              filename: note.filename ?? "",
              mimetype: note.mimetype ?? "application/octet-stream",
            },
          };
        })
        .catch((error) =>
          this.handleRepositoryError<{
            documentbody: string;
            filename: string;
            mimetype: string;
          }>(error, this.strings.messages.error.fileUploadFailed),
        );
    } catch (error) {
      return Promise.resolve(
        this.handleRepositoryError<{
          documentbody: string;
          filename: string;
          mimetype: string;
        }>(error, this.strings.messages.error.fileUploadFailed),
      );
    }
  }

  // Private helper methods
  private buildNoteData(
    entityId: string,
    subject: string,
    noteText: string,
    attachedFile?: AttachedFile,
  ): NoteData {
    const noteData: NoteData = {
      subject,
      notetext: noteText,
      [`objectid_${this.entityInfo.entityLogicalName}@odata.bind`]: `/${this.entityInfo.entitySetName}(${entityId})`,
    };

    // Add file attachment data if provided
    if (attachedFile) {
      noteData.filename = attachedFile.name;
      noteData.documentbody = attachedFile.content;
      noteData.mimetype = attachedFile.type;
      noteData.filesize = attachedFile.size;
    }

    return noteData;
  }

  private generateStageSubject(
    stageId: string,
    action: string,
    attachedFile?: AttachedFile,
  ): string {
    const shortId = this.getShortStageId(stageId);
    const baseSubject = `${this.timelineConfig.subjectPrefix}_${shortId}_${action}`;

    return attachedFile ? `${baseSubject} - ${attachedFile.name}` : baseSubject;
  }

  private getShortStageId(stageId: string): string {
    return stageId.substring(0, VALIDATION.STAGE_ID_SHORT_LENGTH);
  }

  private buildSubjectFetchXml(
    entityId: string,
    subjectPattern: string,
  ): string {
    return `<fetch version="1.0" output-format="xml-platform" mapping="logical" distinct="false">
            <entity name="annotation">
                <attribute name="annotationid" />
                <attribute name="subject" />
                <attribute name="notetext" />
                <attribute name="createdon" />
                <attribute name="isdocument" />
                <attribute name="documentbody" />
                <attribute name="filename" />
                <attribute name="filesize" />
                <attribute name="mimetype" />
                <order attribute="createdon" descending="true" />
                <filter type="and">
                    <condition attribute="subject" operator="like" value="${subjectPattern}"/>
                    <condition attribute="objecttypecode" operator="eq" value="${this.entityInfo.objectTypeCode}"/>
                    <condition attribute="objectid" operator="eq" value="${entityId}"/>
                </filter>
            </entity>
        </fetch>`;
  }

  private convertToTimelineNote(note: DynamicsNote): TimelineNote {
    const subject = note.subject ?? "";
    const parts = subject.split("_");
    let action: BpfActionName | undefined;
    let stageId: string | undefined;

    if (parts.length >= 3 && parts[0] === this.timelineConfig.subjectPrefix) {
      stageId = parts[1];
      const actionPart = parts[2].split(" ")[0];
      if (BPF_NOTE_ACTIONS.includes(actionPart as BpfActionName)) {
        action = actionPart as BpfActionName;
      }
    }

    const timelineNote: TimelineNote = {
      noteId: note.annotationid,
      subject: subject,
      noteText: note.notetext ?? "",
      createdOn: new Date(note.createdon ?? Date.now()),
      action: action,
      stageId: stageId,
    };

    if (note.filename) {
      timelineNote.attachments = [
        {
          id: note.annotationid,
          name: note.filename,
          size: note.filesize ?? 0,
          type: note.mimetype ?? "application/octet-stream",
          content: note.documentbody ?? "",
          uploadDate: new Date(note.createdon ?? Date.now()),
        },
      ];
    }

    return timelineNote;
  }

  private executeCreateNote(
    noteData: NoteData,
  ): Promise<RepositoryResult<string>> {
    return createRecord("annotation", noteData)
      .then((result) => ({
        success: true,
        data: result.id,
      }))
      .catch((error) => {
        return this.handleRepositoryError(
          error,
          this.strings.messages.error.noteCreationFailed,
        );
      });
  }

  private executeFetchXml(
    fetchXml: string,
  ): Promise<RetrieveMultipleResponse<DynamicsNote>> {
    const encoded = encodeURIComponent(fetchXml);
    return retrieveMultipleRecords(
      "annotation",
      `?fetchXml=${encoded}`,
    ) as Promise<RetrieveMultipleResponse<DynamicsNote>>;
  }
}
