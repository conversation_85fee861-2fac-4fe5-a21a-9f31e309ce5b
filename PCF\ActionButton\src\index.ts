/**
 * ActionButton PCF Control Entry Point
 * Following SharedLibraries patterns and PCF-CodingRules.md standards
 */

import type { IInputs, IOutputs } from './generated/ManifestTypes';
import * as React from 'react';
import {
    createLanguageLoader,
    LANGUAGES,
    getValidatedLanguage,
    detectSystemLanguage,
    type SupportedLanguage
} from "../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from './strings/en';
import { vietnameseStrings } from './strings/vi';
import type { LocalizationStrings } from './types/LocalizationTypes';
import { DynamicButtonComponent } from './components/DynamicButtonComponent';

/**
 * ActionButton PCF Control Implementation
 * Provides dynamic button functionality with API integration
 * Following SharedLibraries LocalizationUtils patterns
 */
export class ActionButton implements ComponentFramework.ReactControl<IInputs, IOutputs> {
    private _notifyOutputChanged: () => void;
    private _value: string | null;
    private _strings: LocalizationStrings;
    private _languageLoader: (lang: string | number | undefined | null) => LocalizationStrings;
    private _currentLanguage: SupportedLanguage;

    constructor() {
        // Create language-aware loader following SharedLibraries pattern
        this._languageLoader = createLanguageLoader({
            [LANGUAGES.ENG]: englishStrings,
            [LANGUAGES.VN]: vietnameseStrings
        }) as (lang: string | number | undefined | null) => LocalizationStrings;

        // Initialize with system language detection
        this._currentLanguage = detectSystemLanguage();
        this._strings = this._languageLoader(this._currentLanguage);
    }

    /**
     * Initialize the control
     * @param context - PCF context
     * @param notifyOutputChanged - Callback to notify framework of changes
     * @param state - Control state
     */
    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        _state: ComponentFramework.Dictionary
    ): void {
        this._notifyOutputChanged = notifyOutputChanged;
        this._value = context.parameters.sourceControl.raw || null;

        // Update language based on context
        const contextLanguage = getValidatedLanguage(context.userSettings.languageId);
        this._currentLanguage = contextLanguage;
        this._strings = this._languageLoader(contextLanguage) as LocalizationStrings;
    }

    /**
     * Update the view with new data
     * @param context - PCF context
     * @returns React element
     */
    public updateView(context: ComponentFramework.Context<IInputs>): React.ReactElement {
        // Update internal value
        this._value = context.parameters.sourceControl.raw || null;

        // Check if language changed and update strings
        const contextLanguage = getValidatedLanguage(context.userSettings.languageId);
        if (contextLanguage !== this._currentLanguage) {
            this._currentLanguage = contextLanguage;
            this._strings = this._languageLoader(contextLanguage) as LocalizationStrings;
        }

        // Return React component with localized strings
        return React.createElement(DynamicButtonComponent, {
            context,
            notifyOutputChanged: this._notifyOutputChanged,
            sourceControlValue: this._value,
            strings: this._strings, // Pass localized strings to component
        });
    }

    /**
     * Get outputs for the control
     * @returns Control outputs
     */
    public getOutputs(): IOutputs {
        return {
            sourceControl: this._value ?? "",
        };
    }

    /**
     * Cleanup when control is destroyed
     */
    public destroy(): void {
        // Cleanup resources if needed
        this._value = null;
    }
}
