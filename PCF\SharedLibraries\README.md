# 📚 SharedLibraries

> **Reusable utilities and components for PCF development**

---

## 🎯 Overview

**SharedLibraries** is a collection of reusable utilities and components extracted from ComboboxControl and ProcessCheckpoint projects. This library provides centralized, tested, and consistent code patterns for PCF development.

## 🚀 Key Features

- **🛡️ XrmUtility**: Safe wrapper for global Xrm object with testability
- **🌐 LocalizationUtils**: Utilities for merging localization strings and custom text overrides
- **🗃️ RepositoryTypes**: Comprehensive interfaces for Repository pattern implementation
- **⚙️ Constants**: Centralized UI and logic constants organized by purpose
- **📎 FileTypes**: File attachment types and utilities for file upload controls
- **✅ Type Safety**: Full TypeScript support with strict typing
- **🔄 Reusable**: Proven patterns ready for copy-paste integration
- **📚 Well-Documented**: Comprehensive examples and usage guides

## 🏗️ Technical Architecture

This library implements a **Utility Pattern** with modular components for clean separation of concerns:

- **Utils Layer**: Core utilities for Xrm operations and localization
- **Types Layer**: Comprehensive TypeScript interfaces and type definitions
- **Constants Layer**: Centralized UI and logic constants

Key benefits include reusability, type safety, maintainability, and consistency across PCF projects.

## 🔧 Configuration & Deployment

### Integration Steps

1. **Copy Libraries**: Copy the SharedLibraries folder to your PCF project
2. **Update Imports**: Replace local utilities with shared library imports
3. **Update Constants**: Use shared constants instead of hardcoded values
4. **Test Integration**: Verify all functionality works with shared libraries

## 📋 Usage Scenarios

### **Scenario 1: New PCF Project**

**Setup:**

- Copy SharedLibraries folder to project src directory
- Import required utilities and types
- Use shared constants for consistent styling

**Workflow:**

1. **Project Setup** → Copy SharedLibraries to src/SharedLibraries
2. **Import Utilities** → Import XrmUtility, LocalizationUtils as needed
3. **Use Types** → Import RepositoryTypes for consistent interfaces
4. **Apply Constants** → Use shared UI constants for styling

### **Scenario 2: Existing Project Migration**

**Setup:**

- Audit existing utilities for duplication with SharedLibraries
- Plan migration strategy for gradual replacement
- Update import statements systematically

**Workflow:**

1. **Audit Code** → Identify utilities that can be replaced
2. **Copy Libraries** → Add SharedLibraries to project
3. **Replace Utilities** → Gradually replace local utilities
4. **Test Thoroughly** → Ensure functionality remains intact

## 🚀 How to Use

### Quick Integration

#### Step 1: Copy Libraries

```bash
# Copy the entire SharedLibraries folder to your PCF project
cp -r SharedLibraries/ /path/to/your/pcf/project/src/
```

#### Step 2: Update Imports

```typescript
// Replace local imports with shared library imports
import { getEntityId, createRecord } from '../SharedLibraries/utils/XrmUtility';
import { mergeLocalizationStrings } from '../SharedLibraries/utils/LocalizationUtils';
import { RepositoryResult } from '../SharedLibraries/types/RepositoryTypes';
```

### Migration Checklist

- [ ] Copy SharedLibraries folder to your project
- [ ] Update import statements to use shared libraries
- [ ] Replace local utilities with shared versions
- [ ] Update constants usage to shared constants
- [ ] Remove duplicate utility files
- [ ] Test all functionality after migration
- [ ] Update documentation to reference shared libraries

## 📝 Technical Implementation Details

### Library Structure

```txt
📁 SharedLibraries/
├── 📁 utils/
│   ├── XrmUtility.ts                    # 🛡️ Safe wrapper for global Xrm object
│   └── LocalizationUtils.ts             # 🌐 Utilities for merging localization strings
├── 📁 types/
│   ├── RepositoryTypes.ts               # 🗃️ Repository interfaces and error types
│   ├── Constants.ts                     # ⚙️ Centralized UI and logic constants
│   └── FileTypes.ts                     # 📎 File attachment types and utilities
└── 📄 README.md                         # 📄 This documentation
```

### Component Details

#### 🛡️ XrmUtility.ts

**Purpose:** Safe abstraction layer for global Xrm object, making repositories testable and isolating direct dependencies on Dynamics 365 client API.

**Key Features:**

- Type-safe Xrm object access
- Promise-based error handling
- BPF (Business Process Flow) operations
- Entity metadata retrieval
- Web API operations wrapper

#### 🌐 LocalizationUtils.ts

**Purpose:** Utilities for merging localization strings, supporting custom text overrides and deep object merging.

**Key Features:**

- Deep merge functionality for nested objects
- Custom text override support
- Type-safe string merging
- Fallback mechanism for missing translations

#### 🗃️ RepositoryTypes.ts

**Purpose:** Comprehensive interfaces and types for Repository pattern implementation in PCF controls.

**Key Features:**

- Standardized repository interfaces
- Consistent error handling types
- BPF-specific type definitions
- Timeline/Note operation types

#### ⚙️ Constants.ts

**Purpose:** Centralized UI and logic constants organized by purpose (background, border, text, button, icon).

**Key Features:**

- Organized constant groups
- Type-safe constant definitions
- Consistent styling values
- Easy maintenance and updates

#### 📎 FileTypes.ts

**Purpose:** File attachment types and utilities for PCF controls supporting file uploads.

**Key Features:**

- Comprehensive file type definitions
- File validation utilities
- MIME type mappings
- File size and type checking

---

## 📚 Additional Resources

- [Rule.md](../Rule.md) - Comprehensive PCF development guidelines and patterns
- [Setup.md](../Setup.md) - Project setup and initialization guide
- [ComboboxControl](../ComboboxControl/) - Example implementation using SharedLibraries
- [ProcessCheckpoint](../ProcessCheckpoint/) - Advanced implementation with SharedLibraries

## 🎉 Conclusion

The SharedLibraries collection provides a solid foundation for PCF development, ensuring consistency and reducing development time across projects.

**Key Benefits:**

- 🎯 **Faster Development** with proven, tested utilities
- 📝 **Consistent Patterns** across all PCF projects
- 🔄 **Reusable Components** with copy-paste integration
- 📎 **Type Safety** with comprehensive TypeScript support
- 🌐 **Maintainable Code** with centralized utilities
- 🛡️ **Enterprise-Ready** with professional error handling
