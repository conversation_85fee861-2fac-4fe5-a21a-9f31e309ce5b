/**
 * Localization types for ActionButton PCF Control
 * Following SharedLibraries LocalizationUtils patterns
 */

import type { NestedObject } from "../../../SharedLibraries/types/CoreTypes";

export interface LocalizationStrings extends NestedObject {
    ui: {
        buttons: {
            create: string;
            update: string;
            remove: string;
        };
        messages: {
            processing: string;
        };
        errors: {
            apiError: string;
            unknownError: string;
        };
    };
    api: {
        actions: {
            create: string;
            update: string;
            remove: string;
        };
        messages: {
            createSuccess: string;
            updateSuccess: string;
            removeSuccess: string;
        };
    };
}
