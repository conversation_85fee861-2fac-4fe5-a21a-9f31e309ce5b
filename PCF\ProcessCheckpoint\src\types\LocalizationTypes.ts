/**
 * ProcessCheckpoint Localization Types & Interface
 * Consolidated for easy maintenance
 */

import type { NestedObject } from "../../../SharedLibraries/types/CoreTypes";

// Localization Strings Interface
export interface LocalizationStrings extends NestedObject {
  ui: {
    buttons: {
      cancel: string;
      submit: string;
      advise: string;
      approve: string;
      reject: string;
      confirm: string;
      tryAgain: string;
      attachFile: string;
      removeFile: string;
      downloadFile: string;
    };
    placeholders: {
      defaultInput: string;
      readOnlyComment: string;
      enterComment: string;
      clickToViewComment: string;
      loadingStageData: string;
      stageNotActive: string;
      errorLoadingStage: string;
    };
    titles: {
      defaultPopup: string;
      errorTitle: string;
      warningTitle: string;
      successTitle: string;
    };
    labels: {
      defaultLabel: string;
      errorDetails: string;
    };
    readOnly: {
      title: string;
      description: string;
      indicator: string;
    };
    fileAttachment: {
      title: string;
      dragDropText: string;
      browseText: string;
      maxSizeText: string;
      allowedTypesText: string;
      attachedFilesTitle: string;
      noFilesText: string;
      fileSizeUnits: {
        bytes: string;
        kb: string;
        mb: string;
        gb: string;
        zeroBytes: string;
      };
      fileAttachmentPrefix: string;
    };
    iconTitles: {
      document: string;
      pdf: string;
      word: string;
      excel: string;
      powerpoint: string;
      image: string;
      archive: string;
      email: string;
      project: string;
      text: string;
      attach: string;
      delete: string;
      download: string;
    };
  };
  messages: {
    success: {
      submitted: string;
      advised: string;
      approved: string;
      rejected: string;
    };
    error: {
      submitFailed: string;
      adviseFailed: string;
      approveFailed: string;
      rejectFailed: string;
      noteCreationFailed: string;
      commentRequired: string;
      invalidComment: string;
      fileUploadFailed: string;
      fileDownloadFailed: string;
      fileRemovalFailed: string;
      fileAttachmentRequired: string;
      operationFailed: string;
      fileSizeExceeded: string;
      fileTypeNotAllowed: string;
    };
    warning: {
      unsavedChanges: string;
      noteCreationWarning: string;
    };
    confirmation: {
      confirmTitle: string;
      discardChanges: string;
      confirmDiscardButton: string;
      confirmKeepEditingButton: string;
    };
    loading: {
      submitting: string;
      advising: string;
      approving: string;
      rejecting: string;
      loadingData: string;
    };
  };
  errors: {
    general: {
      unknownError: string;
      missingBpfContext: string;
      missingPermissions: string;
      missingBpfStagesCollection: string;
      cannotMoveNextStage: string;
      cannotMovePreviousStage: string;
      entityInfoIncomplete: string;
      entityMetadataIncomplete: string;
      failedToCreateNoteInternal: string;
    };
    errorBoundary: {
      title: string;
      message: string;
      details: string;
      description: string;
    };
  };
}
