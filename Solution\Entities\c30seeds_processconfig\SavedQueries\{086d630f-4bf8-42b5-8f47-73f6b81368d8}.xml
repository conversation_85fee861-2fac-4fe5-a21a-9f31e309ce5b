﻿<?xml version="1.0" encoding="utf-8"?>
<savedqueries xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <savedquery>
    <IsCustomizable>1</IsCustomizable>
    <CanBeDeleted>0</CanBeDeleted>
    <isquickfindquery>1</isquickfindquery>
    <isprivate>0</isprivate>
    <isdefault>1</isdefault>
    <savedqueryid>{086d630f-4bf8-42b5-8f47-73f6b81368d8}</savedqueryid>
    <layoutxml>
      <grid name="resultset" jump="c30seeds_name" select="1" icon="1" preview="1">
        <row name="result" id="c30seeds_processconfigid">
          <cell name="c30seeds_name" width="300" />
          <cell name="createdon" width="125" />
        </row>
      </grid>
    </layoutxml>
    <querytype>4</querytype>
    <fetchxml>
      <fetch version="1.0" mapping="logical">
        <entity name="c30seeds_processconfig">
          <attribute name="c30seeds_processconfigid" />
          <attribute name="c30seeds_name" />
          <attribute name="createdon" />
          <order attribute="c30seeds_name" descending="false" />
          <filter type="and">
            <condition attribute="statecode" operator="eq" value="0" />
          </filter>
          <filter type="or" isquickfindfields="1">
            <condition attribute="c30seeds_name" operator="like" value="{0}" />
          </filter>
        </entity>
      </fetch>
    </fetchxml>
    <IntroducedVersion>*******</IntroducedVersion>
    <LocalizedNames>
      <LocalizedName description="Quick Find Active Process Config" languagecode="1033" />
    </LocalizedNames>
  </savedquery>
</savedqueries>