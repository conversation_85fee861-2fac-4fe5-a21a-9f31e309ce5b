﻿<?xml version="1.0" encoding="utf-8"?>
<Workflow WorkflowId="{f8ab7041-266a-471a-9fce-eee537a1fff6}" Name="Process Registration Services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <XamlFileName>/Workflows/ProcessRegistrationServices-F8AB7041-266A-471A-9FCE-EEE537A1FFF6.xaml</XamlFileName>
  <Type>1</Type>
  <Subprocess>0</Subprocess>
  <Category>3</Category>
  <Mode>0</Mode>
  <Scope>4</Scope>
  <OnDemand>0</OnDemand>
  <TriggerOnCreate>0</TriggerOnCreate>
  <TriggerOnDelete>0</TriggerOnDelete>
  <AsyncAutodelete>0</AsyncAutodelete>
  <SyncWorkflowLogOnFailure>1</SyncWorkflowLogOnFailure>
  <StateCode>1</StateCode>
  <StatusCode>2</StatusCode>
  <CreateStage>40</CreateStage>
  <RunAs>1</RunAs>
  <SdkMessageId>{9c5e3f46-ce33-eb11-a813-000d3a0859e5}</SdkMessageId>
  <UniqueName>RegistrationServices</UniqueName>
  <IsTransacted>1</IsTransacted>
  <IntroducedVersion>*******</IntroducedVersion>
  <IsCustomizable>1</IsCustomizable>
  <FormId>{00000000-0000-0000-0000-000000000000}</FormId>
  <IsCustomProcessingStepAllowedForOtherPublishers>1</IsCustomProcessingStepAllowedForOtherPublishers>
  <ModernFlowType>0</ModernFlowType>
  <PrimaryEntity>none</PrimaryEntity>
  <LocalizedNames>
    <LocalizedName languagecode="1033" description="Process Registration Services" />
  </LocalizedNames>
</Workflow>