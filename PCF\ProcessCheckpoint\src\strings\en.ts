import type { LocalizationStrings } from "../types/LocalizationTypes";

export const englishStrings: LocalizationStrings = {
  ui: {
    buttons: {
      cancel: "Cancel",
      submit: "Submit",
      advise: "Advise",
      approve: "Approve",
      reject: "Reject",
      confirm: "Confirm",
      tryAgain: "Try Again",
      attachFile: "Attach File",
      removeFile: "Remove File",
      downloadFile: "Download",
    },
    placeholders: {
      defaultInput: "Enter your content here",
      readOnlyComment: "Previous comment (read-only)",
      enterComment: "Enter your comments here",
      clickToViewComment: "Click to view comment (read-only)",
      loadingStageData: "Loading stage data...",
      stageNotActive: "This stage is not active yet",
      errorLoadingStage: "Error loading stage data",
    },
    titles: {
      defaultPopup: "Process Checkpoint",
      errorTitle: "Process Checkpoint Error",
      warningTitle: "Process Checkpoint Warning",
      successTitle: "Process Checkpoint Success",
    },
    labels: {
      defaultLabel: "Comments/Notes:",
      errorDetails: "Error Details",
    },
    readOnly: {
      title: "Read-Only Mode",
      description: "This stage is completed - View comment only",
      indicator: "read-only",
    },
    fileAttachment: {
      title: "File Attachment",
      dragDropText: "Drag and drop a file here or",
      browseText: "browse file",
      maxSizeText: "Maximum size",
      allowedTypesText: "Allowed file types",
      attachedFilesTitle: "Attachment",
      noFilesText: "No file attached",
      fileSizeUnits: {
        bytes: "Bytes",
        kb: "KB",
        mb: "MB",
        gb: "GB",
        zeroBytes: "0 Bytes",
      },
      fileAttachmentPrefix: "File attachment:",
    },
    iconTitles: {
      document: "Document file icon",
      pdf: "PDF file icon",
      word: "Word document icon",
      excel: "Excel spreadsheet icon",
      powerpoint: "PowerPoint presentation icon",
      image: "Image file icon",
      archive: "Archive file icon",
      email: "Email file icon",
      project: "Project file icon",
      text: "Text file icon",
      attach: "Attach file icon",
      delete: "Delete icon",
      download: "Download icon",
    },
  },
  messages: {
    success: {
      submitted: "Submitted successfully",
      advised: "Advised and moved to next stage",
      approved: "Approved and moved to next stage",
      rejected: "Rejected and moved to previous stage",
    },
    error: {
      submitFailed: "Failed to submit",
      adviseFailed: "Failed to advise and move to next stage",
      approveFailed: "Failed to approve and move to next stage",
      rejectFailed: "Failed to reject and move to previous stage",
      noteCreationFailed: "Failed to save comment",
      operationFailed: "Operation failed",
      fileUploadFailed: "Failed to upload file",
      fileDownloadFailed: "Failed to download file",
      fileRemovalFailed: "Failed to remove file",
      invalidComment: "Comment contains invalid characters",
      commentRequired: "Comment is required for this action.",
      fileAttachmentRequired: "A file attachment is required to proceed.",
      fileSizeExceeded: "File size exceeds the maximum allowed limit",
      fileTypeNotAllowed: "File type is not allowed",
    },
    warning: {
      unsavedChanges: "You have unsaved changes",
      noteCreationWarning: "Comment may not have been saved to timeline",
    },
    confirmation: {
      discardChanges: "If you cancel now, your comments and any attached files will be lost.",
      confirmTitle: "Discard unsaved changes?",
      confirmDiscardButton: "Discard Changes",
      confirmKeepEditingButton: "Keep Editing",
    },
    loading: {
      submitting: "Submitting...",
      advising: "Advising...",
      approving: "Approving...",
      rejecting: "Rejecting...",
      loadingData: "Loading data...",
    },
  },
  errors: {
    general: {
      unknownError: "Unknown error occurred",
      missingBpfContext: "Missing BPF context",
      missingPermissions: "Missing permissions",
      missingBpfStagesCollection: "BPF stages collection not available",
      cannotMoveNextStage: "Cannot move to next stage",
      cannotMovePreviousStage: "Cannot move to previous stage",
      entityInfoIncomplete: "Entity information incomplete",
      entityMetadataIncomplete: "Entity metadata incomplete",
      failedToCreateNoteInternal: "Failed to create note",
    },
    errorBoundary: {
      title: "Process Checkpoint Error",
      message: "An unexpected error occurred",
      details: "Please check the console for more details",
      description:
        "Something went wrong with the Process Checkpoint control. This might be due to missing BPF context or permissions.",
    },
  },
};