<?xml version="1.0" encoding="utf-8"?>
<manifest>
  <control namespace="LokBiz.Controls" constructor="ComboboxControl" version="0.0.9" display-name-key="Dynamic Options Combobox" description-key="Displays statecode or statuscode options based on a selected Business Process Flow." control-type="virtual">
    <type-group name="supportedFieldTypes">
      <type>SingleLine.Text</type>
      <type>Whole.None</type>
    </type-group>
    <external-service-usage enabled="false"></external-service-usage>
    <property name="sourceControl" display-name-key="Data Field" description-key="The field that will store the value of the selected option." of-type-group="supportedFieldTypes" usage="bound" required="true" />
    <property name="bpfLookupField" display-name-key="BPF Lookup" description-key="Bind this to the lookup field on the form that points to the BPF (Workflow entity)." of-type="Lookup.Simple" usage="bound" required="true" />
    <property name="fieldType" display-name-key="Field Type" description-key="Choose the type of options to display: State, Status Reason, or filtered Attributes by type." of-type="Enum" usage="input" required="true">
      <value name="statecode" display-name-key="Statecode">statecode</value>
      <value name="statuscode" display-name-key="Statuscode">statuscode</value>
      <value name="string" display-name-key="Single line of text">string</value>
      <value name="picklist" display-name-key="Choice">picklist</value>
      <value name="datetime" display-name-key="Date and time">datetime</value>
      <value name="all" display-name-key="All Attributes">all</value>
    </property>
    <property name="statecodeField" display-name-key="Statecode Field" description-key="Binds to the statecode (Status) field on the form. Required when the Field Type property is set to statuscode." of-type="Whole.None" usage="bound" required="false" />
    <resources>
      <code path="index.ts" order="1" />
      <platform-library name="React" version="16.14.0" />
      <platform-library name="Fluent" version="9.46.2" />
    </resources>
  </control>
</manifest>