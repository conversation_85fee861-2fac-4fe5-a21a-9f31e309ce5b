/**
 * TypeScript type definitions for ActionButton PCF Control
 * Following SharedLibraries patterns and PCF-CodingRules.md standards
 */

import type { IInputs } from '../generated/ManifestTypes';
import type { Entity, ErrorObject, NestedObject } from '../../../SharedLibraries/types/CoreTypes';
import type { LocalizationStrings } from './LocalizationTypes';

/**
 * API action types
 */
export type ApiAction = 'create' | 'update' | 'remove';

/**
 * API request payload structure - matches README.md specification
 */
export interface ApiPayload {
    action: ApiAction;
    id: string;
}

/**
 * Dynamics 365 Action payload structure for ActionButton project
 * Matches JavaScript example pattern with Command and Data structure
 * Extends NestedObject from SharedLibraries
 */
export interface ActionButtonPayload extends NestedObject {
    Command: string;  // Action command (create, update, remove, etc.)
    Data: string;     // Entity ID or data
}

/**
 * API response structure for successful calls
 * Extends SharedLibraries patterns
 */
export interface ApiResponse {
    success: true;
    data?: Entity | unknown;
    message: string;
    value?: string;
}

/**
 * API error response structure
 * Extends SharedLibraries ErrorObject pattern
 */
export interface ApiError extends Omit<ErrorObject, 'success'> {
    success: false;
    error: string;
    code: string;
    details?: string;
}

/**
 * Combined API result type
 */
export type ApiResult = ApiResponse | ApiError;

/**
 * Component props interface for DynamicButtonComponent
 */
export interface IDynamicButtonComponentProps {
    context: ComponentFramework.Context<IInputs>;
    notifyOutputChanged: () => void;
    sourceControlValue: string | null;
    strings: LocalizationStrings; // Localized strings passed from index.ts
}

/**
 * Component state interface
 */
export interface IComponentState {
    currentValue: string | null;
    isProcessing: boolean;
    errorMessage: string | null;
    lastAction: ApiAction | null;
    language: string;
    strings: LocalizationStrings;
}

/**
 * Button configuration interface
 */
export interface IButtonConfig {
    text: string;
    action: ApiAction;
    variant: 'primary' | 'secondary';
    disabled?: boolean;
}

/**
 * Type guard to check if API result is successful
 */
export const isApiSuccess = (result: ApiResult): result is ApiResponse => {
    return result.success === true;
};

/**
 * Type guard to check if API result is an error
 */
export const isApiError = (result: ApiResult): result is ApiError => {
    return result.success === false;
};
