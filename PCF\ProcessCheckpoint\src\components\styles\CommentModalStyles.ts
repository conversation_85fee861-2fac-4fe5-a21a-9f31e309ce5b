import type { CSSProperties } from "react";
import { UI_DIMENSIONS, UI_SPACING } from "../../types";

export const contentStyle: CSSProperties = {
  padding: `${UI_SPACING.SPACING_XL} ${UI_SPACING.SPACING_XXL}`,
  flex: 1,
  overflow: "hidden",
  display: "flex",
  flexDirection: "column",
};

export const fieldStyle: CSSProperties = {
  display: "flex",
  flexDirection: "column",
  flex: 1,
};

export const textareaStyle: CSSProperties = {
  width: "100%",
  maxHeight: UI_DIMENSIONS.TEXTAREA_MAX_HEIGHT,
  flex: 1,
};
