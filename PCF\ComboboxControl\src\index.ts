import * as React from 'react';
import { DynamicCombobox } from './components/DynamicCombobox';
import type { IInputs, IOutputs } from './generated/ManifestTypes';
import { FieldTypes, FieldTypeConfig, type FieldType } from '../../SharedLibraries/types/MetadataTypes';

export class ComboboxControl implements ComponentFramework.ReactControl<IInputs, IOutputs> {
    private _context: ComponentFramework.Context<IInputs>;
    private _notifyOutputChanged: () => void;
    private _container: HTMLDivElement;
    private _value: number | string | null;
    private _reactElement: React.ReactElement | null = null;

    /**
     * Initialize the PCF control with context, callback, and container
     */
    public init(
        context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        container: HTMLDivElement
    ): void {
        this._context = context;
        this._notifyOutputChanged = notifyOutputChanged;
        this._container = container;

        // Initialize value from the bound field (sourceControl)
        this._value = this.parseSourceControlValue(context.parameters.sourceControl.raw?.toString() ?? null);
    }

    /**
     * Parse the raw value from sourceControl parameter
     */
    private parseSourceControlValue(rawValue: string | null): number | string | null {
        if (rawValue === null || rawValue === undefined) {
            return null;
        }

        const fieldType = this.getFieldType();
        if (FieldTypeConfig[fieldType].storage === 'string') {
            return rawValue; // Return string for attribute logical names
        }

        const parsed = Number(rawValue);
        return Number.isNaN(parsed) ? null : parsed;
    }

    /**
     * Update the view when context changes
     */
    public updateView(context: ComponentFramework.Context<IInputs>): React.ReactElement {
        // Update context and value to reflect external changes (e.g., from business rules)
        this._context = context;
        this._value = this.parseSourceControlValue(context.parameters.sourceControl.raw?.toString() ?? null);

        // Determine if the control should be disabled
        const isDisabled = context.mode.isControlDisabled;

        // Get the field type configuration
        const fieldType = this.getFieldType();

        // Create the React element with proper props
        this._reactElement = React.createElement(DynamicCombobox, {
            value: this._value,
            fieldType: fieldType,
            context: this._context,
            disabled: isDisabled,
            onChange: this.handleValueChange.bind(this)
        });

        return this._reactElement;
    }

    /**
     * Get the field type from configuration, defaulting to 'statecode'
     */
    private getFieldType(): FieldType {
        const configuredType = this._context.parameters.fieldType.raw;

        // Simple validation using switch statement for better reliability
        switch (configuredType) {
            case FieldTypes.STATECODE:
            case FieldTypes.STATUSCODE:
            case FieldTypes.ALL:
            case FieldTypes.STRING:
            case FieldTypes.PICKLIST:
            case FieldTypes.DATETIME:
                return configuredType;
            default:
                return FieldTypes.STATECODE;
        }
    }

    /**
     * Handle value changes from the component
     */
    private handleValueChange(newValue: number | string | null): void {
        if (this._value !== newValue) {
            this._value = newValue;
            this._notifyOutputChanged();
        }
    }

    /**
     * Return the current value to be saved to the bound field
     */
    public getOutputs(): IOutputs {
        return {
            sourceControl: this._value !== null ? this._value : undefined
        };
    }

    /**
     * Cleanup resources when the control is destroyed
     */
    public destroy(): void {
        // Clean up React element reference
        this._reactElement = null;

        // Clear context and callback references to prevent memory leaks
        // Note: ComboboxComponent uses context.webAPI and context.utils extensively
        // These will be cleaned up when we clear the context reference
        this._context = undefined as unknown as ComponentFramework.Context<IInputs>;
        this._notifyOutputChanged = undefined as unknown as () => void;

        // Clear container reference
        this._container = undefined as unknown as HTMLDivElement;

        // Reset value
        this._value = null;

        // Note: ComboboxComponent uses React hooks internally (useState, useEffect, useCallback)
        // These will be automatically cleaned up when the React component unmounts
        // The component also makes WebAPI calls but these are promise-based and will
        // complete or be garbage collected when the component is destroyed

        // No manual cleanup needed for:
        // - React hooks (useState, useEffect, useCallback) - handled by React
        // - WebAPI promises - will complete or be garbage collected
        // - Event handlers (onOptionSelect, handleClearInvalidValue) - are React callbacks
        // - No timers, intervals, or manual event listeners were found in ComboboxComponent
    }
}