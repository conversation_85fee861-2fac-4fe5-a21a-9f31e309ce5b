/**
 * Field type definitions for ComboboxControl
 * Local implementation for better tree-shaking
 */

export type FieldType = 'all' | 'string' | 'picklist' | 'datetime' | 'statecode' | 'statuscode';

export interface FieldTypeConfiguration {
    category: 'attribute' | 'state';
    attributeTypeFilter?: string;
    storage?: 'string' | 'number';
}

export const FieldTypes: Record<FieldType, FieldType> = {
    all: 'all',
    string: 'string',
    picklist: 'picklist',
    datetime: 'datetime',
    statecode: 'statecode',
    statuscode: 'statuscode'
} as const;

export const FieldTypeConfig: Record<FieldType, FieldTypeConfiguration> = {
    all: { category: 'attribute', storage: 'string' },
    string: { category: 'attribute', attributeTypeFilter: 'String', storage: 'string' },
    picklist: { category: 'attribute', attributeTypeFilter: 'Picklist', storage: 'number' },
    datetime: { category: 'attribute', attributeTypeFilter: 'DateTime', storage: 'string' },
    statecode: { category: 'state', storage: 'number' },
    statuscode: { category: 'state', storage: 'number' }
} as const;
