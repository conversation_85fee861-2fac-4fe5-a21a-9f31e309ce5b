import type { LocalizationStrings } from "../types/LocalizationTypes";

export const vietnameseStrings: LocalizationStrings = {
  ui: {
    buttons: {
      cancel: "<PERSON>ủ<PERSON>",
      submit: "<PERSON><PERSON><PERSON>",
      advise: "<PERSON><PERSON> mưu",
      approve: "<PERSON><PERSON> duyệt",
      reject: "<PERSON>ừ chối",
      confirm: "<PERSON><PERSON><PERSON> nhận",
      tryAgain: "Thử lại",
      attachFile: "<PERSON><PERSON>h kèm tệp",
      removeFile: "Xóa tệp",
      downloadFile: "Tải xuống",
    },
    placeholders: {
      defaultInput: "Nhập nội dung của bạn tại đây",
      readOnlyComment: "Bình luận trước đó (chỉ đọc)",
      enterComment: "Nhập bình luận của bạn tại đây",
      clickToViewComment: "Click để xem comment (chỉ đọc)",
      loadingStageData: "Đang tải dữ liệu stage...",
      stageNotActive: "<PERSON>iai đoạn này chưa được kích hoạt",
      errorLoadingStage: "Lỗi tải dữ liệu giai đoạn",
    },
    titles: {
      defaultPopup: "Checkpoint Quy Trình",
      errorTitle: "Lỗi Process Checkpoint",
      warningTitle: "Cảnh Báo Process Checkpoint",
      successTitle: "Thành Công Process Checkpoint",
    },
    labels: {
      defaultLabel: "Bình luận/Ghi chú:",
      errorDetails: "Chi tiết lỗi",
    },
    readOnly: {
      title: "Chế độ chỉ xem",
      description: "Stage này đã hoàn thành - Chỉ có thể xem comment",
      indicator: "chỉ xem",
    },
    fileAttachment: {
      title: "Đính kèm tệp",
      dragDropText: "Kéo thả một tệp vào đây hoặc",
      browseText: "chọn tệp",
      maxSizeText: "Kích thước tối đa",
      allowedTypesText: "Loại tệp được phép",
      attachedFilesTitle: "Tệp đính kèm",
      noFilesText: "Chưa có tệp nào được đính kèm",
      fileSizeUnits: {
        bytes: "Bytes",
        kb: "KB",
        mb: "MB",
        gb: "GB",
        zeroBytes: "0 Bytes",
      },
      fileAttachmentPrefix: "Tệp đính kèm:",
    },
    iconTitles: {
      document: "Biểu tượng tệp tài liệu",
      pdf: "Biểu tượng tệp PDF",
      word: "Biểu tượng tệp Word",
      excel: "Biểu tượng tệp Excel",
      powerpoint: "Biểu tượng tệp PowerPoint",
      image: "Biểu tượng tệp hình ảnh",
      archive: "Biểu tượng tệp nén",
      email: "Biểu tượng tệp email",
      project: "Biểu tượng tệp dự án",
      text: "Biểu tượng tệp văn bản",
      attach: "Biểu tượng đính kèm tệp",
      delete: "Biểu tượng xóa",
      download: "Biểu tượng tải xuống",
    },
  },
  messages: {
    success: {
      submitted: "Đã gửi thành công",
      advised: "Đã tham mưu và chuyển sang giai đoạn tiếp theo",
      approved: "Đã phê duyệt và chuyển sang giai đoạn tiếp theo",
      rejected: "Đã từ chối và quay về giai đoạn trước",
    },
    error: {
      submitFailed: "Không thể gửi",
      adviseFailed: "Không thể tham mưu và chuyển sang giai đoạn tiếp theo",
      approveFailed: "Không thể phê duyệt và chuyển sang giai đoạn tiếp theo",
      rejectFailed: "Không thể từ chối và quay về giai đoạn trước",
      noteCreationFailed: "Không thể lưu bình luận",
      operationFailed: "Thao tác thất bại",
      fileUploadFailed: "Không thể tải lên tệp",
      fileDownloadFailed: "Không thể tải xuống tệp",
      fileRemovalFailed: "Không thể xóa tệp",
      invalidComment: "Bình luận chứa ký tự không hợp lệ",
      commentRequired: "Bình luận là bắt buộc cho hành động này.",
      fileAttachmentRequired: "Cần phải đính kèm tệp tin để tiếp tục.",
      fileSizeExceeded: "Kích thước tệp vượt quá giới hạn cho phép",
      fileTypeNotAllowed: "Loại tệp không được phép",
    },
    warning: {
      unsavedChanges: "Bạn có thay đổi chưa được lưu",
      noteCreationWarning: "Bình luận có thể chưa được lưu vào timeline",
    },
    confirmation: {
      discardChanges: "Nếu bạn hủy bây giờ, bình luận và tệp đính kèm của bạn sẽ bị mất.",
      confirmTitle: "Hủy các thay đổi chưa lưu?",
      confirmDiscardButton: "Đồng ý hủy",
      confirmKeepEditingButton: "Tiếp tục chỉnh sửa",
    },
    loading: {
      submitting: "Đang gửi...",
      advising: "Đang tham mưu...",
      approving: "Đang phê duyệt...",
      rejecting: "Đang từ chối...",
      loadingData: "Đang tải dữ liệu...",
    },
  },
  errors: {
    general: {
      unknownError: "Đã xảy ra lỗi không xác định",
      missingBpfContext: "Thiếu ngữ cảnh BPF",
      missingPermissions: "Thiếu quyền truy cập",
      missingBpfStagesCollection: "Bộ sưu tập giai đoạn BPF không khả dụng",
      cannotMoveNextStage: "Không thể chuyển sang giai đoạn tiếp theo",
      cannotMovePreviousStage: "Không thể quay lại giai đoạn trước",
      entityInfoIncomplete: "Thông tin thực thể không đầy đủ",
      entityMetadataIncomplete: "Metadata thực thể không đầy đủ",
      failedToCreateNoteInternal: "Không thể tạo ghi chú",
    },
    errorBoundary: {
      title: "Lỗi Process Checkpoint",
      message: "Đã xảy ra lỗi không mong muốn",
      details: "Vui lòng kiểm tra console để biết thêm chi tiết",
      description:
        "Đã xảy ra lỗi với control Process Checkpoint. Điều này có thể do thiếu ngữ cảnh BPF hoặc quyền truy cập.",
    },
  },
};