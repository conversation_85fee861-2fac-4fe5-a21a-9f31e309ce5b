import * as React from "react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { DEFAULT_VALUES } from "../../../SharedLibraries/types/Constants";
import { DEFAULT_FILE_TYPES_STRING, parseFileTypes, type AttachedFile } from "../../../SharedLibraries/types/FileTypes";
import {
  createAdvancedLocalizationMerger,
  createLanguageLoader,
  getValidatedLanguage,
  LANGUAGES
} from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from "../strings/en";
import { vietnameseStrings } from "../strings/vi";
import { useBpfWorkflow, useBpfState } from "../hooks";
import { createTimelineRepository } from "../repositories";
import {
  TOAST_INTENTS,
  type ITimelineRepository,
  type ProcessCheckpointProps,
  type LocalizationStrings,
  ReadOnlyReasonType,
} from "../types";
import { CommentModal } from "./CommentModal";
import { ConfirmationModal } from "./ConfirmationModal";
import { StageInput } from "./StageInput";
import { containerStyle, fluentProviderStyle } from "./styles";
import { ToastNotification, useToastMessages } from "./ToastNotification";
interface ProcessCheckpointControlCoreProps extends ProcessCheckpointProps {
  customLocalizationStrings?: LocalizationStrings;
}

const ProcessCheckpointControlCore: React.FC<ProcessCheckpointControlCoreProps> = ({
  sourceControl,
  bpfRepository,
  targetStageId,
  language,
  enableFileAttachment = false,
  allowedFileTypes = DEFAULT_FILE_TYPES_STRING,
  maxFileSize = DEFAULT_VALUES.MAX_FILE_SIZE,
  customLocalizationStrings,
  requireFileAttachment = false,
}) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [textValue, setTextValue] = React.useState(sourceControl ?? "");
  const [timelineRepository, setTimelineRepository] =
    React.useState<ITimelineRepository | null>(null);
  const [attachedFiles, setAttachedFiles] = React.useState<AttachedFile[]>([]);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] =
    React.useState(false);
  const [confirmationAction, setConfirmationAction] = React.useState<{
    onConfirm: () => void;
    onCancel: () => void;
  } | null>(null);
  const {
    messages,
    dismissToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  } = useToastMessages();

  // Create language loader for fallback
  const languageLoader = createLanguageLoader({
    [LANGUAGES.ENG]: englishStrings,
    [LANGUAGES.VN]: vietnameseStrings
  });

  const strings = customLocalizationStrings ?? languageLoader(getValidatedLanguage(language ?? undefined));

  const { readOnlyReason, isLoadingBpfState, loadStageData } = useBpfState({
    bpfRepository: bpfRepository ?? null,
    timelineRepository,
    targetStageId,
    sourceControl,
    setTextValue,
    setAttachedFiles,
  });

  const isReadOnly = readOnlyReason !== null;
  const isLoading = isLoadingBpfState || !timelineRepository;

  const { actionHandlers, isProcessing } = useBpfWorkflow({
    bpfRepository: bpfRepository ?? null,
    timelineRepository: timelineRepository,
    onClose: () => setIsModalOpen(false),
    currentComment: textValue,
    attachedFiles: attachedFiles,
    onCommentSubmitted: () => {
      setTextValue("");
      setAttachedFiles([]);
    },
    onCancel: () => {
      setTextValue("");
      setAttachedFiles([]);
    },
    isReadOnly: isReadOnly,
    onShowToast: (intent, title, message) => {
      switch (intent) {
        case TOAST_INTENTS.SUCCESS:
          showSuccess(title, message);
          break;
        case TOAST_INTENTS.ERROR:
          showError(title, message);
          break;
        case TOAST_INTENTS.WARNING:
          showWarning(title, message);
          break;
        case TOAST_INTENTS.INFO:
          showInfo(title, message);
          break;
      }
    },
    onShowConfirmation: (onConfirmCallback, onCancelCallback) => {
      setConfirmationAction({
        onConfirm: onConfirmCallback,
        onCancel: onCancelCallback,
      });
      setIsConfirmationModalOpen(true);
    },
    requireFileAttachment: requireFileAttachment,
    language: getValidatedLanguage(language ?? undefined),
  });

  // Get button configuration from BPF helper
  const buttonConfig = React.useMemo(() => {
    if (isReadOnly || !bpfRepository) {
      return {
        showCancel: true,
        showSubmit: false,
        showAdvise: false,
        showApprove: false,
        showReject: false,
      };
    }

    const stage = bpfRepository.getCurrentStageInfo();
    if (!stage) {
      return {
        showCancel: true,
        showSubmit: false,
        showAdvise: false,
        showApprove: false,
        showReject: false,
      };
    }

    if (stage.isFirstStage) {
      return {
        showCancel: true,
        showSubmit: true,
        showAdvise: false,
        showApprove: false,
        showReject: false,
      };
    } else if (stage.isLastStage) {
      return {
        showCancel: true,
        showSubmit: false,
        showAdvise: false,
        showApprove: true,
        showReject: true,
      };
    } else {
      return {
        showCancel: true,
        showSubmit: false,
        showAdvise: true,
        showApprove: false,
        showReject: true,
      };
    }
  }, [bpfRepository, isReadOnly]);

  // Effect to initialize TimelineRepository
  React.useEffect(() => {
    if (bpfRepository) {
      bpfRepository
        .getEntityInfo()
        .then((result) => {
          if (result.success && result.data) {
            setTimelineRepository(
              createTimelineRepository(
                result.data,
                undefined,
                undefined,
                strings,
              ),
            );
          } else {
            setTimelineRepository(null);
          }
          return;
        })
        .catch(() => {
          setTimelineRepository(null);
          return;
        });
    } else {
      setTimelineRepository(null);
    }
  }, [bpfRepository, strings]);

  const handleInputClick = (e: React.MouseEvent): void => {
    e.preventDefault();
    e.stopPropagation();

    // Exit early if modal is already open or the stage is not clickable.
    if (isModalOpen || readOnlyReason === ReadOnlyReasonType.NEXT) {
      return;
    }

    // Check if the stage is a past/completed one that requires data loading.
    const isPastStage = [
      ReadOnlyReasonType.PREVIOUS,
      ReadOnlyReasonType.FINISHED,
    ].includes(readOnlyReason as ReadOnlyReasonType);

    const stageToLoad =
      isPastStage && bpfRepository
        ? (targetStageId ?? bpfRepository.getSelectedStageInfo()?.stageId)
        : null;

    // If there's a specific stage to load data for, do it. Then open the modal.
    if (stageToLoad) {
      void loadStageData(stageToLoad).finally(() => {
        setIsModalOpen(true);
      });
    } else {
      // For all other cases (active stage, error state, etc.), open the modal immediately.
      setIsModalOpen(true);
    }
  };

  const handleCommentChange = (value: string) => {
    setTextValue(value);
  };

  const handleConfirmDiscard = () => {
    confirmationAction?.onConfirm();
    setIsConfirmationModalOpen(false);
    setConfirmationAction(null);
  };

  const handleCancelDiscard = () => {
    confirmationAction?.onCancel();
    setIsConfirmationModalOpen(false);
    setConfirmationAction(null);
  };
  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
  };

  if (isLoading) {
    return (
      <div style={containerStyle}>
        <StageInput
          value={strings.ui.placeholders.loadingStageData}
          readOnlyReason={null}
          onClick={() => {
            /* No action during loading state */
          }}
          isLoading={true}
          strings={strings}
        />
      </div>
    );
  }

  return (
    <FluentProvider theme={webLightTheme} style={fluentProviderStyle}>
      <div style={containerStyle}>
        <StageInput
          value={textValue}
          readOnlyReason={readOnlyReason}
          onClick={handleInputClick}
          isLoading={false}
          strings={strings}
        />
      </div>

      {isModalOpen && (
        <CommentModal
          open={isModalOpen}
          onOpenChange={handleModalOpenChange}
          value={textValue}
          onChange={handleCommentChange}
          bpfActionHandlers={actionHandlers}
          buttonConfig={buttonConfig}
          isProcessing={isProcessing}
          readOnly={isReadOnly}
          enableFileAttachment={enableFileAttachment}
          allowedFileTypes={parseFileTypes(allowedFileTypes)}
          maxFileSize={maxFileSize}
          attachedFiles={attachedFiles}
          onFilesChange={setAttachedFiles}
          timelineRepository={timelineRepository}
          strings={strings}
        />
      )}
      {isConfirmationModalOpen && (
        <ConfirmationModal
          open={isConfirmationModalOpen}
          title={strings.messages.confirmation.confirmTitle}
          message={strings.messages.confirmation.discardChanges}
          onConfirm={handleConfirmDiscard}
          onCancel={handleCancelDiscard}
          confirmButtonText={strings.messages.confirmation.confirmDiscardButton}
          cancelButtonText={
            strings.messages.confirmation.confirmKeepEditingButton
          }
          strings={strings}
        />
      )}

      <ToastNotification messages={messages} onDismiss={dismissToast} />
    </FluentProvider>
  );
};

// Create advanced merger with language map
const advancedMerger = createAdvancedLocalizationMerger(englishStrings, {
  [LANGUAGES.ENG]: englishStrings,
  [LANGUAGES.VN]: vietnameseStrings
});

export const ProcessCheckpointControl: React.FC<ProcessCheckpointProps> = (
  props,
) => {
  // Merge custom strings with default strings using SharedLibraries approach
  const finalStrings = React.useMemo(() => {
    if (props.customLocalizationStrings) {
      return props.customLocalizationStrings;
    }

    const language = getValidatedLanguage(props.language ?? undefined);

    // Use advanced merger that considers both language and custom overrides
    return advancedMerger(props.customText ?? null, language);
  }, [props.customLocalizationStrings, props.language, props.customText]);

  return <ProcessCheckpointControlCore {...props} customLocalizationStrings={finalStrings} />;
};
