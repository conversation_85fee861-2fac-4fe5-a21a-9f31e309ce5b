# 📋 Process Checkpoint PCF Control

> **Power Apps Component Framework (PCF) control for Business Process Flow stage management with comment enforcement**

## 🎯 Overview

**Process Checkpoint** is a PCF control designed to create checkpoints when transitioning stages on a Business Process Flow (BPF) in Microsoft Dynamics 365. This control ensures that users must provide comments when performing stage transition operations.

## 🚀 Key Features

- **BPF Checkpoint**: Enforces users to enter comments when changing stages
- **Timeline Integration**: Automatically creates Notes in the Timeline (annotation table)
- **Stage-Aware UI**: The user interface adapts based on the current BPF stage
- **Audit Trail**: Tracks the history of comments for each stage
- **File Attachment Support**: Single file attachment per comment (PDF, Office docs, images, etc.)
- **Multi-Language Support**: English and Vietnamese localization
- **Read-Only Mode**: View-only interface for completed stages

## ✨ File Attachment Capabilities

### Supported Features

- **Single File Attachment**: One file per submission (consistent with Dynamics 365 Timeline)
- **Supported File Types**: PDF, Word, Excel, PowerPoint, Text files, Project files, Images, Archives, Email
- **Drag & Drop Interface**: Direct drag-and-drop file upload
- **File Validation**: Automatic file type and size checking
- **Download Support**: Download attached files from Timeline Notes

### Configuration Properties

- **enableFileAttachment**: Boolean to enable/disable feature (default: false)
- **allowedFileTypes**: Comma-separated string of allowed file types
- **maxFileSize**: Maximum file size in MB (default: 10)

## 🔄 Stage-Aware UI and Read-Only Mode

### Current Stage (Active Stage)

**Buttons:**

- First stage: `Cancel` + `Submit`
- Other stages: `Cancel` + `Reject` + `Advise` (or `Approve` at final stage)

**Behavior:**

- Users can enter new comments
- `Cancel`: Closes popup (with confirmation if content exists)
- `Submit/Approve`: Creates Note + transitions to next stage
- `Reject`: Creates Note + reverts to previous stage

### Previous Stages - Read-Only Mode

**Automatic Read-Only Transition:** When user clicks on completed stage.

**Buttons:** `Cancel` only.

**Behavior:**

- **View-only mode**: Comment cannot be edited
- **On-Demand Comment Loading**: Comments loaded when user clicks input field
- **Visual Indicators**: Grayed-out interface, "read-only" placeholder

### Smart Stage Detection

- **Automatic Recognition**: System automatically detects current stage versus past stages
- **Context-Aware UI**: Interface dynamically changes based on stage status
- **On-Demand Loading**: Comments loaded only when user explicitly needs to view them

## 📝 Timeline Integration

### Automatic Note Creation

- Each action (Submit/Approve/Reject) creates a Note in the Timeline
- **Subject format**: `PCP_{stageId}_{action}` (e.g., `PCP_e6d5f859_submit`)
- **Content**: Pure comment text (no metadata)
- **Entity-Safe**: Filters by `objectTypeCode` to prevent collisions

### Stage-Specific Comments

- Each stage has its own distinct comments
- Loads the correct comment for each stage
- Supports multiple entries for the same stage
- **FetchXML queries** for optimized performance

## 🏗️ Technical Architecture

This control implements a **Repository Pattern** with 3-layer architecture for clean separation of concerns:

- **UI Layer**: React components and hooks for state management
- **Data Access Layer**: Repository pattern for Dynamics 365 interactions
- **Utility Layer**: XrmUtility abstraction for testability

Key benefits include testability, type safety, maintainability, and reusability across PCF controls.

## 🔧 Configuration & Deployment

Add the control to a field on your Dynamics 365 form and configure the properties in this order:

### Property Configuration

1. **`sourceControl` (Bound Field, Required)**:
   - Stores the comment text and manages the control's value
   - Must be a **Single Line of Text** or **Multiple Lines of Text** field

2. **`language` (Input, Optional)**:
   - Options: `en` (English), `vi` (Vietnamese)
   - Controls the UI language and localization
   - Default: `en`

3. **`enableFileAttachment` (Input, Optional)**:
   - Boolean value to enable/disable file attachment feature
   - Default: `false`

4. **`requireFileAttachment` (Input, Optional)**:
   - Boolean value to enforce file attachment for submit/advise actions
   - Only used when `enableFileAttachment` is `true`
   - Default: `false`

5. **`allowedFileTypes` (Input, Optional)**:
   - Comma-separated string of allowed file extensions
   - Example: `"pdf,docx,xlsx,jpg,png"`
   - Only used when `enableFileAttachment` is `true`

6. **`maxFileSize` (Input, Optional)**:
   - Maximum file size in MB
   - Default: `10`
   - Only used when `enableFileAttachment` is `true`

7. **`customText` (Input, Optional)**:
   - JSON string to customize default text for labels, buttons, and messages
   - Must be a valid JSON format
   - Used for UI text localization and customization
   - Example: `"{ \"ui\": { \"titles\": { \"defaultPopup\": \"Custom Title\" }, \"buttons\": { \"submit\": \"Send\" } } }"`

## 📋 Usage Scenarios

### **Scenario 1: Viewing Comments for a Previous Stage (Read-Only Mode)**

**Situation:** When the user wants to review comments for a completed stage.

**Workflow:**

1. **User Action** → Click on a completed stage on the Business Process Flow
2. **System Response** → Input field switches to read-only mode with gray background
3. **Click Input Field** → Modal displays saved comment (non-editable)
4. **Click Cancel** → Modal closes immediately without confirmation

### **Scenario 2: Entering Comments for the Current Stage (Non-Final Stage)**

**Situation:** When the user wants to enter comments for the current stage (not the final stage).

**Workflow:**

1. **Click Current Stage** → Input field in normal mode (white background)
2. **Click Input Field** → Modal displays with empty textarea
3. **Enter Comment and Click Submit/Advise** → System creates Note in Timeline
4. **Result** → BPF automatically transitions to next stage, success notification appears

### **Scenario 3: Entering Comments for the Current Stage (Final Stage)**

**Situation:** When the user wants to enter comments for the final stage of the Business Process Flow.

**Workflow:**

1. **Click Final Stage** → Input field in normal mode
2. **Click Input Field** → Modal displays with Cancel, Reject, and Approve buttons
3. **Enter Comment and Click Approve** → System creates Note in Timeline
4. **Result** → BPF changes status to "Finished", success notification appears

### **Scenario 4: Cancelling with Content Confirmation (Editable Mode)**

**Situation:** User has entered content but not saved it and wants to cancel.

**Workflow:**

1. **Click Cancel with Content** → Confirmation dialog appears
2. **Select OK** → Content is cleared and modal closes
3. **Select Cancel** → Modal remains open and content is retained

### **Scenario 5: "Reject" Action (Non-First Stage)**

**Situation:** User is on a stage that is not the first stage and wants to reject.

**Workflow:**

1. **Enter Comment and Click Reject** → System creates Note with "reject" action
2. **BPF Automatically Reverts** → Returns to previous stage, success notification
3. **Result** → Previous stage becomes the active stage

### **Scenario 6: Re-editing a Rejected Stage**

**Situation:** An approver has rejected a stage, and BPF has moved back to the original submitter's stage.

**Workflow:**

1. **Submitter Clicks Active Stage** → Control identifies this as current active stage
2. **Input Field in Editable Mode** → Not locked in read-only mode
3. **Click Input Field** → Modal displays with empty textarea
4. **Result** → User can enter new comment and resubmit for approval

### **Scenario 7: Error Handling and Recovery**

**Situation:** A network error or permission issue occurs during submission of comments or file attachments.

**Workflow:**

1. **System Response** → System fails to create Note in Timeline
2. **Error Notification** → Toast notification "Failed to save comment" or "Failed to upload file"
3. **System Still Attempts BPF Transition** → If user has sufficient permissions
4. **User Can Retry** → Comment content and file attachments are not lost

### **Scenario 8: File Attachment Experience**

**Situation:** File attachment feature is enabled (`enableFileAttachment: true`).

**Workflow:**

1. **File Attachment Section Appears** → Drag & drop area with visual indicators
2. **Information Display** → Shows allowed file types and maximum size
3. **Upload File** → Drag & drop or click "browse file" to select file
4. **Automatic Validation** → System checks file type and size
5. **Preview Display** → Valid file displays with icon and size information
6. **Submit Comment with File** → Comment and file saved into single Note in Timeline

### **Scenario 9: Multi-Language Experience**

**Situation:** Control's language is configured as Vietnamese or English.

**Workflow:**

1. **User Switches Language** → In Power Apps settings
2. **System Response** → All UI text updates immediately to new language
3. **Existing Comments Retain Language** → Timeline comments keep original language
4. **File Attachment UI Localized** → File attachment interface follows new language

## 🚀 How to Use

### End User Experience

#### Active Stage Workflow

1. **Click Input Field** → Modal opens for comment entry
2. **Enter Comment** → Type comment text
3. **Attach File** (optional) → Drag & drop or browse file
4. **Choose Action** → Submit/Approve/Reject/Advise
5. **Confirm** → Action executes, Note created, stage transitions

#### Read-Only Stage Workflow

1. **Click Previous Stage** → Interface switches to read-only mode
2. **Click Input Field** → Modal opens with saved comment (read-only)
3. **View Content** → Review comment and any attached files
4. **Click Cancel** → Modal closes

## 📁 Project Structure

```text
📁 ProcessCheckpoint/
├── 📁 src/
│   ├── 📁 components/                                            # 🎨 UI Components Layer
│   │   ├── 🎯 ProcessCheckpointControl.tsx                       # Main PCF control, handles state and BPF events
│   │   ├── 💬 CommentModal.tsx                                   # Modal dialog for entering/viewing comments
│   │   ├── 🖱️ StageInput.tsx                                     # The clickable input field that triggers the modal
│   │   ├── 🔘 ActionButtons.tsx                                  # Renders dynamic action buttons (Submit, Reject, etc.)
│   │   ├── 📎 FileAttachment.tsx                                 # Component for handling file uploads and display
│   │   ├── ✅ ConfirmationModal.tsx                              # Modal for user confirmations (e.g., discard changes)
│   │   ├── 🛡️ ErrorBoundary.tsx                                  # Catches React errors to prevent crashing the control
│   │   ├── 📢 ToastNotification.tsx                              # Displays success/error/warning notifications
│   │   └── 📁 styles/                                            # 🎨 Centralized CSSProperties for components
│   │       ├── 📦 index.ts                                       # Barrel file for simplified style imports
│   │       ├── 🛠️ CommonStyles.ts                                # General-purpose, reusable CSS patterns
│   │       ├── 🖼️ ModalStyles.ts                                 # Base styles for modal components
│   │       ├── 💬 CommentModalStyles.ts                          # Specific styles for CommentModal
│   │       ├── ✅ ConfirmationModalStyles.ts                     # Specific styles for ConfirmationModal
│   │       ├── 📎 FileAttachmentStyles.ts                        # Specific styles for FileAttachment
│   │       ├── 🎯 ProcessCheckpointControlStyles.ts              # Specific styles for ProcessCheckpointControl
│   │       └── 🖱️ StageInputStyles.ts                            # Specific styles for StageInput
│   ├── 📁 generated/                                             # 🤖 Auto-generated files
│   │   └── ⚙️ ManifestTypes.d.ts                                 # Auto-generated types from ControlManifest.Input.xml
│   ├── 📁 hooks/                                                 # 🔗 UI State Management Layer
│   │   ├── 🔗 UseBpfWorkflow.ts                                  # UI workflow state management with Promise chains
│   │   ├── 🔄 UseBpfState.ts                                     # Manages BPF state and read-only logic
│   │   ├── 📎 UseFileAttachmentHandler.ts                        # Logic for file attachment handling
│   │   └── 📦 index.ts                                           # Re-export all hooks for clean imports
│   ├── 📁 repositories/                                          # 🗃️ Data Access Layer (Repository Pattern)
│   │   ├── 🔄 BpfRepository.ts                                   # BPF data access operations
│   │   ├── 📝 TimelineRepository.ts                              # Timeline/Notes data access with FetchXML
│   │   ├── 🏭 RepositoryFactory.ts                               # Factory functions for creating repositories
│   │   └── 📦 index.ts                                           # Re-export all repositories for clean imports
│   ├── 📁 types/                                                 # 📋 Type Definition Layer
│   │   ├── 🔄 BpfTypes.ts                                        # BPF core types, interfaces, enums
│   │   ├── 🎯 ComponentTypes.ts                                  # Component props interfaces
│   │   ├── ⚙️ Constants.ts                                       # Centralized UI and logic constants
│   │   ├── 📎 FileTypes.ts                                       # File attachment types
│   │   ├── 🌐 LocalizationTypes.ts                               # Types related to localization
│   │   ├── 📢 NotificationTypes.ts                               # Toast/notification types
│   │   ├── 🗃️ RepositoryTypes.ts                                 # Repository interfaces and error types
│   │   └── 📦 index.ts                                           # Re-export all types
│   ├── 📁 strings/                                               # 🌍 Internationalization Layer
│   │   ├── 🇬🇧 en.ts                                               # English localization strings
│   │   └── 🇻🇳 vi.ts                                               # Vietnamese localization strings
│   ├── ⚙️ ControlManifest.Input.xml                              # PCF manifest configuration
│   └── 🚀 index.ts                                               # PCF control entry point and lifecycle
├── 📄 package.json                                               # Dependencies and scripts
├── 📄 tsconfig.json                                              # TypeScript configuration
└── 📄 README.md                                                  # Project documentation (English)
```

## 📚 Additional Resources

- [Rule.md](../../../Rule.md) - Comprehensive PCF development guidelines and patterns
- [Setup.md](../../../Setup.md) - Project setup and initialization guide
- [SharedLibraries](../../../SharedLibraries/) - Reusable utilities and components

## 🎉 Conclusion

The Process Checkpoint PCF Control provides a comprehensive and production-ready solution for managing comments within Business Process Flows, ensuring complete audit trail and optimal user experience for Dynamics 365.

**Key Benefits:**

- 🎯 **Enforced commenting** at each stage with comprehensive validation
- 📝 **Automatic timeline integration** with structured Notes creation
- 🔄 **Stage-aware user interface** with dynamic button configuration
- 📎 **File attachment support** with validation, preview, and download capabilities
- 🌐 **Multi-language support** for global deployments (English/Vietnamese)
- 🛡️ **Enterprise-ready architecture** with Repository Pattern and clean separation of concerns
- ⚡ **Performance optimized** with on-demand loading and Promise chain architecture
- 🔒 **Security compliant** with TypeScript type safety and Dynamics 365 security model
- 🎨 **Professional UI/UX** with context-aware design and accessibility support
- 📊 **Complete audit trail** with timestamp and action tracking for compliance requirements
