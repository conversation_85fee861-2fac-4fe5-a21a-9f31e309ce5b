/**
 * Metadata Types - Dynamics 365 metadata interfaces
 */

export interface AttributeMetadata {
  LogicalName: string;
  AttributeType: string;
  MetadataId: string;
  DisplayName?: {
    UserLocalizedLabel?: { 
      Label: string; 
      LanguageCode: number; 
    };
    LocalizedLabels?: { 
      Label: string; 
      LanguageCode: number; 
    }[];
  };
  OptionSet?: {
    Options?: Array<{
      Value: number;
      Label: {
        UserLocalizedLabel?: {
          Label: string;
        };
      };
    }>;
  };
}

export interface IRuntimeMetadata {
  key: number | string;
  displayName: string;
  state?: number;
}

export interface EntityDefinition {
  LogicalName?: string;
  MetadataId?: string;
  DisplayName?: {
    UserLocalizedLabel?: {
      Label: string;
      LanguageCode: number;
    };
    LocalizedLabels?: {
      Label: string;
      LanguageCode: number;
    }[];
  };
  Attributes?: AttributeMetadata[];
}

export const FieldTypes = {
  STATECODE: 'statecode',
  STATUSCODE: 'statuscode',
  STRING: 'string',
  PICKLIST: 'picklist',
  DATETIME: 'datetime',
  ALL: 'all'
} as const;

export type FieldType = typeof FieldTypes[keyof typeof FieldTypes];

export const AttributeTypes = {
  STRING: 'String',
  PICKLIST: 'Picklist',
  DATETIME: 'DateTime'
} as const;

export type AttributeType = typeof AttributeTypes[keyof typeof AttributeTypes];

export interface FieldTypeConfiguration {
  category: 'attribute' | 'optionset';
  storage: 'string' | 'number';
  attributeTypeFilter?: AttributeType;
}

export const FieldTypeConfig: Record<FieldType, FieldTypeConfiguration> = {
  [FieldTypes.STATECODE]: { category: 'optionset', storage: 'number' },
  [FieldTypes.STATUSCODE]: { category: 'optionset', storage: 'number' },
  [FieldTypes.ALL]: { category: 'attribute', storage: 'string' },
  [FieldTypes.STRING]: { category: 'attribute', storage: 'string', attributeTypeFilter: AttributeTypes.STRING },
  [FieldTypes.PICKLIST]: { category: 'attribute', storage: 'string', attributeTypeFilter: AttributeTypes.PICKLIST },
  [FieldTypes.DATETIME]: { category: 'attribute', storage: 'string', attributeTypeFilter: AttributeTypes.DATETIME }
};
