﻿<?xml version="1.0" encoding="utf-8"?>
<RibbonDiffXml xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <CustomActions>
    <CustomAction Id="zlb.c30seeds.c30seeds_processconfig.Publish.Button.CustomAction" Location="Mscrm.Form.c30seeds_processconfig.MainTab.Save.Controls._children" Sequence="25">
      <CommandUIDefinition>
        <Button Alt="$LocLabels:c30seeds.c30seeds_processconfig.Publish.Button.Alt" Command="c30seeds.c30seeds_processconfig.Publish.Command" Id="c30seeds.c30seeds_processconfig.Publish.Button" Image32by32="$webresource:c30seeds_/processConfig/icons/add.svg" Image16by16="$webresource:c30seeds_/processConfig/icons/add.svg" LabelText="$LocLabels:c30seeds.c30seeds_processconfig.Publish.Button.LabelText" Sequence="25" TemplateAlias="o1" ToolTipTitle="$LocLabels:c30seeds.c30seeds_processconfig.Publish.Button.ToolTipTitle" ToolTipDescription="$LocLabels:c30seeds.c30seeds_processconfig.Publish.Button.ToolTipDescription" ModernImage="$webresource:c30seeds_/processConfig/icons/add.svg" />
      </CommandUIDefinition>
    </CustomAction>
    <CustomAction Id="zlb.c30seeds.c30seeds_processconfig.UnPublish.Button.CustomAction" Location="Mscrm.Form.c30seeds_processconfig.MainTab.Save.Controls._children" Sequence="28">
      <CommandUIDefinition>
        <Button Alt="$LocLabels:c30seeds.c30seeds_processconfig.UnPublish.Button.Alt" Command="c30seeds.c30seeds_processconfig.UnPublish.Command" Id="c30seeds.c30seeds_processconfig.UnPublish.Button" Image32by32="$webresource:c30seeds_/processConfig/icons/banned.svg" Image16by16="$webresource:c30seeds_/processConfig/icons/banned.svg" LabelText="$LocLabels:c30seeds.c30seeds_processconfig.UnPublish.Button.LabelText" Sequence="28" TemplateAlias="o1" ToolTipTitle="$LocLabels:c30seeds.c30seeds_processconfig.UnPublish.Button.ToolTipTitle" ToolTipDescription="$LocLabels:c30seeds.c30seeds_processconfig.UnPublish.Button.ToolTipDescription" ModernImage="$webresource:c30seeds_/processConfig/icons/banned.svg" />
      </CommandUIDefinition>
    </CustomAction>
  </CustomActions>
  <Templates>
    <RibbonTemplates Id="Mscrm.Templates"></RibbonTemplates>
  </Templates>
  <CommandDefinitions>
    <CommandDefinition Id="c30seeds.c30seeds_processconfig.Publish.Command">
      <EnableRules>
        <EnableRule Id="c30seeds.c30seeds_processconfig.Publish.EnableRule" />
      </EnableRules>
      <DisplayRules />
      <Actions>
        <JavaScriptFunction FunctionName="C30Seeds.ProcessConfig.Ribbon.publishClicked" Library="$webresource:c30seeds_/processConfig/processconfig.js">
          <CrmParameter Value="PrimaryControl" />
        </JavaScriptFunction>
      </Actions>
    </CommandDefinition>
    <CommandDefinition Id="c30seeds.c30seeds_processconfig.UnPublish.Command">
      <EnableRules>
        <EnableRule Id="c30seeds.c30seeds_processconfig.UnPublish.EnableRule" />
      </EnableRules>
      <DisplayRules />
      <Actions>
        <JavaScriptFunction FunctionName="C30Seeds.ProcessConfig.Ribbon.unPublishClicked" Library="$webresource:c30seeds_/processConfig/processconfig.js">
          <CrmParameter Value="PrimaryControl" />
        </JavaScriptFunction>
      </Actions>
    </CommandDefinition>
  </CommandDefinitions>
  <RuleDefinitions>
    <TabDisplayRules />
    <DisplayRules />
    <EnableRules>
      <EnableRule Id="c30seeds.c30seeds_processconfig.Publish.EnableRule">
        <FormStateRule State="Existing" Default="false" />
        <ValueRule Field="c30seeds_registered" Value="0" Default="false" />
      </EnableRule>
      <EnableRule Id="c30seeds.c30seeds_processconfig.UnPublish.EnableRule">
        <FormStateRule State="Existing" Default="false" />
        <ValueRule Field="c30seeds_registered" Value="1" Default="false" />
      </EnableRule>
    </EnableRules>
  </RuleDefinitions>
  <LocLabels>
    <LocLabel Id="c30seeds.c30seeds_processconfig.Publish.Button.Alt">
      <Titles>
        <Title description="Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.Publish.Button.LabelText">
      <Titles>
        <Title description="Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.Publish.Button.ToolTipDescription">
      <Titles>
        <Title description="Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.Publish.Button.ToolTipTitle">
      <Titles>
        <Title description="Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.UnPublish.Button.Alt">
      <Titles>
        <Title description="Un-Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.UnPublish.Button.LabelText">
      <Titles>
        <Title description="Un-Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.UnPublish.Button.ToolTipDescription">
      <Titles>
        <Title description="Un-Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
    <LocLabel Id="c30seeds.c30seeds_processconfig.UnPublish.Button.ToolTipTitle">
      <Titles>
        <Title description="Un-Publish" languagecode="1033" />
      </Titles>
    </LocLabel>
  </LocLabels>
</RibbonDiffXml>