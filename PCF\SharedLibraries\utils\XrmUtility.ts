/**
 * XrmUtility - Modular Xrm wrapper for tree-shaking optimization
 *
 * File Structure (MANDATORY ORDER):
 * 1. Type Definitions & Interfaces
 * 2. Core Utilities → 3. Entity Context → 4. Web API → 5. Metadata → 6. BPF → 7. Actions
 * 8. ALL export const { } as const (at the end)
 */
import type {
  BpfCollection,
  BpfStage
} from '../types/BpfTypes';

import type {
  Entity,
  LookupValue,
  NestedObject,
  RetrieveMultipleResponse,
  ActionResponse,
  ActionRequest,
  ActionMetadata,
  WebApiExecuteResponse,
} from '../types/CoreTypes';

import type {
  AttributeMetadata,
  EntityDefinition
} from '../types/MetadataTypes';

export type {
  BpfStage,
  BpfCollection,
  Entity,
  RetrieveMultipleResponse,
  LookupValue,
  AttributeMetadata,
  EntityDefinition
};

// Internal types

interface XrmWindow extends Window {
  Xrm?: {
    WebApi?: {
      createRecord: (
        entityLogicalName: string,
        data: Entity,
      ) => Promise<LookupValue>;

      retrieveRecord: (
        entityLogicalName: string,
        recordId: string,
        options?: string,
      ) => Promise<Entity>;

      retrieveMultipleRecords: (
        entityLogicalName: string,
        options?: string,
        maxPageSize?: number,
      ) => Promise<RetrieveMultipleResponse>;

      updateRecord: (
        entityLogicalName: string,
        recordId: string,
        data: Entity,
      ) => Promise<LookupValue>;

      deleteRecord: (
        entityLogicalName: string,
        recordId: string,
      ) => Promise<LookupValue>;

      online?: {
        execute: (request: any) => Promise<any>;
      };
    };
    Utility?: {
      getGlobalContext: () => {
        getClientUrl: () => string;
      };
      getEntityMetadata: (entityName: string) => Promise<{
        EntitySetName?: string;
        ObjectTypeCode?: number;
      }>;
    };
    Page?: {
      data?: {
        refresh: () => Promise<void>;
        process?: {
          getStatus: () => string;
          setStatus: (status: string) => Promise<void>;
          getSelectedStage: () => BpfStage | null;
          getActiveStage: () => BpfStage | null;
          getActivePath: () => BpfCollection;
          moveNext: () => Promise<void>;
          movePrevious: () => Promise<void>;
        };
        entity?: {
          getId: () => string;
          getEntityName: () => string;
        };
      };
      ui?: {
        refreshRibbon: () => void;
      };
    };
  };
}

// ===== 1. CORE UTILITIES (Shared by multiple functions) =====

/**
 * Safely gets the Xrm object from the window
 */
export const getXrm = (): XrmWindow["Xrm"] | undefined => {
  const xrmWindow = window as XrmWindow;
  return xrmWindow.Xrm;
};

/**
 * Helper to create standardized error for missing Xrm paths
 */
const rejectXrmPromise = (path: string): Promise<never> => {
  return Promise.reject(new Error(`Xrm.${path} not available`));
};

// ===== 2. UTILITY FUNCTIONS (Tree-shakable) =====

/**
 * Gets the client URL from Xrm.
 * @returns The client URL string or an empty string if Xrm is not available.
 * @category Utility
 */
export const getClientUrl = (): string => {
  const xrm = getXrm();
  return xrm?.Utility?.getGlobalContext().getClientUrl() ?? "";
};

// ===== 3. ENTITY CONTEXT FUNCTIONS =====

/**
 * Gets the current entity ID from the form context.
 * @returns The entity ID or empty string if not available.
 */
export const getEntityId = (): string => {
  const xrm = getXrm();
  return xrm?.Page?.data?.entity?.getId() ?? "";
};

/**
 * Gets the current entity logical name from the form context.
 * @returns The entity logical name or empty string if not available.
 */
export const getEntityLogicalName = (): string => {
  const xrm = getXrm();
  return xrm?.Page?.data?.entity?.getEntityName() ?? "";
};

// ===== 4. WEB API FUNCTIONS (CRUD Operations) =====

/**
 * Creates a table record.
 * @param entityLogicalName Logical name of the table you want to create.
 * @param data A JSON object defining the columns and values for the new table record.
 * @returns A promise that resolves with the lookup value containing record info.
 * @category WebAPI
 */
export const createRecord = (
  entityLogicalName: string,
  data: Entity,
): Promise<LookupValue> => {
  const xrm = getXrm();
  if (!xrm?.WebApi) {
    return rejectXrmPromise("WebApi");
  }
  return xrm.WebApi.createRecord(entityLogicalName, data);
};

/**
 * Retrieves a table record.
 * @param entityLogicalName The table logical name of the record you want to retrieve.
 * @param recordId GUID of the table record you want to retrieve.
 * @param options OData system query options to control what is returned.
 * @returns On success, returns a promise containing a JSON object with the retrieved columns and their values.
 * @category WebAPI
 */
export const retrieveRecord = (
  entityLogicalName: string,
  recordId: string,
  options?: string,
): Promise<Entity> => {
  const xrm = getXrm();
  if (!xrm?.WebApi) {
    return rejectXrmPromise("WebApi");
  }
  return xrm.WebApi.retrieveRecord(entityLogicalName, recordId, options);
};

/**
 * Retrieves a collection of table records.
 * @param entityLogicalName The table logical name of the records you want to retrieve.
 * @param options OData system query options or FetchXML query to retrieve your data.
 * @param maxPageSize Max number of records to be retrieved per page
 * @returns A promise that resolves with the query result.
 * @category WebAPI
 */
export const retrieveMultipleRecords = (
  entityLogicalName: string,
  options?: string,
  maxPageSize?: number,
): Promise<RetrieveMultipleResponse> => {
  const xrm = getXrm();
  if (!xrm?.WebApi) {
    return rejectXrmPromise("WebApi");
  }
  return xrm.WebApi.retrieveMultipleRecords(entityLogicalName, options, maxPageSize);
};

/**
 * Updates a table record.
 * @param entityLogicalName Logical name of the table record to update.
 * @param recordId GUID of the table record you want to update.
 * @param data A JSON object containing the columns and values to update.
 * @returns A promise that resolves with the updated record info.
 * @category WebAPI
 */
export const updateRecord = (
  entityLogicalName: string,
  recordId: string,
  data: Entity,
): Promise<LookupValue> => {
  const xrm = getXrm();
  if (!xrm?.WebApi) {
    return rejectXrmPromise("WebApi");
  }
  return xrm.WebApi.updateRecord(entityLogicalName, recordId, data);
};

/**
 * Deletes a table record.
 * @param entityLogicalName Logical name of the table record to delete.
 * @param recordId GUID of the table record you want to delete.
 * @returns A promise that resolves with the deleted record info.
 * @category WebAPI
 */
export const deleteRecord = (
  entityLogicalName: string,
  recordId: string,
): Promise<LookupValue> => {
  const xrm = getXrm();
  if (!xrm?.WebApi) {
    return rejectXrmPromise("WebApi");
  }
  return xrm.WebApi.deleteRecord(entityLogicalName, recordId);
};

/**
 * Fetches entity definitions using OData query
 * @param entityName The logical name of the entity (e.g., 'account', 'contact')
 * @param query The OData query string (e.g., "?$select=LogicalName&$expand=Attributes")
 * @returns A promise that resolves with the entity definition
 * @category WebAPI
 */
export const fetchEntityDefinitions = (
  entityName: string,
  query: string,
): Promise<EntityDefinition> => {
  const clientUrl = getClientUrl();
  if (!clientUrl) {
    return Promise.reject(new Error('Client URL not available'));
  }

  // Build full URL with entityName and query
  const fullQuery = query.startsWith('EntityDefinitions') ? query : `EntityDefinitions(LogicalName='${entityName}')${query}`;

  return fetch(`${clientUrl}/api/data/v9.2/${fullQuery}`, {
    method: 'GET',
    headers: {
      'OData-MaxVersion': '4.0',
      'OData-Version': '4.0',
      'Accept': 'application/json',
      'Content-Type': 'application/json; charset=utf-8',
      'Prefer': 'odata.include-annotations="*"'
    }
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((entityDefinition: EntityDefinition) => {
      if (!entityDefinition) {
        throw new Error('Entity definition not found');
      }
      return entityDefinition;
    });
};

// ===== 5. METADATA FUNCTIONS =====

/**
 * Returns table definitions for the specified table.
 * @param entityName The logical name of the table.
 * @returns An object containing the table definitions information.
 * @category Metadata
 */
export const getEntityMetadata = (
  entityName: string,
): Promise<{ EntitySetName?: string; ObjectTypeCode?: number }> => {
  const xrm = getXrm();
  if (!xrm?.Utility?.getEntityMetadata) {
    return rejectXrmPromise("Utility.getEntityMetadata");
  }
  return xrm.Utility.getEntityMetadata(entityName);
};

// ===== 6. BUSINESS PROCESS FLOW FUNCTIONS =====

/**
 * Returns the current status of the process instance.
 * @returns Returns one of the following values: active, aborted, or finished.
 * @category BPF
 */
export const getProcessStatus = (): string | null => {
  const xrm = getXrm();
  return xrm?.Page?.data?.process?.getStatus() ?? null;
};

/**
 * Sets the current status of the active process instance.
 * @param status The new status. The values can be active, aborted, finished, or invalid.
 * @returns Returns one of the following values: active, aborted, or finished. It returns invalid if the setStatus API fails.
 * @category BPF
 */
export const setProcessStatus = (status: string): Promise<void> => {
  const xrm = getXrm();
  if (!xrm?.Page?.data?.process?.setStatus) {
    return rejectXrmPromise("Page.data.process.setStatus");
  }
  return xrm.Page.data.process.setStatus(status);
};

/**
 * Gets the currently selected stage.
 * @returns The currently selected stage or null.
 * @category BPF
 */
export const getSelectedStage = (): BpfStage | null => {
  const xrm = getXrm();
  return xrm?.Page?.data?.process?.getSelectedStage() ?? null;
};

/**
 * Returns a Stage object representing the active stage.
 * @returns The currently active stage or null.
 * @category BPF
 */
export const getActiveStage = (): BpfStage | null => {
  const xrm = getXrm();
  return xrm?.Page?.data?.process?.getActiveStage() ?? null;
};

/**
 * Gets a collection of stages currently in the active path with methods to interact with the stages displayed in the business process flow control.
 * The active path represents stages currently rendered in the process control based on the branching rules and current data in the record.
 * @returns  A collection of all completed stages, the currently active stage, and the predicted set of future stages based on satisfied conditions in the branching rule.
 * @category BPF
 */
export const getActivePath = (): BpfCollection => {
  const xrm = getXrm();
  // Return a mock BpfCollection that behaves as an empty collection
  if (!xrm?.Page?.data?.process) {
    return {
      get: () => null,
      getAll: () => [],
      getByName: () => null,
      getByIndex: () => null,
      getLength: () => 0,
      forEach: () => {
        /* do nothing */
      },
    };
  }
  return xrm.Page.data.process.getActivePath();
};

/**
 * Progresses to the next stage.
 * Moving to next stage isn't supported for different table.
 * @returns A promise that resolves when the process moves to the next stage.
 * @category BPF
 */
export const moveNextStage = (): Promise<void> => {
  const xrm = getXrm();
  if (!xrm?.Page?.data?.process?.moveNext) {
    return rejectXrmPromise("Page.data.process.moveNext");
  }
  return xrm.Page.data.process.moveNext();
};

/**
 * Moves to the previous stage.
 * You can also move to a previous stage in a different table.
 * @returns A promise that resolves when the process moves to the previous stage.
 * @category BPF
 */
export const movePreviousStage = (): Promise<void> => {
  const xrm = getXrm();
  if (!xrm?.Page?.data?.process?.movePrevious) {
    return rejectXrmPromise("Page.data.process.movePrevious");
  }
  return xrm.Page.data.process.movePrevious();
};

// ===== 7. ACTION CALL UTILITIES =====

// ActionResponse, ActionRequest, ActionMetadata interfaces moved to CoreTypes.ts for better architecture

/**
 * Executes a custom action using Dynamics 365 WebAPI pattern
 * Matches JavaScript example with Xrm.WebApi.online.execute()
 * @param actionName - Name of the custom action
 * @param payload - Action payload with Command and Data
 * @param parameterName - Parameter name (default: "Request")
 * @returns Promise<ActionResponse>
 */
export const executeCustomAction = (
  actionName: string,
  payload: NestedObject,
  parameterName: string = "Request"
): Promise<ActionResponse> => {
  return new Promise((resolve) => {
    try {
      const xrm = getXrm();
      if (!xrm?.WebApi?.online?.execute) {
        resolve({
          success: false,
          error: 'Xrm.WebApi.online.execute is not available',
          code: 'XRM_NOT_AVAILABLE',
        });
        return;
      }

      // Create request object following JavaScript example pattern
      const reqObject: ActionRequest = {
        [parameterName]: JSON.stringify(payload),
        getMetadata: (): ActionMetadata => ({
          boundParameter: null,
          operationType: 0, // Action type
          operationName: actionName,
          parameterTypes: {
            [parameterName]: {
              typeName: "Edm.String",
              structuralProperty: 1
            }
          }
        })
      };

      xrm.WebApi.online.execute(reqObject)
        .then((response: WebApiExecuteResponse) => {
          if (response?.ok) {
            response.json().then((result: unknown) => {
              resolve({
                success: true,
                data: result,
                message: (result as { Response?: string }).Response || 'Action executed successfully',
                value: (result as { Response?: string }).Response
              });
            }).catch(() => {
              resolve({
                success: true,
                data: response,
                message: 'Action executed successfully'
              });
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${response?.status}: ${response?.statusText}`,
              code: response?.status?.toString() || 'RESPONSE_ERROR',
            });
          }
        })
        .catch((error: ErrorEvent) => {
          resolve({
            success: false,
            error: error?.message || 'Unknown error occurred',
            code: 'EXECUTION_ERROR',
          });
        });
    } catch (error) {
      resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'EXECUTION_ERROR',
      });
    }
  });
};

/**
 * Executes a bound action on a specific entity record using Dynamics 365 WebAPI pattern
 * @param entityLogicalName - Logical name of the entity
 * @param recordId - ID of the record
 * @param actionName - Name of the bound action
 * @param payload - Action payload with Command and Data
 * @param parameterName - Parameter name (default: "Request")
 * @returns Promise<ActionResponse>
 */
export const executeBoundAction = (
  entityLogicalName: string,
  recordId: string,
  actionName: string,
  payload: NestedObject,
  parameterName: string = "Request"
): Promise<ActionResponse> => {
  return new Promise((resolve) => {
    try {
      const xrm = getXrm();
      if (!xrm?.WebApi?.online?.execute) {
        resolve({
          success: false,
          error: 'Xrm.WebApi.online.execute is not available',
          code: 'XRM_NOT_AVAILABLE',
        });
        return;
      }

      // Create request object for bound action
      const reqObject: ActionRequest = {
        [parameterName]: JSON.stringify(payload),
        getMetadata: (): ActionMetadata => ({
          boundParameter: "entity",
          operationType: 0, // Action type
          operationName: actionName,
          parameterTypes: {
            entity: {
              typeName: `mscrm.${entityLogicalName}`,
              structuralProperty: 5
            },
            [parameterName]: {
              typeName: "Edm.String",
              structuralProperty: 1
            }
          }
        })
      };

      // Set the bound parameter
      reqObject.entity = {
        [`${entityLogicalName}id`]: recordId,
        "@odata.type": `Microsoft.Dynamics.CRM.${entityLogicalName}`
      };

      xrm.WebApi.online.execute(reqObject)
        .then((response: WebApiExecuteResponse) => {
          if (response?.ok) {
            response.json().then((result: unknown) => {
              resolve({
                success: true,
                data: result,
                message: (result as { Response?: string }).Response || 'Bound action executed successfully',
                value: (result as { Response?: string }).Response
              });
            }).catch(() => {
              resolve({
                success: true,
                data: response,
                message: 'Bound action executed successfully'
              });
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${response?.status}: ${response?.statusText}`,
              code: response?.status?.toString() || 'RESPONSE_ERROR',
            });
          }
        })
        .catch((error: Error) => {
          resolve({
            success: false,
            error: error?.message || 'Unknown error occurred',
            code: 'BOUND_ACTION_ERROR',
          });
        });
    } catch (error) {
      resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        code: 'BOUND_ACTION_ERROR',
      });
    }
  });
};

/**
 * Generic action executor with tree-shaking support
 * @param config - Action configuration
 * @returns Promise<ActionResponse>
 */
export const executeAction = (config: {
  actionName: string;
  payload: NestedObject;
  entityLogicalName?: string;
  recordId?: string;
  endpoint?: string;
  isBound?: boolean;
}): Promise<ActionResponse> => {
  const { actionName, payload, entityLogicalName, recordId, endpoint, isBound } = config;

  if (isBound && entityLogicalName && recordId) {
    return executeBoundAction(entityLogicalName, recordId, actionName, payload);
  } else {
    return executeCustomAction(actionName, payload, endpoint);
  }
};

// ===== 8. CONVENIENCE EXPORTS FOR COMMON COMBINATIONS =====

/**
 * Core Utility functions - import this for basic utilities and client URL
 */
export const CoreUtils = {
  getXrm,
  getClientUrl,
} as const;

/**
 * Entity Context functions - import this if you need entity context
 */
export const EntityUtils = {
  getEntityId,
  getEntityLogicalName,
} as const;

/**
 * WebAPI functions - import this if you need CRUD operations
 */
export const WebAPI = {
  createRecord,
  retrieveRecord,
  retrieveMultipleRecords,
  updateRecord,
  deleteRecord,
  fetchEntityDefinitions,
} as const;

/**
 * Metadata functions - import this for entity metadata operations
 */
export const MetadataUtils = {
  getEntityMetadata,
  fetchEntityDefinitions,
} as const;

/**
 * Business Process Flow functions - import this if you're working with BPF
 */
export const BPF = {
  getProcessStatus,
  setProcessStatus,
  getSelectedStage,
  getActiveStage,
  getActivePath,
  moveNextStage,
  movePreviousStage,
} as const;

/**
 * Action Utility functions - import this for action execution with tree-shaking support
 */
export const ActionUtility = {
  executeCustomAction,
  executeBoundAction,
  executeAction,
} as const;

/**
 * Legacy Utility export - for backward compatibility (use CoreUtils + MetadataUtils instead)
 * @deprecated Use CoreUtils and MetadataUtils instead
 */
export const Utility = {
  getClientUrl,
  getEntityMetadata,
  fetchEntityDefinitions,
} as const;

// ===== EXPORTS SUMMARY =====
/**
 * Grouped Exports (in dependency order):
 * CoreUtils → EntityUtils → WebAPI → MetadataUtils → BPF → ActionUtility
 *
 * Individual functions available for tree-shaking
 * All types re-exported from CoreTypes, BpfTypes, MetadataTypes
 */
