export const UI_COLORS = {
  // --- Core/General Colors ---
  WHITE: "#ffffff", // Used for general backgrounds, etc.

  // --- Background Colors ---
  background: {
    LIGHT_GRAY: "#f8f8f8",
    MODAL_OVERLAY: "rgba(0, 0, 0, 0.6)",
    MODAL_CONFIRMATION_OVERLAY: "rgba(0, 0, 0, 0.5)",
    CODE_BLOCK: "#f5f5f5", // Formerly CODE_BLOCK_BG
    READ_ONLY_INFO: "#f3f2f1", // Formerly READ_ONLY_INFO_BG
    DRAG_ACTIVE: "#f3f9fd", // Formerly DRAG_ACTIVE_BG
  },

  // --- Border Colors ---
  border: {
    DEFAULT: "#d1d1d1", // Formerly DEFAULT_BORDER
    DIVIDER_LIGHT: "#e1e5e9", // Formerly DIVIDER_LIGHT
    READ_ONLY: "#d2d0ce", // Formerly READ_ONLY_BORDER
    ACTIVE: "#605e5c", // Formerly ACTIVE_BORDER
    READ_ONLY_INFO: "#8a8886", // Formerly READ_ONLY_INFO_BORDER
    DRAG_ACTIVE: "#0078d4", // Formerly DRAG_ACTIVE_BORDER
  },

  // --- Text Colors ---
  text: {
    DEFAULT: "#323130", // Formerly DEFAULT_TEXT (Main text color)
    READ_ONLY: "#8a8886", // Formerly READ_ONLY_TEXT
    SECONDARY: "#605e5c", // Formerly SECONDARY_TEXT
    LINK: "#0078d4", // Formerly LINK_TEXT
  },

  // --- Button Colors ---
  button: {
    PRIMARY_BACKGROUND: "#0078D4", // Formerly PRIMARY_BUTTON_BACKGROUND
    SUBMIT_BACKGROUND: "#0078D4", // Same as PRIMARY for submit actions
    REJECT_BACKGROUND: "#d13438", // Formerly REJECT_BUTTON_BG
    APPROVE_BACKGROUND: "#107c10", // Formerly APPROVE_BUTTON_BG
    TEXT_WHITE: "white", // Formerly BUTTON_TEXT_WHITE (inferred from usage)
  },

  // --- Icon Colors ---
  icon: {
    WORD: "#2b579a", // Formerly WORD_ICON_COLOR
    POWERPOINT: "#d24726", // Formerly POWERPOINT_ICON_COLOR
    PROJECT: "#31752f", // Formerly PROJECT_ICON_COLOR
    EXCEL: "#107c10", // Matches button.APPROVE_BACKGROUND
    PDF: "#d13438", // Matches button.REJECT_BACKGROUND
    EMAIL: "#0078d4", // Matches button.PRIMARY_BACKGROUND
    ATTACH: "#0078d4", // Matches button.PRIMARY_BACKGROUND
    GENERAL: "#605e5c", // Matches text.SECONDARY for generic document/image/archive/text icons
  },
} as const;

export const UI_DIMENSIONS = {
  // Component dimensions
  MIN_INPUT_HEIGHT: "32px",
  MODAL_MAX_WIDTH: "800px",
  CONFIRMATION_MODAL_MAX_WIDTH: "500px",
  MODAL_MAX_HEIGHT: "90vh",
  CONFIRMATION_MODAL_MAX_HEIGHT: "80vh",
  TEXTAREA_MAX_HEIGHT: "60vh",
  BUTTON_MIN_WIDTH: "80px",

  // Border radius
  DEFAULT_BORDER_RADIUS: "4px",
  MEDIUM_BORDER_RADIUS: "6px",
  MODAL_BORDER_RADIUS: "8px",
  INFO_BAR_BORDER_WIDTH: "3px",
  NO_BORDER: "none",

  // Z-index
  MODAL_Z_INDEX: 999999,
  CONFIRMATION_MODAL_Z_INDEX: 1000000,
} as const;

export const UI_TYPOGRAPHY = {
  // Font families
  DEFAULT_FONT_FAMILY:
    '"Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif',

  // Line Heights
  LINE_HEIGHT_NORMAL: "1.5",

  // Font sizes
  DEFAULT_FONT_SIZE: "14px",
  MODAL_TITLE_SIZE: "18px",
  FONT_SIZE_SMALL: "12px",

  // Font weights
  FONT_WEIGHT_MEDIUM: 500,
  FONT_WEIGHT_SEMIBOLD: 600,
} as const;

export const UI_EFFECTS = {
  // Shadows
  MODAL_SHADOW: "0 8px 32px rgba(0, 0, 0, 0.4)",
  CONFIRMATION_MODAL_SHADOW: "0 10px 30px rgba(0, 0, 0, 0.3)",

  // Transitions
  DEFAULT_TRANSITION: "all 0.2s ease",

  // Animations
  MODAL_FADE_IN_ANIMATION: "modalFadeIn 0.3s ease-out",
} as const;

export const UI_SPACING = {
  // Spacing units
  SPACING_XXS: "2px",
  SPACING_XS: "4px",
  SPACING_S: "8px",
  SPACING_M: "12px",
  SPACING_L: "16px",
  SPACING_XL: "20px",
  SPACING_XXL: "24px",
} as const;

// ===== UI CURSORS =====

export const UI_CURSORS = {
  POINTER: "pointer",
  NOT_ALLOWED: "not-allowed",
  TEXT: "text",
} as const;

// ===== KEYBOARD CONSTANTS =====

export const KEYBOARD_KEYS = {
  ENTER: "Enter",
  ESCAPE: "Escape",
  SPACE: " ",
} as const;

// ===== TIMING CONSTANTS =====

export const TIMING = {
  // Initialization delays
  TIMELINE_INIT_DELAY: 100, // ms - Delay before initializing timeline repository

  TOAST_ERROR_TIMEOUT: 20000, // ms - Timeout for error toasts (20 seconds for better readability)
  TOAST_DEFAULT_TIMEOUT: 7000, // ms - Default toast display duration

  // Component defaults
  TEXTAREA_DEFAULT_ROWS: 15,
} as const;

// ===== FILE HANDLING CONSTANTS =====

export const FILE_CONSTANTS = {
  // Default file size limits
  DEFAULT_MAX_FILE_SIZE_MB: 10,

  // File size calculation
  BYTES_PER_KB: 1024,

  // File size precision
  FILE_SIZE_DECIMAL_PLACES: 2,
} as const;

// ===== DEFAULT VALUES =====

export const DEFAULT_VALUES = {
  // Guid.Empty
  GUID_EMPTY: "00000000-0000-0000-0000-000000000000",

  // Component defaults
  MAX_FILE_SIZE: FILE_CONSTANTS.DEFAULT_MAX_FILE_SIZE_MB,

  // Entity metadata fallback
  OBJECT_TYPE_CODE_FALLBACK: 0,
} as const;

// ===== ID GENERATION PATTERNS =====

export const ID_PATTERNS = {
  TOAST_ID_PREFIX: "toast-",
} as const;

// ===== VALIDATION CONSTANTS =====

export const VALIDATION = {
  // Stage ID processing
  STAGE_ID_SHORT_LENGTH: 8, // Characters to take from stage ID for subject generation
} as const;

// ===== API CONSTANTS =====

export const API_CONSTANTS = {
  // Timeout values
  DEFAULT_TIMEOUT: 10000, // 10 seconds
  METADATA_TIMEOUT: 15000, // 15 seconds for metadata calls
  
  // OData version
  ODATA_VERSION: "4.0",
  ODATA_MAX_VERSION: "4.0",
  
  // Content types
  CONTENT_TYPE_JSON: "application/json; charset=utf-8",
  
  // Headers
  PREFER_ANNOTATIONS: 'odata.include-annotations="*"',
} as const;

// ===== CACHE CONSTANTS =====

export const CACHE_CONSTANTS = {
  // Default TTL values (in milliseconds)
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  METADATA_TTL: 30 * 60 * 1000, // 30 minutes
  ENTITY_INFO_TTL: 10 * 60 * 1000, // 10 minutes
  
  // Cache key prefixes
  METADATA_PREFIX: "metadata_",
  ENTITY_INFO_PREFIX: "entity_info_",
  BPF_STAGES_PREFIX: "bpf_stages_",
} as const;

// ===== ERROR MESSAGES =====

export const ERROR_MESSAGES = {
  // XRM Errors
  XRM_NOT_AVAILABLE: "Xrm object is not available",
  WEBAPI_NOT_AVAILABLE: "Xrm.WebApi is not available",
  UTILITY_NOT_AVAILABLE: "Xrm.Utility is not available",
  PROCESS_NOT_AVAILABLE: "Xrm.Page.data.process is not available",

  // Network Errors
  NETWORK_ERROR: "Network error occurred",
  TIMEOUT_ERROR: "Request timed out",

  // Validation Errors
  INVALID_ENTITY_ID: "Invalid entity ID",
  INVALID_STAGE_ID: "Invalid stage ID",
  INVALID_FILE_TYPE: "Invalid file type",
  FILE_TOO_LARGE: "File size exceeds maximum allowed size",

  // Generic Errors
  UNKNOWN_ERROR: "An unknown error occurred",
  OPERATION_FAILED: "Operation failed",
} as const;

// ===== PCF COMPLIANCE CONSTANTS =====
// Constants required for PCFRules.md compliance - no hard-coded strings

export const HTML_CONSTANTS = {
  // HTML element types
  type: {
    file: "file",
    button: "button",
  },

  // CSS display values
  display: {
    none: "none",
    flex: "flex",
  },

  // HTML roles
  role: {
    dialog: "dialog",
    document: "document",
    alert: "alert",
  },

  // ARIA attributes
  aria: {
    modal: "true",
    describedBy: "textarea-description",
    labelledBy: "confirmation-modal-title",
  },
} as const;

export const UI_APPEARANCE = {
  // Fluent UI appearance values
  primary: "primary",
  secondary: "secondary",
  outline: "outline",
  subtle: "subtle",
  transparent: "transparent",
} as const;

export const CSS_CONSTANTS = {
  // CSS overflow values
  overflow: {
    hidden: "hidden",
    unset: "unset",
  },

  // CSS white-space values
  whiteSpace: {
    preWrap: "pre-wrap",
  },

  // CSS text-decoration values
  textDecoration: {
    underline: "underline",
  },

  // CSS align-items values
  alignItems: {
    center: "center",
  },

  // CSS justify-content values
  justifyContent: {
    center: "center",
  },
} as const;

export const COMPONENT_CONSTANTS = {
  // React component sizes
  size: {
    tiny: "tiny",
    small: "small",
  },

  // Font weights
  weight: {
    semibold: "semibold",
  },

  // Action types for reducers
  actions: {
    setLoadingAction: "SET_LOADING_ACTION",
    showValidationError: "SHOW_VALIDATION_ERROR",
    hideValidationError: "HIDE_VALIDATION_ERROR",
  },

  // Event types
  events: {
    keydown: "keydown",
  },

  // Toast status
  toast: {
    status: {
      dismissed: "dismissed",
    },
  },

  // File attachment
  file: {
    relationship: {
      label: "label",
    },
  },

  // SVG properties
  svg: {
    xmlns: "http://www.w3.org/2000/svg",
    fill: {
      currentColor: "currentColor",
    },
  },

  // Component display names
  component: {
    displayName: {
      withErrorBoundary: "withErrorBoundary",
      component: "Component",
    },
  },

  // Intent types
  intent: {
    error: "error",
  },

  // Element IDs
  ids: {
    textareaDescription: "textarea-description",
    confirmationModalTitle: "confirmation-modal-title",
  },
} as const;
