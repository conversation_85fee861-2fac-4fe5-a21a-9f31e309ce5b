/**
 * DynamicButtonComponent for ActionButton PCF Control
 * Following PCF-CodingRules.md standards
 */

import * as React from 'react';
import { Button, makeStyles, tokens } from '@fluentui/react-components';
import { callApi, createApiPayload } from '../utils/apiUtils';
import { getXrm } from '../../../SharedLibraries/utils/XrmUtility';
import type {
    IDynamicButtonComponentProps,
    IComponentState,
    ApiAction,
    IButtonConfig,
    ApiResponse,
    ApiError
} from '../types';
import { isApiSuccess } from '../types';
import { DEFAULT_LANGUAGE } from '../../../SharedLibraries/utils/LocalizationUtils';

// Fluent UI v9 styles using makeStyles with design tokens
const useStyles = makeStyles({
    root: {
        display: 'flex',
        gap: tokens.spacingHorizontalM, // Increased spacing between buttons
        padding: tokens.spacingVerticalM,
        alignItems: 'center',
        flexWrap: 'wrap',
    },
    button: {
        minWidth: '100px',
        height: tokens.spacingVerticalXXL,
    },
    updateButton: {
        minWidth: '100px',
        height: tokens.spacingVerticalXXL,
        marginRight: tokens.spacingHorizontalL, // Extra spacing before Remove button
    },
    removeButton: {
        minWidth: '100px',
        height: tokens.spacingVerticalXXL,
        backgroundColor: tokens.colorPaletteRedBackground2,
        color: tokens.colorNeutralForegroundOnBrand,
        '&:hover': {
            backgroundColor: tokens.colorPaletteRedBackground3,
        },
    },
    errorMessage: {
        color: tokens.colorPaletteRedForeground1,
        fontSize: tokens.fontSizeBase200,
        marginTop: tokens.spacingVerticalS,
        padding: tokens.spacingVerticalXS,
        backgroundColor: tokens.colorPaletteRedBackground1,
        borderRadius: tokens.borderRadiusSmall,
        border: `${tokens.strokeWidthThin} solid ${tokens.colorPaletteRedBorder1}`,
    },
    processingIndicator: {
        color: tokens.colorNeutralForeground2,
        fontSize: tokens.fontSizeBase200,
        fontStyle: 'italic',
    },
});

export const DynamicButtonComponent: React.FC<IDynamicButtonComponentProps> = ({
    context,
    notifyOutputChanged,
    sourceControlValue,
    strings
}) => {
    const styles = useStyles();
    const [state, setState] = React.useState<IComponentState>({
        currentValue: sourceControlValue,
        isProcessing: false,
        errorMessage: null,
        lastAction: null,
        language: context.userSettings.languageId?.toString() || DEFAULT_LANGUAGE,
        strings: strings,
    });

    // Update currentValue when the bound field changes
    React.useEffect(() => {
        const newValue = context.parameters.sourceControl.raw || null;
        setState(prev => ({
            ...prev,
            currentValue: newValue,
        }));
    }, [context.parameters.sourceControl.raw]);

    const handleAction = React.useCallback((action: ApiAction): Promise<void> => {
        setState(prev => ({
            ...prev,
            isProcessing: true,
            errorMessage: null,
            lastAction: action,
        }));

        // Create payload with real entity ID from XrmUtility
        const actionName = context.parameters.apiEndpoint.raw || "";
        const payload = createApiPayload(action);

        return callApi(actionName, payload, context.userSettings.languageId)
            .then((result: ApiResponse | ApiError) => {
                if (isApiSuccess(result)) {
                    // Success - refresh page to reload data and show updated buttons
                    setState(prev => ({
                        ...prev,
                        isProcessing: false,
                        errorMessage: null,
                    }));

                    // Refresh form data and ribbon to show updated buttons (Dynamics 365 standard pattern)
                    const xrm = getXrm();
                    if (xrm?.Page?.data?.refresh && xrm?.Page?.ui?.refreshRibbon) {
                        xrm.Page.data.refresh().then(() => {
                            xrm.Page?.ui?.refreshRibbon();
                        });
                    }
                } else {
                    // Handle error response
                    const errorResult = result as { error: string };
                    setState(prev => ({
                        ...prev,
                        isProcessing: false,
                        errorMessage: errorResult.error || strings.ui.errors.apiError,
                    }));
                }
            })
            .catch((error: unknown) => {
                setState(prev => ({
                    ...prev,
                    isProcessing: false,
                    errorMessage: error instanceof Error ? error.message : strings.ui.errors.unknownError,
                }));
            });
    }, [context.parameters.apiEndpoint.raw, context.parameters.sourceControl.raw, notifyOutputChanged, strings]);

    // Button configurations based on current state
    const getButtonConfigs = React.useCallback((): IButtonConfig[] => {
        if (state.currentValue) {
            return [
                {
                    text: strings.ui.buttons.update,
                    action: strings.api.actions.update as ApiAction,
                    variant: 'primary',
                    disabled: state.isProcessing,
                },
                {
                    text: strings.ui.buttons.remove,
                    action: strings.api.actions.remove as ApiAction,
                    variant: 'secondary',
                    disabled: state.isProcessing,
                },
            ];
        } else {
            return [
                {
                    text: strings.ui.buttons.create,
                    action: strings.api.actions.create as ApiAction,
                    variant: 'primary',
                    disabled: state.isProcessing,
                },
            ];
        }
    }, [state.currentValue, state.isProcessing, strings]);

    const buttonConfigs = getButtonConfigs();

    return (
        <div className={styles.root}>
            {buttonConfigs.map((config, index) => {
                // Determine button style based on action
                let buttonClassName = styles.button;
                if (config.action === strings.api.actions.update) {
                    buttonClassName = styles.updateButton;
                } else if (config.action === strings.api.actions.remove) {
                    buttonClassName = styles.removeButton;
                }

                return (
                    <Button
                        key={`${config.action}-${index}`}
                        className={buttonClassName}
                        appearance={config.variant === 'primary' ? 'primary' : 'secondary'}
                        disabled={config.disabled}
                        onClick={() => {
                            handleAction(config.action)
                                .catch(error => {
                                    console.error('Action failed:', error);
                                });
                        }}
                    >
                        {config.text}
                    </Button>
                );
            })}

            {state.isProcessing && (
                <div className={styles.processingIndicator}>
                    {strings.ui.messages.processing}
                </div>
            )}

            {state.errorMessage && (
                <div className={styles.errorMessage}>
                    {state.errorMessage}
                </div>
            )}
        </div>
    );
};