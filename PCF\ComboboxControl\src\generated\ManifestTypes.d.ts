/*
*This is auto generated from the ControlManifest.Input.xml file
*/

// Define IInputs and IOutputs Type. They should match with ControlManifest.
export interface IInputs {
    sourceControl: ComponentFramework.PropertyTypes.Property;
    bpfLookupField: ComponentFramework.PropertyTypes.LookupProperty;
    fieldType: ComponentFramework.PropertyTypes.EnumProperty<"statecode" | "statuscode" | "string" | "picklist" | "datetime" | "all">;
    statecodeField: ComponentFramework.PropertyTypes.WholeNumberProperty;
}
export interface IOutputs {
    sourceControl?: any;
    bpfLookupField?: ComponentFramework.LookupValue[];
    statecodeField?: number;
}
