import * as React from "react";
import { Button } from "@fluentui/react-components";
import { KEYBOARD_KEYS } from "../types";
import type { LocalizationStrings } from "../types/LocalizationTypes";
import {
  bodyStyles,
  confirmationModalContentStyle,
  confirmationModalHeaderStyle,
  confirmationModalOverlayStyle,
  confirmationModalFooterStyle,
  messageStyles,
  modalTitleStyle,
} from "./styles";

interface ConfirmationModalProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmButtonText?: string;
  cancelButtonText?: string | null;
  strings: LocalizationStrings;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  title,
  message,
  onConfirm,
  onCancel,
  confirmButtonText,
  cancelButtonText,
  strings,
}) => {
  const handleBackdropClick = React.useCallback(
    (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        onCancel();
      }
    },
    [onCancel],
  );

  const handleBackdropKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === KEYBOARD_KEYS.ENTER || e.key === KEYBOARD_KEYS.SPACE) {
        if (e.target === e.currentTarget) {
          onCancel();
        }
      }
    },
    [onCancel],
  );

  const handleContentKeyDown = React.useCallback((e: React.KeyboardEvent) => {
    e.stopPropagation();
  }, []);

  const handleConfirm = React.useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onConfirm();
    },
    [onConfirm],
  );

  const handleCancel = React.useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onCancel();
    },
    [onCancel],
  );

  React.useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === KEYBOARD_KEYS.ESCAPE && open) {
        onCancel();
      }
    };

    if (open) {
      document.addEventListener("keydown", handleEscKey);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = "unset";
    };
  }, [open, onCancel]);

  if (!open) return null;

  return (
    <div
      style={confirmationModalOverlayStyle}
      onClick={handleBackdropClick}
      onKeyDown={handleBackdropKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="confirmation-modal-title"
      tabIndex={-1}
    >
      <div
        style={confirmationModalContentStyle}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={handleContentKeyDown}
        role="dialog"
      >
        <div style={confirmationModalHeaderStyle}>
          <h2 id="confirmation-modal-title" style={modalTitleStyle}>
            {title}
          </h2>
        </div>
        <div style={bodyStyles}>
          <p style={messageStyles}>{message}</p>
        </div>
        <div style={confirmationModalFooterStyle}>
          {cancelButtonText !== null && (
            <Button appearance="secondary" onClick={handleCancel}>
              {cancelButtonText ?? strings.ui.buttons.cancel}
            </Button>
          )}
          <Button appearance="primary" onClick={handleConfirm}>
            {confirmButtonText ?? strings.ui.buttons.confirm}
          </Button>
        </div>
      </div>
    </div>
  );
};
