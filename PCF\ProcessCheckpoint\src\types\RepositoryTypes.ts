// All types are now re-exported from SharedLibraries via types/index.ts

// Re-export shared types for backward compatibility
export type {
  RepositoryResult,
  RepositoryOptions,
  IBpfRepository,
  ITimelineRepository,
  EntityInfo,
  TimelineNote,
  DynamicsNote,
  RetrieveMultipleResponse,
  Entity,
  AttachedFile,
  BpfStage,
  BpfCollection,
  TimelineConfig,
  DEFAULT_TIMELINE_CONFIG,
  DEFAULT_SUBJECT_PREFIX,
} from "../../../SharedLibraries/types/RepositoryTypes";

// All deprecated aliases have been removed
// Use Entity and RetrieveMultipleResponse directly
