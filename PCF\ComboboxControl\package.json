{"name": "combobox-project", "version": "0.0.9", "description": "Project containing your PowerApps Component Framework (PCF) control.", "scripts": {"build": "pcf-scripts build", "build-prod": "pcf-scripts build --buildMode production", "clean": "pcf-scripts clean", "lint": "pcf-scripts lint", "lint:fix": "pcf-scripts lint fix", "rebuild": "pcf-scripts rebuild", "start": "pcf-scripts start", "start:watch": "pcf-scripts start watch", "refreshTypes": "pcf-scripts refreshTypes"}, "dependencies": {"@fluentui/react-components": "9.46.2", "react": "16.14.0"}, "devDependencies": {"@eslint/js": "^9.25.1", "@microsoft/eslint-plugin-power-apps": "^0.2.51", "@types/powerapps-component-framework": "^1.3.16", "@types/react": "^16.14.60", "@types/react-dom": "^16.9.24", "eslint-plugin-promise": "^7.1.0", "eslint-plugin-react": "^7.37.2", "globals": "^15.15.0", "pcf-scripts": "^1", "pcf-start": "^1", "react": "^16.14.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}}