/**
 * Timeline Types - Note and timeline-related interfaces
 */

import type { Entity, RepositoryResult } from './CoreTypes';
import type { BpfActionName } from './BpfTypes';
import type { AttachedFile } from './FileTypes';

export interface TimelineConfig {
  subjectPrefix: string;
}

export const DEFAULT_SUBJECT_PREFIX = "PCP" as const;

export const DEFAULT_TIMELINE_CONFIG: TimelineConfig = {
  subjectPrefix: DEFAULT_SUBJECT_PREFIX,
};

export interface DynamicsNote extends Entity {
  annotationid: string;
  subject: string;
  notetext: string;
  createdon: string; // ISO string from Dynamics
  isdocument: boolean;
  filename?: string;
  filesize?: number;
  mimetype?: string;
  documentbody?: string;
}

export interface TimelineNote {
  noteId: string;
  subject: string;
  noteText: string;
  createdOn: Date; // Converted to Date object
  createdBy?: string;
  modifiedOn?: Date;
  modifiedBy?: string;
  action?: BpfActionName;
  stageId?: string;
  attachments?: AttachedFile[];
}

export type Note = DynamicsNote | TimelineNote;

export interface ITimelineRepository {
  createNote(
    entityId: string,
    subject: string,
    noteText: string,
    attachedFiles?: AttachedFile[],
  ): Promise<RepositoryResult<string>>;
  
  getNotesBySubject(
    entityId: string,
    subjectPattern: string,
  ): Promise<RepositoryResult<TimelineNote[]>>;

  getAnnotationDocumentBody(annotationId: string): Promise<
    RepositoryResult<{
      documentbody: string;
      filename: string;
      mimetype: string;
    }>
  >;
  
  createStageNote(
    stageId: string,
    comment: string,
    action: BpfActionName,
    attachedFiles?: AttachedFile[],
  ): Promise<RepositoryResult<string>>;
  
  getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>>;
  
  getLatestStageNote(
    stageId: string,
  ): Promise<RepositoryResult<TimelineNote | null>>;
}

// ===== UTILITY FUNCTIONS =====

/**
 * Converts a raw DynamicsNote to a processed TimelineNote
 */
export function convertDynamicsNoteToTimelineNote(dynamicsNote: DynamicsNote): TimelineNote {
  return {
    noteId: dynamicsNote.annotationid,
    subject: dynamicsNote.subject,
    noteText: dynamicsNote.notetext,
    createdOn: new Date(dynamicsNote.createdon),
    // Additional fields can be mapped as needed
  };
}

/**
 * Type guard to check if a note is a DynamicsNote
 */
export function isDynamicsNote(note: Note): note is DynamicsNote {
  return 'annotationid' in note && 'createdon' in note;
}

/**
 * Type guard to check if a note is a TimelineNote
 */
export function isTimelineNote(note: Note): note is TimelineNote {
  return 'noteId' in note && 'createdOn' in note;
}
