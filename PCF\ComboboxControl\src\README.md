# 📋 Dynamic Combobox Control for Dynamics 365

> **Power Apps Component Framework (PCF) control that displays dynamic options from Business Process Flow (BPF)**

---

## 🎯 Overview

**Dynamic Combobox Control** is a PCF control designed for Dynamics 365 that provides intelligent option selection based on Business Process Flow context.

## 🚀 Key Features

- **🔗 Dynamic BPF Integration**: Automatically retrieves primary entity from selected Business Process Flow
- **📊 State/Status Management**: Displays `statecode` and `statuscode` options with intelligent filtering
- **📝 Attribute Metadata Display**: Shows user-friendly attributes in "Display Name (logicalname)" format when `fieldType = all`
- **🔍 Type-Specific Filtering**: Filters attributes by specific types: `string`, `picklist`, `datetime`
- **✅ Invalid Value Detection**: Automatically detects and handles invalid values
- **💾 Flexible Storage**: Stores integers for state/status codes, strings for attribute logical names
- **🎯 Enhanced UX**: Provides clear, readable attribute display format
- **🔧 Simplified Architecture**: Uses reliable combobox pattern without autocomplete complexity
- **📊 Server-Side Optimization**: Implements server-side AttributeType filtering for better performance

## 🎯 Supported Field Types

| Field Type | Description | AttributeType Filter | Use Case |
|------------|-------------|---------------------|----------|
| `statecode` | Entity state options | N/A | Active/Inactive states |
| `statuscode` | Status reason options | N/A | Detailed status reasons |
| `all` | All entity attributes | None | General attribute selection |
| `string` | Single line of text attributes | String | Text field selection |
| `picklist` | Choice attributes | Picklist | Choice field selection |
| `datetime` | Date and time attributes | DateTime | Date field selection |

## 🏗️ Technical Architecture

This control implements a **Dynamic Metadata Pattern** with server-side filtering for optimal performance:

- **UI Layer**: React components with Fluent UI for consistent styling
- **Data Access Layer**: Direct OData queries to EntityDefinitions and entity data
- **Utility Layer**: SharedLibraries integration for common PCF operations

Key benefits include type safety, server-side optimization, and reusable component architecture.

## 🔧 Configuration & Deployment

Add the control to a field on your Dynamics 365 form and configure the properties in this order:

### Property Configuration

1. **`sourceControl` (Bound Field, Required)**:
   - Stores the selected value
   - For `fieldType` = `statecode` or `statuscode`: Must be a **Whole Number** field
   - For `fieldType` = `all`: Must be a **Single Line of Text** field to store the attribute's logical name

2. **`bpfLookupField` (Bound Field, Required)**:
   - Bind to the lookup field pointing to the Business Process Flow (`workflow` entity)

3. **`fieldType` (Input, Required)**:
   - Options: `statecode`, `statuscode`, `all`
   - `statecode`: Displays State options
   - `statuscode`: Displays Status Reason options, requires `statecodeField`
   - `all`: Displays user-facing attributes of the BPF's entity, saves the logical name

4. **`statecodeField` (Bound Field, Optional)**:
   - Required for `fieldType` = `statuscode` to filter status reasons by the current state
   - Must be a **Whole Number** field

## 📋 Usage Scenarios

### **Scenario 1: Case Status Management**

**Setup:**

- Add control to Case entity form
- Configure with Case BPF (e.g., "Case to Resolution Process")
- Set `fieldType` to `statuscode` for status management

**Workflow:**

1. **Select BPF** → User selects active Business Process Flow
2. **Status Options** → Control displays relevant status codes
3. **Status Update** → User selects appropriate status for current stage

### **Scenario 2: Dynamic Attribute Selection**

**Setup:**

- Add control to custom entity form
- Configure with multi-entity BPF
- Set `fieldType` to `all` for attribute selection

**Workflow:**

1. **BPF Selection** → User selects Business Process Flow
2. **Entity Detection** → Control detects primary entity from BPF
3. **Attribute Display** → Shows user-friendly attribute list
4. **Selection** → User selects desired attribute for processing

## 🚀 How to Use

### Configuration Examples

#### Example 1: Status Reason on Case Form

**Setup:**

- Add the control to the `statuscode` field
- Bind `bpfLookupField` to the "Process" lookup field
- Bind `statecodeField` to the `statecode` field
- Set `fieldType` to `statuscode`

**Result:** The combobox displays `statuscode` options filtered by the current `statecode` of the selected BPF's entity.

#### Example 2: Attribute Selection

**Setup:**

- Add the control to a **Single Line of Text** field
- Bind `bpfLookupField` to the BPF lookup field
- Set `fieldType` to `all`

**Result:** The combobox lists user-facing attributes (e.g., "ticketnumber", "title") of the BPF's entity, saving the selected attribute's logical name.

## 📝 Technical Implementation Details

### Project Structure

```txt
📁 ComboboxControl/
├── 📄 index.ts                          # 🚀 PCF Control entry point and lifecycle
├── 📄 ControlManifest.Input.xml         # ⚙️ PCF manifest configuration
├── 📁 components/
│   └── DynamicCombobox.tsx              # 🎯 Main React component with BPF integration
├── 📁 constants/
│   └── FieldTypes.ts                    # 📊 Field type configurations and mappings
├── 📁 generated/                        # Auto-generated files
│   └── ManifestTypes.d.ts               # ⚙️ Auto-generated types from ControlManifest.Input.xml
├── 📁 strings/
│   └── en.ts                            # 🇬🇧 English localization strings
├── 📄 package.json                      # 📦 Dependencies and build scripts
├── 📄 tsconfig.json                     # ⚙️ TypeScript configuration
└── 📄 README.md                         # 📄 Project documentation
```

---

## 📚 Additional Resources

- [Rule.md](../../../Rule.md) - Comprehensive PCF development guidelines and patterns
- [Setup.md](../../../Setup.md) - Project setup and initialization guide
- [SharedLibraries](../../../SharedLibraries/) - Reusable utilities and components

## 🎉 Conclusion

The Dynamic Combobox Control provides intelligent option selection based on Business Process Flow context, ensuring optimal user experience for Dynamics 365.

**Key Benefits:**

- 🎯 **Dynamic BPF Integration** with automatic entity detection
- 📊 **Server-Side Optimization** with AttributeType filtering
- 💾 **Flexible Storage** supporting multiple data types
- 🔧 **Simplified Architecture** with reliable combobox pattern
- 🌐 **Type-Safe Operations** with comprehensive error handling
- 🛡️ **Enterprise-Ready** with professional design and validation
