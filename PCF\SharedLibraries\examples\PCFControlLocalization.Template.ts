/**
 * Template for implementing centralized localization in PCF controls
 * 
 * This template shows how to use the processControlLocalization function
 * from LocalizationUtils.ts in any PCF control.
 * 
 * Usage:
 * 1. Copy this template to your control's index.ts
 * 2. Replace YourControlStrings with your actual localization interface
 * 3. Import your language files (en.ts, vi.ts, etc.)
 * 4. Update the localizationMap with your strings
 * 5. Set your preferred default language
 */

import type { IInputs, IOutputs } from "./generated/ManifestTypes";
import * as React from "react";
import { YourControlComponent } from "./components/YourControlComponent";
import { processControlLocalization, LANGUAGES } from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from "./strings/en";
import { vietnameseStrings } from "./strings/vi";
// Add more language imports as needed
// import { frenchStrings } from "./strings/fr";
// import { germanStrings } from "./strings/de";
import type { YourControlStrings } from "./types/LocalizationTypes";

// Define localization map for this control
const localizationMap = {
    [LANGUAGES.ENG]: englishStrings,
    [LANGUAGES.VN]: vietnameseStrings,
    // Add more languages as needed
    // 'fr': frenchStrings,
    // 'de': germanStrings,
};

export class YourPCFControl implements ComponentFramework.ReactControl<IInputs, IOutputs> {
    public init(
        _context: ComponentFramework.Context<IInputs>,
        _notifyOutputChanged: () => void,
        _state: ComponentFramework.Dictionary
    ): void {
        // Required by PCF framework
    }

    public updateView(
        context: ComponentFramework.Context<IInputs>
    ): React.ReactElement {

        // 🌍 Centralized localization processing
        // This single function call handles:
        // - Language priority (user setting -> default -> fallback)
        // - Custom text merging
        // - Validation and error handling
        // - Fallback to default strings on errors
        const strings = processControlLocalization<YourControlStrings>(
            localizationMap,                        // Your control's language map
            context.userSettings.languageId,        // User's language setting (LCID)
            LANGUAGES.ENG,                          // Default language for your control
            context.parameters.customText?.raw      // Custom text from control properties
        );

        return React.createElement(
            YourControlComponent,
            {
                context: context,
                strings: strings  // ✅ Fully processed strings ready to use
            }
        );
    }

    public getOutputs(): IOutputs {
        return {};
    }
    
    public destroy(): void {
        // Required by PCF framework
    }
}

/**
 * 📋 ControlManifest.Input.xml Configuration
 * 
 * Add this property to your manifest to enable customText:
 * 
 * <property name="customText" 
 *           display-name-key="Custom Localization (JSON)" 
 *           description-key="JSON string to customize localization strings - supports partial overrides" 
 *           of-type="Multiple" 
 *           usage="input" 
 *           required="false" />
 */

/**
 * 🎯 Usage Examples for customText property:
 * 
 * 1. Simple button override:
 * {"ui":{"buttons":{"save":"Save Document","cancel":"Cancel Operation"}}}
 * 
 * 2. Error message customization:
 * {"errors":{"validation":{"required":"This field is mandatory"}}}
 * 
 * 3. Complete French localization:
 * {"ui":{"buttons":{"save":"Enregistrer","cancel":"Annuler"},"labels":{"title":"Titre du Document"}}}
 * 
 * 4. Business-specific terminology:
 * {"ui":{"labels":{"customer":"Client","order":"Commande"}}}
 */

/**
 * 🔧 Language Priority Logic:
 * 
 * 1. User's language setting (context.userSettings.languageId)
 *    - If available in localizationMap → use it
 *    - If not available → fallback to step 2
 * 
 * 2. Control's default language (specified in processControlLocalization call)
 *    - If available in localizationMap → use it
 *    - If not available → fallback to step 3
 * 
 * 3. System default language (LANGUAGES.ENG)
 *    - If available in localizationMap → use it
 *    - If not available → fallback to step 4
 * 
 * 4. First available language in localizationMap
 *    - Use first language found in the map
 *    - If map is empty → throw error
 */

/**
 * ✅ Benefits of this approach:
 * 
 * 1. **Centralized Logic**: All localization logic in one place
 * 2. **Consistent Behavior**: Same behavior across all PCF controls
 * 3. **Easy Maintenance**: Update LocalizationUtils.ts affects all controls
 * 4. **Flexible**: Supports any number of languages
 * 5. **Robust**: Built-in error handling and fallbacks
 * 6. **Type-Safe**: Full TypeScript support
 * 7. **Partial Overrides**: CustomText can override any subset of strings
 * 8. **Debug-Friendly**: Console warnings for validation issues
 */

/**
 * 🚨 Important Notes:
 * 
 * 1. **Language Keys**: Use LANGUAGES constants for consistency
 * 2. **Default Language**: Choose appropriate default for your control
 * 3. **CustomText Format**: Must be valid JSON string
 * 4. **Placeholders**: Preserve {0}, {1} placeholders in custom text
 * 5. **Error Handling**: Function handles all errors gracefully
 * 6. **Performance**: Minimal overhead, optimized for PCF usage
 */

/**
 * 📁 Required Files Structure:
 * 
 * YourControl/
 * ├── src/
 * │   ├── strings/
 * │   │   ├── en.ts          // English strings
 * │   │   ├── vi.ts          // Vietnamese strings
 * │   │   └── [other].ts     // Additional languages
 * │   ├── types/
 * │   │   └── LocalizationTypes.ts  // String interfaces
 * │   ├── components/
 * │   │   └── YourComponent.tsx     // React component
 * │   └── index.ts           // This file (control entry point)
 * └── ControlManifest.Input.xml     // With customText property
 */
