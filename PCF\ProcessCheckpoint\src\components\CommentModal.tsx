import * as React from "react";
import {
  UI_APPEARANCE,
  COMPONENT_CONSTANTS,
  HTML_CONSTANTS
} from "../../../SharedLibraries/types/Constants";
import { <PERSON><PERSON>, Field, Textarea } from "@fluentui/react-components";
import { ActionButtons } from "./ActionButtons";
import { ConfirmationModal } from "./ConfirmationModal";
import { FileAttachment } from "./FileAttachment";
import {
  modalOverlayStyle,
  modalContentStyle,
  modalHeaderStyle,
  modalTitleStyle,
  modalFooterStyle,
} from "./styles/ModalStyles";
import {
  contentStyle,
  fieldStyle,
  textareaStyle,
} from "./styles/CommentModalStyles";
import { infoBannerStyle } from "./styles/CommonStyles";
import {
  BpfActionType,
  type BpfActionResult,
  type CommentModalProps,
  DEFAULT_FILE_CONFIG,
  DEFAULT_VALUES,
  type FileAttachmentConfig,
  KEYBOARD_KEYS,
  TIMING,
  UI_COLORS,
  UI_CURSORS,
} from "../types";

interface CommentModalState {
  loadingAction: BpfActionType | null;
  isErrorModalOpen: boolean;
  validationError: string;
}

type CommentModalAction =
  | { type: typeof COMPONENT_CONSTANTS.actions.setLoadingAction; payload: BpfActionType | null }
  | { type: typeof COMPONENT_CONSTANTS.actions.showValidationError; payload: string }
  | { type: typeof COMPONENT_CONSTANTS.actions.hideValidationError };

const initialState: CommentModalState = {
  loadingAction: null,
  isErrorModalOpen: false,
  validationError: "",
};

const commentModalReducer = (
  state: CommentModalState,
  action: CommentModalAction,
): CommentModalState => {
  switch (action.type) {
    case COMPONENT_CONSTANTS.actions.setLoadingAction:
      return { ...state, loadingAction: action.payload };
    case COMPONENT_CONSTANTS.actions.showValidationError:
      return {
        ...state,
        isErrorModalOpen: true,
        validationError: action.payload,
      };
    case COMPONENT_CONSTANTS.actions.hideValidationError:
      return { ...state, isErrorModalOpen: false, validationError: "" };
    default:
      return state;
  }
};

export const CommentModal: React.FC<CommentModalProps> = ({
  open,
  onOpenChange,
  value = "",
  onChange,
  bpfActionHandlers,
  buttonConfig,
  isProcessing = false,
  readOnly = false,
  enableFileAttachment = false,
  allowedFileTypes = [],
  maxFileSize = DEFAULT_VALUES.MAX_FILE_SIZE,
  attachedFiles = [],
  onFilesChange,
  timelineRepository,
  strings,
}) => {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const [state, dispatch] = React.useReducer(
    commentModalReducer,
    initialState,
  );

  const fileConfig: FileAttachmentConfig = React.useMemo(
    () => ({
      enabled: enableFileAttachment,
      allowedTypes:
        allowedFileTypes.length > 0
          ? allowedFileTypes
          : DEFAULT_FILE_CONFIG.allowedTypes,
      maxSizeInMB:
        maxFileSize > 0 ? maxFileSize : DEFAULT_FILE_CONFIG.maxSizeInMB,
      maxFiles: DEFAULT_FILE_CONFIG.maxFiles,
    }),
    [enableFileAttachment, allowedFileTypes, maxFileSize],
  );

  React.useEffect(() => {
    if (open && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [open]);

  const handleValidationError = (error: string) => {
    dispatch({ type: COMPONENT_CONSTANTS.actions.showValidationError, payload: error });
  };

  const handleTextareaChange = React.useCallback(
    (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (onChange) {
        onChange(event.target.value);
      }
    },
    [onChange],
  );



  const handleBackdropKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === KEYBOARD_KEYS.ESCAPE) {
        onOpenChange(false);
      } else if (e.key === KEYBOARD_KEYS.ENTER || e.key === KEYBOARD_KEYS.SPACE) {
        if (e.target === e.currentTarget) {
          onOpenChange(false);
        }
      }
    },
    [onOpenChange],
  );

  const handleContentKeyDown = React.useCallback((e: React.KeyboardEvent) => {
    e.stopPropagation();
  }, []);

  const handleBpfAction = (actionType: BpfActionType) => {
    if (isProcessing) return;

    dispatch({ type: COMPONENT_CONSTANTS.actions.setLoadingAction, payload: actionType });

    if (actionType === BpfActionType.CANCEL) {
      if (bpfActionHandlers?.onCancel) {
        bpfActionHandlers.onCancel();
      } else {
        onOpenChange(false);
      }
      dispatch({ type: COMPONENT_CONSTANTS.actions.setLoadingAction, payload: null });
      return;
    }

    let actionPromise: Promise<BpfActionResult> | null = null;

    switch (actionType) {
      case BpfActionType.SUBMIT:
        if (bpfActionHandlers?.onSubmit) {
          actionPromise = bpfActionHandlers.onSubmit();
        }
        break;

      case BpfActionType.ADVISE:
        if (bpfActionHandlers?.onAdvise) {
          actionPromise = bpfActionHandlers.onAdvise();
        }
        break;

      case BpfActionType.APPROVE:
        if (bpfActionHandlers?.onApprove) {
          actionPromise = bpfActionHandlers.onApprove();
        }
        break;

      case BpfActionType.REJECT:
        if (bpfActionHandlers?.onReject) {
          actionPromise = bpfActionHandlers.onReject();
        }
        break;
    }

    if (actionPromise) {
      void actionPromise
        .then((result) => {
          if (result.success) {
            onOpenChange(false);
          }
          return result;
        })
        .catch(() => {
          return { success: false };
        })
        .finally(() => {
          dispatch({ type: COMPONENT_CONSTANTS.actions.setLoadingAction, payload: null });
        });
    } else {
      dispatch({ type: COMPONENT_CONSTANTS.actions.setLoadingAction, payload: null });
    }
  };

  const memoizedTextarea = React.useMemo(
    () => (
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={handleTextareaChange}
        rows={TIMING.TEXTAREA_DEFAULT_ROWS}
        style={{
          ...textareaStyle,
          backgroundColor: readOnly
            ? UI_COLORS.background.LIGHT_GRAY
            : UI_COLORS.WHITE,
          borderColor: readOnly
            ? UI_COLORS.border.READ_ONLY
            : UI_COLORS.border.ACTIVE,
          cursor: readOnly ? UI_CURSORS.NOT_ALLOWED : UI_CURSORS.TEXT,
          color: readOnly ? UI_COLORS.text.READ_ONLY : UI_COLORS.text.DEFAULT,
        }}
        placeholder={
          readOnly
            ? value
              ? ""
              : strings.ui.placeholders.readOnlyComment
            : strings.ui.placeholders.defaultInput
        }
        readOnly={readOnly}
        aria-label={strings.ui.labels.defaultLabel}
        aria-describedby={COMPONENT_CONSTANTS.ids.textareaDescription}
        aria-required={!readOnly}
      />
    ),
    [readOnly, value, strings, handleTextareaChange],
  );

  if (!open) {
    return null;
  }

  return (
    <>
      {state.isErrorModalOpen && (
        <ConfirmationModal
          open={state.isErrorModalOpen}
          title={strings.ui.titles.errorTitle}
          message={state.validationError}
          onConfirm={() => dispatch({ type: COMPONENT_CONSTANTS.actions.hideValidationError })}
          onCancel={() => dispatch({ type: COMPONENT_CONSTANTS.actions.hideValidationError })}
          confirmButtonText={strings.ui.buttons.confirm}
          cancelButtonText={null}
          strings={strings}
        />
      )}

      <div
        onKeyDown={handleBackdropKeyDown}
        style={modalOverlayStyle}
        tabIndex={-1}
        role={HTML_CONSTANTS.role.dialog}
        aria-modal={HTML_CONSTANTS.aria.modal}
      >
        <div
          style={modalContentStyle}
          onClick={(e) => e.stopPropagation()}
          onKeyDown={handleContentKeyDown}
          role={HTML_CONSTANTS.role.document}
        >
          <div style={modalHeaderStyle}>
            <h2 id="comment-modal-title" style={modalTitleStyle}>
              {strings.ui.titles.defaultPopup}
            </h2>
          </div>

          <div style={contentStyle}>
            {readOnly && (
              <div style={infoBannerStyle}>
                {strings.ui.readOnly.description}
              </div>
            )}

            <Field
              label={
                <span
                  style={{
                    color: readOnly
                      ? UI_COLORS.text.READ_ONLY
                      : UI_COLORS.text.DEFAULT,
                  }}
                >
                  {strings.ui.labels.defaultLabel}
                </span>
              }
              style={fieldStyle}
            >
              {memoizedTextarea}
            </Field>

            {fileConfig.enabled && (
              <FileAttachment
                files={attachedFiles}
                onFilesChange={
                  onFilesChange ??
                  (() => {
                    // Default empty handler for file changes
                  })
                }
                config={fileConfig}
                onValidationError={handleValidationError}
                readOnly={readOnly}
                timelineRepository={timelineRepository}
                strings={strings}
              />
            )}
          </div>

          <div style={modalFooterStyle}>
            {buttonConfig?.showCancel !== false && (
              <Button
                appearance={UI_APPEARANCE.secondary}
                onClick={() => handleBpfAction(BpfActionType.CANCEL)}
                disabled={isProcessing}
              >
                {strings.ui.buttons.cancel}
              </Button>
            )}

            <ActionButtons
              buttonConfig={buttonConfig}
              isProcessing={isProcessing}
              loadingAction={state.loadingAction}
              onAction={handleBpfAction}
              strings={strings}
            />
          </div>
        </div>
      </div>
    </>
  );
};
