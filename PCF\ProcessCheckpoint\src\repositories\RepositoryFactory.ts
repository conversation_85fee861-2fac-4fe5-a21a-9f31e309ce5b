import { BpfRepository } from "./BpfRepository";
import { TimelineRepository } from "./TimelineRepository";
import {
  createLanguageLoader,
  LANGUAGES,
  DEFAULT_LANGUAGE,
} from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from "../strings/en";
import { vietnameseStrings } from "../strings/vi";
import type { LocalizationStrings } from "../types/LocalizationTypes";
import {
  type EntityInfo,
  type IBpfRepository,
  type ITimelineRepository,
  type RepositoryOptions,
  type TimelineConfig,
} from "../types";
import { DEFAULT_TIMELINE_CONFIG } from "../../../SharedLibraries/types/RepositoryTypes";

// Create language loader
const languageLoader = createLanguageLoader({
  [LANGUAGES.ENG]: englishStrings,
  [LANGUAGES.VN]: vietnameseStrings
});

export function createBpfRepository(
  options: RepositoryOptions = {},
  strings: LocalizationStrings = languageLoader(DEFAULT_LANGUAGE),
): IBpfRepository {
  return new BpfRepository(options, strings);
}

export function createTimelineRepository(
  entityInfo: EntityInfo,
  timelineConfig: TimelineConfig = DEFAULT_TIMELINE_CONFIG,
  options: RepositoryOptions = {},
  strings: LocalizationStrings = languageLoader(DEFAULT_LANGUAGE),
): ITimelineRepository {
  return new TimelineRepository(entityInfo, timelineConfig, options, strings);
}
