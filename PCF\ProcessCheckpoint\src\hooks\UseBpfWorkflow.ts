import * as React from "react";
import {
  type AttachedFile,
  type BpfActionHand<PERSON>,
  type BpfActionName,
  type BpfActionResult,
  BpfActionType,
  BpfStatusType,
  type IBpfRepository,
  type ITimelineRepository,
  type RepositoryResult,
  TOAST_INTENTS,
  type ToastIntent,
} from "../types";
import {
  createLanguageLoader,
  LANGUAGES,
  DEFAULT_LANGUAGE,
  type SupportedLanguage,
} from "../../../SharedLibraries/utils/LocalizationUtils";
import { englishStrings } from "../strings/en";
import { vietnameseStrings } from "../strings/vi";

interface UseBpfWorkflowProps {
  bpfRepository: IBpfRepository | null;
  timelineRepository: ITimelineRepository | null;
  onClose: () => void;
  currentComment?: string;
  attachedFiles?: AttachedFile[];
  onCommentSubmitted?: () => void;
  onCancel?: () => void;
  onShowToast?: (intent: ToastIntent, title: string, message?: string) => void;
  language?: SupportedLanguage;
  isReadOnly?: boolean;
  onShowConfirmation?: (onConfirm: () => void, onCancel: () => void) => void;
  requireFileAttachment?: boolean;
}

export const useBpfWorkflow = ({
  bpfRepository,
  timelineRepository,
  onClose,
  currentComment,
  attachedFiles,
  onCommentSubmitted,
  onCancel,
  onShowToast,
  language = DEFAULT_LANGUAGE,
  isReadOnly = false,
  onShowConfirmation,
  requireFileAttachment = false,
}: UseBpfWorkflowProps) => {
  const [isProcessing, setIsProcessing] = React.useState(false);
  // Create language loader
  const languageLoader = createLanguageLoader({
    [LANGUAGES.ENG]: englishStrings,
    [LANGUAGES.VN]: vietnameseStrings
  });

  const strings = languageLoader(language);

  const createErrorResultAndToast = React.useCallback(
    (errorMessage: string, defaultTitle?: string): BpfActionResult => {
      onShowToast?.(
        TOAST_INTENTS.ERROR,
        defaultTitle ?? strings.ui.titles.errorTitle,
        errorMessage,
      );

      return { success: false, error: errorMessage };
    },
    [onShowToast, strings],
  );

  const executeBpfAction = (
    action: (bpfProcess: IBpfRepository) => Promise<RepositoryResult<boolean>>,
    successMsg: string,
    errorMsg: string,
  ): Promise<BpfActionResult> => {
    if (!bpfRepository) {
      return Promise.resolve(
        createErrorResultAndToast(
          strings.errors.general.missingBpfContext,
          strings.ui.titles.errorTitle,
        )
      );
    }

    return action(bpfRepository).then((actionResult) => {
      if (actionResult.success) {
        setTimeout(() => {
          onShowToast?.(TOAST_INTENTS.SUCCESS, successMsg);
        }, 0);
        return { success: true, message: successMsg };
      }
      return createErrorResultAndToast(
        actionResult.error ?? errorMsg,
        strings.ui.titles.errorTitle,
      );
    });
  };



  // Pre-validation function to check all conditions before executing BPF action
  const validateBeforeBpfAction = (
    actionType: BpfActionName,
  ): { isValid: boolean; errorMessage?: string } => {
    // Validate that comment is not empty for submit/advise actions
    if (
      (actionType === BpfActionType.SUBMIT ||
        actionType === BpfActionType.ADVISE) &&
      (!currentComment || currentComment.trim() === "")
    ) {
      return {
        isValid: false,
        errorMessage: strings.messages.error.commentRequired,
      };
    }

    // Validate file attachment requirement
    if (
      requireFileAttachment &&
      (actionType === BpfActionType.SUBMIT ||
        actionType === BpfActionType.ADVISE) &&
      (!attachedFiles || attachedFiles.length === 0)
    ) {
      return {
        isValid: false,
        errorMessage: strings.messages.error.fileAttachmentRequired,
      };
    }

    // Validate BPF repository and timeline repository
    if (!bpfRepository || !timelineRepository) {
      return {
        isValid: false,
        errorMessage: strings.errors.general.missingBpfContext,
      };
    }

    // Validate current stage info
    const currentStageInfo = bpfRepository.getCurrentStageInfo();
    if (!currentStageInfo) {
      return {
        isValid: false,
        errorMessage: strings.errors.general.missingBpfContext,
      };
    }

    // Validate stage movement permissions
    if (actionType === BpfActionType.SUBMIT || actionType === BpfActionType.ADVISE) {
      if (!currentStageInfo.canMoveNext) {
        return {
          isValid: false,
          errorMessage: strings.errors.general.cannotMoveNextStage,
        };
      }
    } else if (actionType === BpfActionType.REJECT) {
      if (!currentStageInfo.canMovePrevious) {
        return {
          isValid: false,
          errorMessage: strings.errors.general.cannotMovePreviousStage,
        };
      }
    }

    return { isValid: true };
  };

  const executeActionWithNote = (
    bpfAction: (
      bpfProcess: IBpfRepository,
    ) => Promise<RepositoryResult<boolean>>,
    successMsg: string,
    errorMsg: string,
    actionType: BpfActionName,
  ): Promise<BpfActionResult> => {
    setIsProcessing(true);

    // Pre-validate BEFORE doing anything else
    const validation = validateBeforeBpfAction(actionType);
    if (!validation.isValid) {
      setIsProcessing(false);
      return Promise.resolve(
        createErrorResultAndToast(
          validation.errorMessage || strings.errors.general.unknownError,
          strings.ui.titles.errorTitle,
        ),
      );
    }

    const currentStageInfo = bpfRepository?.getCurrentStageInfo();
    if (!currentStageInfo) {
      setIsProcessing(false);
      return Promise.resolve(
        createErrorResultAndToast(
          strings.errors.general.missingBpfContext,
          strings.ui.titles.errorTitle,
        )
      );
    }

    // Step 1: Create the note first (to avoid losing it if page refreshes after BPF action)
    if (!timelineRepository) {
      setIsProcessing(false);
      return Promise.resolve(
        createErrorResultAndToast(
          strings.errors.general.missingBpfContext,
          strings.ui.titles.errorTitle,
        )
      );
    }

    return timelineRepository
      .createStageNote(
        currentStageInfo.stageId,
        currentComment?.trim() ?? "",
        actionType,
        attachedFiles,
      )
      .then((noteResult) => {
        if (!noteResult.success) {
          return createErrorResultAndToast(
            noteResult.error ?? strings.messages.error.noteCreationFailed,
            strings.ui.titles.errorTitle,
          );
        }

        // Step 2: Get current stage before BPF action for validation
        const currentStageBeforeAction = bpfRepository?.getCurrentStageInfo();

        // Step 3: If note creation is successful, execute the BPF action
        return executeBpfAction(bpfAction, successMsg, errorMsg)
          .then((bpfResult) => {
            // Enhanced validation: Check if stage actually changed (detect plugin prevention)
            if (bpfResult.success && (actionType === BpfActionType.SUBMIT || actionType === BpfActionType.ADVISE || actionType === BpfActionType.REJECT)) {
              const currentStageAfterAction = bpfRepository?.getCurrentStageInfo();
              const stageActuallyChanged = currentStageBeforeAction?.stageId !== currentStageAfterAction?.stageId;

              if (!stageActuallyChanged) {
                // Stage didn't change despite "success" - likely plugin prevented it
                return createErrorResultAndToast(
                  "Stage transition was prevented by business rules. Please check your data and try again.",
                  strings.ui.titles.errorTitle,
                );
              }
            }

            if (bpfResult.success) {
              onCommentSubmitted?.();
            }
            return bpfResult;
          });
      })
      .catch((error) => {
        return createErrorResultAndToast(
          error instanceof Error ? error.message : String(error),
          strings.ui.titles.errorTitle,
        );
      })
      .finally(() => {
        setIsProcessing(false);
      });
  };

  const actionHandlers: BpfActionHandlers = {
    onCancel: () => {
      // In read-only mode, close immediately without confirmation
      if (isReadOnly) {
        onCancel?.();
        onClose();
        return;
      }

      // In editable mode, check if there's content to potentially lose
      if (currentComment?.trim() || (attachedFiles && attachedFiles.length > 0)) {
        if (onShowConfirmation) {
          onShowConfirmation(
            () => {
              // onConfirm callback for the custom modal
              onCancel?.(); // Clear content only if confirmed
              onClose();
            },
            () => {
              // onCancel callback for the custom modal (keep editing)
              // Do nothing, modal will close, and user can continue editing
            },
          );
        }
      } else {
        // No content to lose, close immediately
        onClose();
      }
    },
    onSubmit: () =>
      executeActionWithNote(
        (bpfProcess) => {
          return bpfProcess.moveToNextStage();
        },
        strings.messages.success.submitted,
        strings.messages.error.submitFailed,
        BpfActionType.SUBMIT,
      ),
    onAdvise: () =>
      executeActionWithNote(
        (bpfProcess) => {
          return bpfProcess.moveToNextStage();
        },
        strings.messages.success.advised,
        strings.messages.error.adviseFailed,
        BpfActionType.ADVISE,
      ),
    onApprove: () =>
      executeActionWithNote(
        (bpfProcess) => {
          return bpfProcess.setStatusProcess(BpfStatusType.FINISH);
        },
        strings.messages.success.approved,
        strings.messages.error.approveFailed,
        BpfActionType.APPROVE,
      ),
    onReject: () =>
      executeActionWithNote(
        (bpfProcess) => {
          return bpfProcess.moveToPreviousStage();
        },
        strings.messages.success.rejected,
        strings.messages.error.rejectFailed,
        BpfActionType.REJECT,
      ),
  };

  return {
    actionHandlers,
    isProcessing,
  };
};
