# 📋 Dynamic Combobox Control cho Dynamics 365

> **Power Apps Component Framework (PCF) control hiển thị các tùy chọn động từ Business Process Flow (BPF)**

---

## 🎯 Tổng quan

**Dynamic Combobox Control** là một PCF control được thiết kế cho Dynamics 365 cung cấp lựa chọn tùy chọn thông minh dựa trên ngữ cảnh Business Process Flow.

## 🚀 Tính năng chính

- **🔗 Tích hợp BPF động**: Tự động truy xuất entity chính từ Business Process Flow đã chọn
- **📊 Quản lý State/Status**: Hiển thị các tùy chọn `statecode` và `statuscode` với lọc thông minh
- **📝 Hiển thị Metadata thuộc tính**: Hiển thị các thuộc tính thân thiện với người dùng theo định dạng "Display Name (logicalname)" khi `fieldType = all`
- **🔍 Lọc theo loại cụ thể**: <PERSON><PERSON><PERSON>u<PERSON> t<PERSON>h theo các loại cụ thể: `string`, `picklist`, `datetime`
- **✅ Phát hiện giá trị không hợp lệ**: Tự động phát hiện và xử lý các giá trị không hợp lệ
- **💾 Lưu trữ linh hoạt**: Lưu trữ số nguyên cho mã state/status, chuỗi cho tên logic thuộc tính
- **🎯 UX nâng cao**: Cung cấp định dạng hiển thị thuộc tính rõ ràng, dễ đọc
- **🔧 Kiến trúc đơn giản hóa**: Sử dụng mẫu combobox đáng tin cậy mà không có độ phức tạp của autocomplete
- **📊 Tối ưu hóa phía máy chủ**: Triển khai lọc AttributeType phía máy chủ để có hiệu suất tốt hơn

## 🎯 Các loại trường được hỗ trợ

| Loại trường | Mô tả | Bộ lọc AttributeType | Trường hợp sử dụng |
|-------------|-------|---------------------|-------------------|
| `statecode` | Các tùy chọn trạng thái entity | N/A | Trạng thái Active/Inactive |
| `statuscode` | Các tùy chọn lý do trạng thái | N/A | Lý do trạng thái chi tiết |
| `all` | Tất cả thuộc tính entity | None | Lựa chọn thuộc tính tổng quát |
| `string` | Thuộc tính dòng văn bản đơn | String | Lựa chọn trường văn bản |
| `picklist` | Thuộc tính lựa chọn | Picklist | Lựa chọn trường choice |
| `datetime` | Thuộc tính ngày và giờ | DateTime | Lựa chọn trường ngày |

## 🏗️ Kiến trúc kỹ thuật

Control này triển khai **Mẫu Metadata động** với lọc phía máy chủ để có hiệu suất tối ưu:

- **Lớp Giao diện**: Các React component với Fluent UI để có kiểu dáng nhất quán
- **Lớp Truy cập Dữ liệu**: Truy vấn OData trực tiếp đến EntityDefinitions và dữ liệu entity
- **Lớp Tiện ích**: Tích hợp SharedLibraries cho các hoạt động PCF chung

Các lợi ích chính bao gồm an toàn kiểu dữ liệu, tối ưu hóa phía máy chủ và kiến trúc component có thể tái sử dụng.

## 🔧 Cấu hình & Triển khai

Thêm control vào một trường trên form Dynamics 365 của bạn và cấu hình các thuộc tính theo thứ tự này:

### Cấu hình thuộc tính

1. **`sourceControl` (Trường liên kết, Bắt buộc)**:
   - Trường được liên kết với control
   - Lưu trữ giá trị đã chọn (số nguyên cho state/status, chuỗi cho thuộc tính)

2. **`fieldType` (Input, Bắt buộc)**:
   - Loại trường để hiển thị: `statecode`, `statuscode`, `all`, `string`, `picklist`, `datetime`
   - Xác định loại metadata nào sẽ được truy xuất

3. **`language` (Input, Tùy chọn)**:
   - Ngôn ngữ cho control (vi hoặc en)
   - Mặc định: "en"

### Thuộc tính Manifest PCF

```xml
<!-- Thuộc tính cốt lõi -->
<property name="sourceControl" display-name-key="Field" usage="bound" required="true" />
<property name="fieldType" display-name-key="Loại trường" description-key="Loại trường để hiển thị (statecode, statuscode, all, string, picklist, datetime)" of-type="SingleLine.Text" usage="input" required="true" />
<property name="language" display-name-key="Ngôn ngữ" description-key="Ngôn ngữ cho control (vi hoặc en)" of-type="SingleLine.Text" usage="input" required="false" />
```

### Ví dụ cấu hình

#### Cấu hình State Code

```javascript
sourceControl: "statecode"
fieldType: "statecode"
language: "vi"
```

#### Cấu hình Status Code

```javascript
sourceControl: "statuscode"
fieldType: "statuscode"
language: "vi"
```

#### Cấu hình tất cả thuộc tính

```javascript
sourceControl: "new_selectedattribute"
fieldType: "all"
language: "vi"
```

#### Cấu hình thuộc tính chuỗi

```javascript
sourceControl: "new_stringfield"
fieldType: "string"
language: "vi"
```

## 📋 Kịch bản sử dụng

### **Kịch bản 1: Quản lý trạng thái Case**

**Thiết lập:**

- Thêm control vào form Case entity
- Cấu hình với fieldType = "statuscode"
- Liên kết với trường statuscode

**Luồng công việc:**

1. **Chọn BPF** → Control tự động tải các tùy chọn status cho Case
2. **Hiển thị tùy chọn** → Hiển thị tất cả status code có sẵn
3. **Chọn status** → Cập nhật trường statuscode của Case

### **Kịch bản 2: Lựa chọn thuộc tính động**

**Thiết lập:**

- Thêm control vào form tùy chỉnh
- Cấu hình với fieldType = "all"
- Liên kết với trường văn bản để lưu tên logic thuộc tính

**Luồng công việc:**

1. **Chọn BPF** → Control tự động phát hiện entity chính
2. **Tải metadata** → Truy xuất tất cả thuộc tính entity
3. **Hiển thị danh sách** → Hiển thị "Display Name (logicalname)"
4. **Chọn thuộc tính** → Lưu logical name vào trường

## 🚀 Cách sử dụng

### Trải nghiệm người dùng cuối

#### Lựa chọn State Code

1. **Mở form** → Control hiển thị các tùy chọn state hiện có
2. **Chọn state** → Cập nhật trạng thái entity
3. **Lưu form** → Thay đổi được áp dụng

#### Lựa chọn thuộc tính

1. **Mở form** → Control tải metadata entity từ BPF
2. **Duyệt danh sách** → Xem tất cả thuộc tính có sẵn
3. **Chọn thuộc tính** → Logical name được lưu vào trường

## 📝 Chi tiết triển khai kỹ thuật

### Cấu trúc dự án

```txt
📁 ComboboxControl/
├── 📄 index.ts                                    # 🚀 Điểm vào và vòng đời của PCF Control
├── 📄 ControlManifest.Input.xml                   # ⚙️ Cấu hình manifest của PCF
├── 📁 components/                                  # Lớp các Component Giao diện
│   └── DynamicCombobox.tsx                        # 🎯 Component React chính với tích hợp BPF
├── 📁 constants/                                   # Lớp Hằng số
│   └── FieldTypes.ts                              # 📊 Cấu hình và ánh xạ loại trường
├── 📁 generated/                                   # Các tệp được tạo tự động
│   └── ManifestTypes.d.ts                         # ⚙️ Các kiểu được tạo tự động từ ControlManifest.Input.xml
├── 📁 strings/                                     # Lớp Nội dung tùy chỉnh
│   └── en.ts                                      # 🇬🇧 Các chuỗi nội dung tiếng Anh
├── 📄 package.json                                # 📦 Dependencies và build scripts
├── 📄 tsconfig.json                               # ⚙️ Cấu hình TypeScript
├── 📄 README.md                                   # 📄 Tài liệu dự án (Tiếng Anh)
└── 📄 README_vi.md                                # 📄 Tài liệu dự án (Tiếng Việt)
```

### Mẫu thiết kế chính

#### **Mẫu Metadata động**

- **Truy xuất động**: Tự động phát hiện entity từ BPF context
- **Lọc phía máy chủ**: Sử dụng OData filters để tối ưu hóa hiệu suất
- **Type safety**: TypeScript interfaces cho tất cả metadata operations
- **Caching**: Metadata được cache để giảm API calls

#### **Lợi ích của kiến trúc**

- **Hiệu suất**: Server-side filtering giảm tải dữ liệu
- **Khả năng bảo trì**: Kiến trúc component đơn giản và rõ ràng
- **Khả năng mở rộng**: Dễ dàng thêm loại trường mới
- **Tái sử dụng**: Tích hợp SharedLibraries cho common operations

## 📚 Tài nguyên bổ sung

- [PCFRules.md](../../../PCFRules.md) - Hướng dẫn phát triển PCF toàn diện và các mẫu
- [Setup.md](../../../Setup.md) - Hướng dẫn thiết lập và khởi tạo dự án
- [SharedLibraries](../../../SharedLibraries/) - Các tiện ích và component có thể tái sử dụng

## 🎉 Kết luận

Dynamic Combobox Control cung cấp lựa chọn tùy chọn thông minh dựa trên ngữ cảnh Business Process Flow, đảm bảo trải nghiệm người dùng tối ưu cho Dynamics 365.

**Lợi ích chính:**

- **Tích hợp BPF thông minh**: Tự động phát hiện và thích ứng với ngữ cảnh
- **Hiệu suất tối ưu**: Server-side filtering và caching
- **Linh hoạt cấu hình**: Hỗ trợ nhiều loại trường khác nhau
- **Trải nghiệm người dùng xuất sắc**: Giao diện trực quan và phản hồi nhanh
- **Kiến trúc sạch**: Mã có thể bảo trì và mở rộng
- **An toàn kiểu dữ liệu**: TypeScript đảm bảo độ tin cậy
