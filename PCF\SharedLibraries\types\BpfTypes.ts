/**
 * BPF Types - Business Process Flow interfaces
 */

import type { RepositoryResult } from './CoreTypes';

export type BpfStatusType = 'active' | 'aborted' | 'finished';
export type BpfActionName = 'submit' | 'advise' | 'approve' | 'reject';

export interface BpfStage {
  getId: () => string;
  getName: () => string;
  getStatus: () => string;
  getSteps: () => unknown[];
}

export interface BpfCollection {
  get: (id: string) => BpfStage | null;
  getAll: () => BpfStage[];
  getByName: (name: string) => BpfStage | null;
  getByIndex: (index: number) => BpfStage | null;
  getLength: () => number;
  forEach: (callback: (stage: BpfStage, index: number) => void) => void;
}

export interface BpfStageInfo {
  stageId: string;
  stageName: string;
  isFirstStage: boolean;
  isLastStage: boolean;
  canMoveNext: boolean;
  canMovePrevious: boolean;
}

export interface EntityInfo {
  entityId: string;
  entityLogicalName: string;
  entitySetName: string;
  objectTypeCode: number;
  entityDisplayName?: string;
}

export interface IBpfRepository {
  getStatusProcess(): string | null;
  setStatusProcess(status: BpfStatusType): Promise<RepositoryResult<boolean>>;

  getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>>;

  getCurrentStageInfo(): BpfStageInfo | null;
  getSelectedStageInfo(): BpfStageInfo | null;
  getStageInfoById(stageId: string): BpfStageInfo | null;

  moveToNextStage(): Promise<RepositoryResult<boolean>>;
  moveToPreviousStage(): Promise<RepositoryResult<boolean>>;

  canMoveToNext(): Promise<RepositoryResult<boolean>>;
  canMoveToPrevious(): Promise<RepositoryResult<boolean>>;
  isFirstStage(): Promise<RepositoryResult<boolean>>;
  isLastStage(): Promise<RepositoryResult<boolean>>;

  getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
}
