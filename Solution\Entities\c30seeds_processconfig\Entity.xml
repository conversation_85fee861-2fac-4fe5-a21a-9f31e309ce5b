﻿<?xml version="1.0" encoding="utf-8"?>
<Entity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name LocalizedName="Process Config" OriginalName="Process Config">c30seeds_processconfig</Name>
  <EntityInfo>
    <entity Name="c30seeds_processconfig">
      <LocalizedNames>
        <LocalizedName description="Process Config" languagecode="1033" />
      </LocalizedNames>
      <LocalizedCollectionNames>
        <LocalizedCollectionName description="Process Config" languagecode="1033" />
      </LocalizedCollectionNames>
      <Descriptions>
        <Description description="" languagecode="1033" />
      </Descriptions>
      <attributes>
        <attribute PhysicalName="c30seeds_allowedfiletypes">
          <Type>nvarchar</Type>
          <Name>c30seeds_allowedfiletypes</Name>
          <LogicalName>c30seeds_allowedfiletypes</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Allow File Types" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_approvalstatus">
          <Type>int</Type>
          <Name>c30seeds_approvalstatus</Name>
          <LogicalName>c30seeds_approvalstatus</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format></Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Approval Current Status" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_approvalstatusprevious">
          <Type>int</Type>
          <Name>c30seeds_approvalstatusprevious</Name>
          <LogicalName>c30seeds_approvalstatusprevious</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format></Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Approval Previous Status" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_approvedatecol">
          <Type>nvarchar</Type>
          <Name>c30seeds_approvedatecol</Name>
          <LogicalName>c30seeds_approvedatecol</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Approve Date Column" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_approvercol">
          <Type>nvarchar</Type>
          <Name>c30seeds_approvercol</Name>
          <LogicalName>c30seeds_approvercol</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Approver Column" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_bpfstep">
          <Type>nvarchar</Type>
          <Name>c30seeds_bpfstep</Name>
          <LogicalName>c30seeds_bpfstep</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Business Process Flow Sdk Message Step" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_bpfstepimage">
          <Type>nvarchar</Type>
          <Name>c30seeds_bpfstepimage</Name>
          <LogicalName>c30seeds_bpfstepimage</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Business Process Flow Sdk Message Step Image" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_checkdoc">
          <Type>bit</Type>
          <Name>c30seeds_checkdoc</Name>
          <LogicalName>c30seeds_checkdoc</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_checkdoc">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Check document" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="0" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
              <option value="1" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Check document" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_enablefileattachment">
          <Type>bit</Type>
          <Name>c30seeds_enablefileattachment</Name>
          <LogicalName>c30seeds_enablefileattachment</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_enablefileattachment">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <ExternalTypeName></ExternalTypeName>
            <displaynames>
              <displayname description="Enable File Attachment" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="1" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
              <option value="0" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Enable File Attachment" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_finalstep">
          <Type>bit</Type>
          <Name>c30seeds_finalstep</Name>
          <LogicalName>c30seeds_finalstep</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_finalstep">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Final step" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="0" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
              <option value="1" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Final step" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_isapproversaved">
          <Type>bit</Type>
          <Name>c30seeds_isapproversaved</Name>
          <LogicalName>c30seeds_isapproversaved</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_isapproversaved">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Enable Save Approver" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="0" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
              <option value="1" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Enable Save Approver" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_issendemail">
          <Type>bit</Type>
          <Name>c30seeds_issendemail</Name>
          <LogicalName>c30seeds_issendemail</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_issendemail">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <ExternalTypeName></ExternalTypeName>
            <displaynames>
              <displayname description="Send Email ?" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="1" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
              <option value="0" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Send Email ?" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_isstagesaved">
          <Type>bit</Type>
          <Name>c30seeds_isstagesaved</Name>
          <LogicalName>c30seeds_isstagesaved</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_isstagesaved">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <ExternalTypeName></ExternalTypeName>
            <displaynames>
              <displayname description="Enable Save Stage" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="1" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
              <option value="0" ExternalValue="" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Enable Save Stage" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_maxfilesize">
          <Type>int</Type>
          <Name>c30seeds_maxfilesize</Name>
          <LogicalName>c30seeds_maxfilesize</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>disabled</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>none</Format>
          <MinValue>0</MinValue>
          <MaxValue>128</MaxValue>
          <displaynames>
            <displayname description="Max File Size (MB)" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_mobilestep">
          <Type>nvarchar</Type>
          <Name>c30seeds_mobilestep</Name>
          <LogicalName>c30seeds_mobilestep</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Mobile Sdk Message Step" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_mobilestepimage">
          <Type>nvarchar</Type>
          <Name>c30seeds_mobilestepimage</Name>
          <LogicalName>c30seeds_mobilestepimage</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Mobile Sdk Message Step Image" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_name">
          <Type>nvarchar</Type>
          <Name>c30seeds_name</Name>
          <LogicalName>c30seeds_name</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>PrimaryName|ValidForAdvancedFind|ValidForForm|ValidForGrid|RequiredForForm</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>1</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>1</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>400</MaxLength>
          <Length>800</Length>
          <displaynames>
            <displayname description="Name" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="The name of the custom entity." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_pcfboundfield">
          <Type>nvarchar</Type>
          <Name>c30seeds_pcfboundfield</Name>
          <LogicalName>c30seeds_pcfboundfield</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="PCF Target Column" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_pcfcontrolid">
          <Type>nvarchar</Type>
          <Name>c30seeds_pcfcontrolid</Name>
          <LogicalName>c30seeds_pcfcontrolid</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Control Id" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_pcfcustomtext">
          <Type>ntext</Type>
          <Name>c30seeds_pcfcustomtext</Name>
          <LogicalName>c30seeds_pcfcustomtext</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>1048576</MaxLength>
          <displaynames>
            <displayname description="Custom Text" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_pcflanguage">
          <Type>int</Type>
          <Name>c30seeds_pcflanguage</Name>
          <LogicalName>c30seeds_pcflanguage</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>disabled</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>language</Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Language" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_processconfigId">
          <Type>primarykey</Type>
          <Name>c30seeds_processconfigid</Name>
          <LogicalName>c30seeds_processconfigid</LogicalName>
          <RequiredLevel>systemrequired</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|RequiredForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>0</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>1</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <displaynames>
            <displayname description="Process Config" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for entity instances" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_ProcessId">
          <Type>lookup</Type>
          <Name>c30seeds_processid</Name>
          <LogicalName>c30seeds_processid</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Process" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for Process associated with Process Config." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_processpreviousstageid">
          <Type>lookup</Type>
          <Name>c30seeds_processpreviousstageid</Name>
          <LogicalName>c30seeds_processpreviousstageid</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Process Previous Stage" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_processstageid">
          <Type>lookup</Type>
          <Name>c30seeds_processstageid</Name>
          <LogicalName>c30seeds_processstageid</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Process Current Stage" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for Process Stage associated with Process Config." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_registered">
          <Type>bit</Type>
          <Name>c30seeds_registered</Name>
          <LogicalName>c30seeds_registered</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_registered">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Registered" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="0" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
              <option value="1" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="Registered" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_Roles">
          <Type>nvarchar</Type>
          <Name>c30seeds_roles</Name>
          <LogicalName>c30seeds_roles</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>500</MaxLength>
          <Length>1000</Length>
          <displaynames>
            <displayname description="Roles" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_stagecol">
          <Type>nvarchar</Type>
          <Name>c30seeds_stagecol</Name>
          <LogicalName>c30seeds_stagecol</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>text</Format>
          <MaxLength>100</MaxLength>
          <Length>200</Length>
          <displaynames>
            <displayname description="Stage Column" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_statecode">
          <Type>int</Type>
          <Name>c30seeds_statecode</Name>
          <LogicalName>c30seeds_statecode</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>disabled</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>none</Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Current StateCode" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_statecodeprevious">
          <Type>int</Type>
          <Name>c30seeds_statecodeprevious</Name>
          <LogicalName>c30seeds_statecodeprevious</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>disabled</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>1.0</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>none</Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Previous StateCode" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_statuscode">
          <Type>int</Type>
          <Name>c30seeds_statuscode</Name>
          <LogicalName>c30seeds_statuscode</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>none</Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Current StatusCode" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_statuscodeprevious">
          <Type>int</Type>
          <Name>c30seeds_statuscodeprevious</Name>
          <LogicalName>c30seeds_statuscodeprevious</LogicalName>
          <RequiredLevel>required</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>none</Format>
          <MinValue>0</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Set Previous StatusCode" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="c30seeds_withmobile">
          <Type>bit</Type>
          <Name>c30seeds_withmobile</Name>
          <LogicalName>c30seeds_withmobile</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>1</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <AppDefaultValue>0</AppDefaultValue>
          <optionset Name="c30seeds_processconfig_c30seeds_withmobile">
            <OptionSetType>bit</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="With Mobile" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="" languagecode="1033" />
            </Descriptions>
            <options>
              <option value="0" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="No" languagecode="1033" />
                </labels>
              </option>
              <option value="1" Color="#0000ff" IsHidden="0">
                <labels>
                  <label description="Yes" languagecode="1033" />
                </labels>
              </option>
            </options>
          </optionset>
          <displaynames>
            <displayname description="With Mobile" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="CreatedBy">
          <Type>lookup</Type>
          <Name>createdby</Name>
          <LogicalName>createdby</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Created By" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier of the user who created the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="CreatedOn">
          <Type>datetime</Type>
          <Name>createdon</Name>
          <LogicalName>createdon</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>inactive</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>1</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>datetime</Format>
          <CanChangeDateTimeBehavior>0</CanChangeDateTimeBehavior>
          <Behavior>1</Behavior>
          <displaynames>
            <displayname description="Created On" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Date and time when the record was created." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="CreatedOnBehalfBy">
          <Type>lookup</Type>
          <Name>createdonbehalfby</Name>
          <LogicalName>createdonbehalfby</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Created By (Delegate)" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier of the delegate user who created the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="ImportSequenceNumber">
          <Type>int</Type>
          <Name>importsequencenumber</Name>
          <LogicalName>importsequencenumber</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind</DisplayMask>
          <ImeMode>disabled</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format></Format>
          <MinValue>-2147483648</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Import Sequence Number" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Sequence number of the import that created this record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="ModifiedBy">
          <Type>lookup</Type>
          <Name>modifiedby</Name>
          <LogicalName>modifiedby</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Modified By" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier of the user who modified the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="ModifiedOn">
          <Type>datetime</Type>
          <Name>modifiedon</Name>
          <LogicalName>modifiedon</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>inactive</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>1</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>datetime</Format>
          <CanChangeDateTimeBehavior>0</CanChangeDateTimeBehavior>
          <Behavior>1</Behavior>
          <displaynames>
            <displayname description="Modified On" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Date and time when the record was modified." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="ModifiedOnBehalfBy">
          <Type>lookup</Type>
          <Name>modifiedonbehalfby</Name>
          <LogicalName>modifiedonbehalfby</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Modified By (Delegate)" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier of the delegate user who modified the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="OverriddenCreatedOn">
          <Type>datetime</Type>
          <Name>overriddencreatedon</Name>
          <LogicalName>overriddencreatedon</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForGrid</DisplayMask>
          <ImeMode>inactive</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format>date</Format>
          <CanChangeDateTimeBehavior>0</CanChangeDateTimeBehavior>
          <Behavior>1</Behavior>
          <displaynames>
            <displayname description="Record Created On" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Date and time that the record was migrated." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="OwnerId">
          <Type>owner</Type>
          <Name>ownerid</Name>
          <LogicalName>ownerid</LogicalName>
          <RequiredLevel>systemrequired</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid|RequiredForForm</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes>
            <LookupType id="00000000-0000-0000-0000-000000000000">8</LookupType>
            <LookupType id="00000000-0000-0000-0000-000000000000">9</LookupType>
          </LookupTypes>
          <displaynames>
            <displayname description="Owner" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Owner Id" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="OwningBusinessUnit">
          <Type>lookup</Type>
          <Name>owningbusinessunit</Name>
          <LogicalName>owningbusinessunit</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Owning Business Unit" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for the business unit that owns the record" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="OwningTeam">
          <Type>lookup</Type>
          <Name>owningteam</Name>
          <LogicalName>owningteam</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsLogical>1</IsLogical>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Owning Team" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for the team that owns the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="OwningUser">
          <Type>lookup</Type>
          <Name>owninguser</Name>
          <LogicalName>owninguser</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>0</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsLogical>1</IsLogical>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <LookupStyle>single</LookupStyle>
          <LookupTypes />
          <displaynames>
            <displayname description="Owning User" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Unique identifier for the user that owns the record." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="statecode">
          <Type>state</Type>
          <Name>statecode</Name>
          <LogicalName>statecode</LogicalName>
          <RequiredLevel>systemrequired</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>0</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>1</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <optionset Name="c30seeds_processconfig_statecode">
            <OptionSetType>state</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Status" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="Status of the Process Config" languagecode="1033" />
            </Descriptions>
            <states>
              <state value="0" defaultstatus="1" invariantname="Active">
                <labels>
                  <label description="Active" languagecode="1033" />
                </labels>
              </state>
              <state value="1" defaultstatus="2" invariantname="Inactive">
                <labels>
                  <label description="Inactive" languagecode="1033" />
                </labels>
              </state>
            </states>
          </optionset>
          <displaynames>
            <displayname description="Status" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Status of the Process Config" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="statuscode">
          <Type>status</Type>
          <Name>statuscode</Name>
          <LogicalName>statuscode</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <DisplayMask>ValidForAdvancedFind|ValidForForm|ValidForGrid</DisplayMask>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>1</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <optionset Name="c30seeds_processconfig_statuscode">
            <OptionSetType>status</OptionSetType>
            <IntroducedVersion>*******</IntroducedVersion>
            <IsCustomizable>1</IsCustomizable>
            <displaynames>
              <displayname description="Status Reason" languagecode="1033" />
            </displaynames>
            <Descriptions>
              <Description description="Reason for the status of the Process Config" languagecode="1033" />
            </Descriptions>
            <statuses>
              <status value="1" state="0">
                <labels>
                  <label description="Active" languagecode="1033" />
                </labels>
              </status>
              <status value="2" state="1">
                <labels>
                  <label description="Inactive" languagecode="1033" />
                </labels>
              </status>
            </statuses>
          </optionset>
          <displaynames>
            <displayname description="Status Reason" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Reason for the status of the Process Config" languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="TimeZoneRuleVersionNumber">
          <Type>int</Type>
          <Name>timezoneruleversionnumber</Name>
          <LogicalName>timezoneruleversionnumber</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format></Format>
          <MinValue>-1</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="Time Zone Rule Version Number" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="For internal use only." languagecode="1033" />
          </Descriptions>
        </attribute>
        <attribute PhysicalName="UTCConversionTimeZoneCode">
          <Type>int</Type>
          <Name>utcconversiontimezonecode</Name>
          <LogicalName>utcconversiontimezonecode</LogicalName>
          <RequiredLevel>none</RequiredLevel>
          <ImeMode>auto</ImeMode>
          <ValidForUpdateApi>1</ValidForUpdateApi>
          <ValidForReadApi>1</ValidForReadApi>
          <ValidForCreateApi>1</ValidForCreateApi>
          <IsCustomField>0</IsCustomField>
          <IsAuditEnabled>0</IsAuditEnabled>
          <IsSecured>0</IsSecured>
          <IntroducedVersion>*******</IntroducedVersion>
          <IsCustomizable>1</IsCustomizable>
          <IsRenameable>1</IsRenameable>
          <CanModifySearchSettings>1</CanModifySearchSettings>
          <CanModifyRequirementLevelSettings>1</CanModifyRequirementLevelSettings>
          <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
          <SourceType>0</SourceType>
          <IsGlobalFilterEnabled>0</IsGlobalFilterEnabled>
          <IsSortableEnabled>0</IsSortableEnabled>
          <CanModifyGlobalFilterSettings>1</CanModifyGlobalFilterSettings>
          <CanModifyIsSortableSettings>1</CanModifyIsSortableSettings>
          <IsDataSourceSecret>0</IsDataSourceSecret>
          <AutoNumberFormat></AutoNumberFormat>
          <IsSearchable>0</IsSearchable>
          <IsFilterable>0</IsFilterable>
          <IsRetrievable>0</IsRetrievable>
          <IsLocalizable>0</IsLocalizable>
          <Format></Format>
          <MinValue>-1</MinValue>
          <MaxValue>2147483647</MaxValue>
          <displaynames>
            <displayname description="UTC Conversion Time Zone Code" languagecode="1033" />
          </displaynames>
          <Descriptions>
            <Description description="Time zone code that was in use when the record was created." languagecode="1033" />
          </Descriptions>
        </attribute>
      </attributes>
      <EntitySetName>c30seeds_processconfigs</EntitySetName>
      <IsDuplicateCheckSupported>0</IsDuplicateCheckSupported>
      <IsBusinessProcessEnabled>0</IsBusinessProcessEnabled>
      <IsRequiredOffline>0</IsRequiredOffline>
      <IsInteractionCentricEnabled>0</IsInteractionCentricEnabled>
      <IsCollaboration>0</IsCollaboration>
      <AutoRouteToOwnerQueue>0</AutoRouteToOwnerQueue>
      <IsConnectionsEnabled>0</IsConnectionsEnabled>
      <EntityColor></EntityColor>
      <IsDocumentManagementEnabled>0</IsDocumentManagementEnabled>
      <AutoCreateAccessTeams>0</AutoCreateAccessTeams>
      <IsOneNoteIntegrationEnabled>0</IsOneNoteIntegrationEnabled>
      <IsKnowledgeManagementEnabled>0</IsKnowledgeManagementEnabled>
      <IsSLAEnabled>0</IsSLAEnabled>
      <IsDocumentRecommendationsEnabled>0</IsDocumentRecommendationsEnabled>
      <IsBPFEntity>0</IsBPFEntity>
      <OwnershipTypeMask>UserOwned</OwnershipTypeMask>
      <IsAuditEnabled>0</IsAuditEnabled>
      <IsRetrieveAuditEnabled>0</IsRetrieveAuditEnabled>
      <IsRetrieveMultipleAuditEnabled>0</IsRetrieveMultipleAuditEnabled>
      <IsActivity>0</IsActivity>
      <ActivityTypeMask></ActivityTypeMask>
      <IsActivityParty>0</IsActivityParty>
      <IsReplicated>0</IsReplicated>
      <IsReplicationUserFiltered>0</IsReplicationUserFiltered>
      <IsMailMergeEnabled>0</IsMailMergeEnabled>
      <IsVisibleInMobile>0</IsVisibleInMobile>
      <IsVisibleInMobileClient>0</IsVisibleInMobileClient>
      <IsReadOnlyInMobileClient>0</IsReadOnlyInMobileClient>
      <IsOfflineInMobileClient>0</IsOfflineInMobileClient>
      <DaysSinceRecordLastModified>0</DaysSinceRecordLastModified>
      <MobileOfflineFilters></MobileOfflineFilters>
      <IsMapiGridEnabled>1</IsMapiGridEnabled>
      <IsReadingPaneEnabled>0</IsReadingPaneEnabled>
      <IsQuickCreateEnabled>0</IsQuickCreateEnabled>
      <SyncToExternalSearchIndex>0</SyncToExternalSearchIndex>
      <IntroducedVersion>*******</IntroducedVersion>
      <IsCustomizable>1</IsCustomizable>
      <IsRenameable>1</IsRenameable>
      <IsMappable>1</IsMappable>
      <CanModifyAuditSettings>1</CanModifyAuditSettings>
      <CanModifyMobileVisibility>1</CanModifyMobileVisibility>
      <CanModifyMobileClientVisibility>1</CanModifyMobileClientVisibility>
      <CanModifyMobileClientReadOnly>1</CanModifyMobileClientReadOnly>
      <CanModifyMobileClientOffline>1</CanModifyMobileClientOffline>
      <CanModifyConnectionSettings>1</CanModifyConnectionSettings>
      <CanModifyDuplicateDetectionSettings>1</CanModifyDuplicateDetectionSettings>
      <CanModifyMailMergeSettings>1</CanModifyMailMergeSettings>
      <CanModifyQueueSettings>1</CanModifyQueueSettings>
      <CanCreateAttributes>1</CanCreateAttributes>
      <CanCreateForms>1</CanCreateForms>
      <CanCreateCharts>1</CanCreateCharts>
      <CanCreateViews>1</CanCreateViews>
      <CanModifyAdditionalSettings>1</CanModifyAdditionalSettings>
      <CanEnableSyncToExternalSearchIndex>1</CanEnableSyncToExternalSearchIndex>
      <IconMediumName>c30seeds_/processConfig/icons/icon.svg</IconMediumName>
      <IconSmallName>c30seeds_/processConfig/icons/icon.svg</IconSmallName>
      <IconVectorName>c30seeds_/processConfig/icons/icon.svg</IconVectorName>
      <EnforceStateTransitions>0</EnforceStateTransitions>
      <CanChangeHierarchicalRelationship>1</CanChangeHierarchicalRelationship>
      <EntityHelpUrlEnabled>0</EntityHelpUrlEnabled>
      <EntityHelpUrl></EntityHelpUrl>
      <ChangeTrackingEnabled>0</ChangeTrackingEnabled>
      <CanChangeTrackingBeEnabled>1</CanChangeTrackingBeEnabled>
      <IsEnabledForExternalChannels>0</IsEnabledForExternalChannels>
      <IsMSTeamsIntegrationEnabled>0</IsMSTeamsIntegrationEnabled>
      <IsSolutionAware>0</IsSolutionAware>
    </entity>
  </EntityInfo>
  <FormXml />
  <SavedQueries />
  <RibbonDiffXml />
</Entity>