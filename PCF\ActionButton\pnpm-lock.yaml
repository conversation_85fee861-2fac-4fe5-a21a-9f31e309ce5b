lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@fluentui/react-components':
        specifier: 9.46.2
        version: 9.46.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      react:
        specifier: 16.14.0
        version: 16.14.0
    devDependencies:
      '@eslint/js':
        specifier: ^9.25.1
        version: 9.30.1
      '@microsoft/eslint-plugin-power-apps':
        specifier: ^0.2.51
        version: 0.2.51
      '@types/node':
        specifier: ^18.19.86
        version: 18.19.115
      '@types/powerapps-component-framework':
        specifier: ^1.3.16
        version: 1.3.18
      '@types/react':
        specifier: ^16.14.60
        version: 16.14.65
      eslint-plugin-promise:
        specifier: ^7.1.0
        version: 7.2.1(eslint@9.30.1)
      eslint-plugin-react:
        specifier: ^7.37.2
        version: 7.37.5(eslint@9.30.1)
      globals:
        specifier: ^15.15.0
        version: 15.15.0
      pcf-scripts:
        specifier: ^1
        version: 1.44.2(eslint@9.30.1)(typescript@5.8.3)
      pcf-start:
        specifier: ^1
        version: 1.44.2
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.31.0
        version: 8.35.1(eslint@9.30.1)(typescript@5.8.3)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@azure/abort-controller@2.1.2':
    resolution: {integrity: sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==}
    engines: {node: '>=18.0.0'}

  '@azure/core-auth@1.7.2':
    resolution: {integrity: sha512-Igm/S3fDYmnMq1uKS38Ae1/m37B3zigdlZw+kocwEhh5GjyKjPrXKO2J6rzpC1wAxrNil/jX9BJRqBshyjnF3g==}
    engines: {node: '>=18.0.0'}

  '@azure/core-rest-pipeline@1.16.3':
    resolution: {integrity: sha512-VxLk4AHLyqcHsfKe4MZ6IQ+D+ShuByy+RfStKfSjxJoL3WBWq17VNmrz8aT8etKzqc2nAeIyLxScjpzsS4fz8w==}
    engines: {node: '>=18.0.0'}

  '@azure/core-tracing@1.2.0':
    resolution: {integrity: sha512-UKTiEJPkWcESPYJz3X5uKRYyOcJD+4nYph+KpfdPRnQJVrZfk0KJgdnaAWKfhsBBtAf/D58Az4AvCJEmWgIBAg==}
    engines: {node: '>=18.0.0'}

  '@azure/core-util@1.12.0':
    resolution: {integrity: sha512-13IyjTQgABPARvG90+N2dXpC+hwp466XCdQXPCRlbWHgd3SJd5Q1VvaBGv6k1BIa4MQm6hAF1UBU1m8QUxV8sQ==}
    engines: {node: '>=18.0.0'}

  '@azure/logger@1.2.0':
    resolution: {integrity: sha512-0hKEzLhpw+ZTAfNJyRrn6s+V0nDWzXk9OjBr2TiGIu0OfMr5s2V4FpKLTAK3Ca5r5OKLbf4hkOGDPyiRjie/jA==}
    engines: {node: '>=18.0.0'}

  '@azure/opentelemetry-instrumentation-azure-sdk@1.0.0-beta.9':
    resolution: {integrity: sha512-gNCFokEoQQEkhu2T8i1i+1iW2o9wODn2slu5tpqJmjV1W7qf9dxVv6GNXW1P1WC8wMga8BCc2t/oMhOK3iwRQg==}
    engines: {node: '>=18.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.0':
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.0':
    resolution: {integrity: sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.28.0':
    resolution: {integrity: sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.27.3':
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.1':
    resolution: {integrity: sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.1':
    resolution: {integrity: sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.5':
    resolution: {integrity: sha512-uJnGFcPsWQK8fvjgGP5LZUZZsYGIoPeRjSF5PGwrelYgq7Q15/Ft9NGFp1zglwgIv//W0uG4BevRuSJRyylZPg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.27.1':
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.27.1':
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.27.1':
    resolution: {integrity: sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.27.1':
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.27.1':
    resolution: {integrity: sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.0':
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1':
    resolution: {integrity: sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1':
    resolution: {integrity: sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1':
    resolution: {integrity: sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1':
    resolution: {integrity: sha512-oO02gcONcD5O1iTLi/6frMJBIwWEHceWGSGqrpCmEL8nogiS6J9PBlE48CaK20/Jx1LuRml9aDftLgdjXT8+Cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1':
    resolution: {integrity: sha512-6BpaYGDavZqkI6yT+KSPdpZFfpnd68UKXbcjI9pJ13pvHhPrCKWOOLp+ysvMeA+DxnhuPpgIaRpxRxo5A9t5jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-decorators@7.28.0':
    resolution: {integrity: sha512-zOiZqvANjWDUaUS9xMxbMcK/Zccztbe/6ikvUXaG9nsPH3w6qh5UaPGAnirI/WhIbZ8m3OHU0ReyPrknG+ZKeg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.27.1':
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.27.1':
    resolution: {integrity: sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.27.1':
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.27.1':
    resolution: {integrity: sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.28.0':
    resolution: {integrity: sha512-BEOdvX4+M765icNPZeidyADIvQ1m1gmunXufXxvRESy/jNNyfovIqUyE7MVgGBjWktCoJlzvFA1To2O4ymIO3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.27.1':
    resolution: {integrity: sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.27.1':
    resolution: {integrity: sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.28.0':
    resolution: {integrity: sha512-gKKnwjpdx5sER/wl0WN0efUBFzF/56YZO0RJrSYP4CljXnP31ByY7fol89AzomdlLNzI36AvOTmYHsnZTCkq8Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.27.1':
    resolution: {integrity: sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.27.1':
    resolution: {integrity: sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.28.0':
    resolution: {integrity: sha512-IjM1IoJNw72AZFlj33Cu8X0q2XK/6AaVC3jQu+cgQ5lThWD5ajnuUAml80dqRmOhmPkTH8uAwnpMu9Rvj0LTRA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.27.1':
    resolution: {integrity: sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.28.0':
    resolution: {integrity: sha512-v1nrSMBiKcodhsyJ4Gf+Z0U/yawmJDBOTpEB3mcQY52r9RIyPneGyAS/yM6seP/8I+mWI3elOMtT5dB8GJVs+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.27.1':
    resolution: {integrity: sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.27.1':
    resolution: {integrity: sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.27.1':
    resolution: {integrity: sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-explicit-resource-management@7.28.0':
    resolution: {integrity: sha512-K8nhUcn3f6iB+P3gwCv/no7OdzOZQcKchW6N389V6PD8NUWKZHzndOd9sPDVbMoBsbmjMqlB4L9fm+fEFNVlwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.27.1':
    resolution: {integrity: sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.27.1':
    resolution: {integrity: sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.27.1':
    resolution: {integrity: sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.27.1':
    resolution: {integrity: sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.27.1':
    resolution: {integrity: sha512-6WVLVJiTjqcQauBhn1LkICsR2H+zm62I3h9faTDKt1qP4jn2o72tSvqMwtGFKGTpojce0gJs+76eZ2uCHRZh0Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.27.1':
    resolution: {integrity: sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.27.1':
    resolution: {integrity: sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.27.1':
    resolution: {integrity: sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.27.1':
    resolution: {integrity: sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.27.1':
    resolution: {integrity: sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.27.1':
    resolution: {integrity: sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.27.1':
    resolution: {integrity: sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.27.1':
    resolution: {integrity: sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1':
    resolution: {integrity: sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.27.1':
    resolution: {integrity: sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.28.0':
    resolution: {integrity: sha512-9VNGikXxzu5eCiQjdE4IZn8sb9q7Xsk5EXLDBKUYg1e/Tve8/05+KJEtcxGxAgCY5t/BpKQM+JEL/yT4tvgiUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.27.1':
    resolution: {integrity: sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.27.1':
    resolution: {integrity: sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.27.1':
    resolution: {integrity: sha512-BQmKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.27.7':
    resolution: {integrity: sha512-qBkYTYCb76RRxUM6CcZA5KRu8K4SM8ajzVeUgVdMVO9NN9uI/GaVmBg/WKJJGnNokV9SY8FxNOVWGXzqzUidBg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.27.1':
    resolution: {integrity: sha512-10FVt+X55AjRAYI9BrdISN9/AQWHqldOeZDUoLyif1Kn05a56xVBXb8ZouL8pZ9jem8QpXaOt8TS7RHUIS+GPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.27.1':
    resolution: {integrity: sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.27.1':
    resolution: {integrity: sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.28.0':
    resolution: {integrity: sha512-D6Eujc2zMxKjfa4Zxl4GHMsmhKKZ9VpcqIchJLvwTxad9zWIYulwYItBovpDOoNLISpcZSXoDJ5gaGbQUDqViA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.27.1':
    resolution: {integrity: sha512-ykDdF5yI4f1WrAolLqeF3hmYU12j9ntLQl/AOG1HAS21jxyg1Q0/J/tpREuYLfatGdGmXp/3yS0ZA76kOlVq9Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.27.1':
    resolution: {integrity: sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.27.1':
    resolution: {integrity: sha512-JfuinvDOsD9FVMTHpzA/pBLisxpv1aSf+OIV8lgH3MuWrks19R27e6a6DipIg4aX1Zm9Wpb04p8wljfKrVSnPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.28.0':
    resolution: {integrity: sha512-LOAozRVbqxEVjSKfhGnuLoE4Kz4Oc5UJzuvFUhSsQzdCdaAQu06mG8zDv2GFSerM62nImUZ7K92vxnQcLSDlCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.27.1':
    resolution: {integrity: sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.27.1':
    resolution: {integrity: sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.27.1':
    resolution: {integrity: sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.27.1':
    resolution: {integrity: sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.27.1':
    resolution: {integrity: sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.27.1':
    resolution: {integrity: sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.1':
    resolution: {integrity: sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.27.1':
    resolution: {integrity: sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.27.1':
    resolution: {integrity: sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.27.1':
    resolution: {integrity: sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.27.1':
    resolution: {integrity: sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.28.0':
    resolution: {integrity: sha512-VmaxeGOwuDqzLl5JUkIRM1X2Qu2uKGxHEQWh+cvvbl7JuJRgKGJSfsEF/bUaxFhJl/XAyxBe7q7qSuTbKFuCyg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/preset-react@7.27.1':
    resolution: {integrity: sha512-oJHWh2gLhU9dW9HHr42q0cI0/iHHXTLGe39qvpAZZzagHy0MzYLCnCVV0symeRvzmjHyVU7mw2K06E6u/JwbhA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.0':
    resolution: {integrity: sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.0':
    resolution: {integrity: sha512-jYnje+JyZG5YThjHiF28oT4SIZLnYOcSBb6+SDaFIyzDVSkXQmQQYclJ2R+YxcdmK0AX6x1E5OQNtuh3jHDrUg==}
    engines: {node: '>=6.9.0'}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==}

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.21.0':
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.3.0':
    resolution: {integrity: sha512-ViuymvFmcJi04qdZeDc2whTHryouGcDlaxPqarTD0ZE10ISpxGUVZGZDx4w01upyIynL3iu6IXH2bS1NhclQMw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.14.0':
    resolution: {integrity: sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.15.1':
    resolution: {integrity: sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.30.1':
    resolution: {integrity: sha512-zXhuECFlyep42KZUhWjfvsmXGX39W8K8LFb8AWXM9gSV9dQB+MrJGLKvW6Zw0Ggnbpw0VHTtrhFXYe3Gym18jg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.3':
    resolution: {integrity: sha512-1+WqvgNMhmlAambTvT3KPtCl/Ibr68VldY2XY40SL1CE0ZXiakFR/cbTspaF5HsnpDMvcYYoJHfl4980NBjGag==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@floating-ui/core@1.7.2':
    resolution: {integrity: sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==}

  '@floating-ui/devtools@0.2.1':
    resolution: {integrity: sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==}
    peerDependencies:
      '@floating-ui/dom': '>=1.5.4'

  '@floating-ui/dom@1.7.2':
    resolution: {integrity: sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==}

  '@floating-ui/utils@0.2.10':
    resolution: {integrity: sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==}

  '@fluentui/keyboard-keys@9.0.8':
    resolution: {integrity: sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==}

  '@fluentui/priority-overflow@9.1.15':
    resolution: {integrity: sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==}

  '@fluentui/react-accordion@9.7.3':
    resolution: {integrity: sha512-RgmBfctL41DRMyHjEmTW3+850La6wk+4DziAmY/3ltciO0qjjxra5UxgTQzJGLKbkdRa0/aOfzulCtJqMo4nWg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-alert@9.0.0-beta.107':
    resolution: {integrity: sha512-/A6XoXv7r575nNUOQU6iMREUvy7K3s8hW/LWfsJGT8ZX2HHflfKF1oI8lL9ZGmOuw0JlDEtDCGWKg42oLWPkow==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-aria@9.15.3':
    resolution: {integrity: sha512-4M+wrimplTIXpJrxyodHO2y0ncjiERd3EuGMF+LvUWqIYgyEqvShAV3qFrJ1rHKQx0F0k64Sl6dd0W7OuRSARQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-avatar@9.8.6':
    resolution: {integrity: sha512-V084fMhMBKZQd6Dua9g7V8DuZPAOghl2iz1XA7MeqE71nmDv0HHvkALv7GocSgquNu1zZPhwo46DcLvFGUbwsw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-badge@9.3.2':
    resolution: {integrity: sha512-uMcSRPvb7iU9/8qt2zJiWIikmItfBMdcDEDhGPQzRf+Zp1krzEKSrkDOwF4kEMo+wPoyBWs37RMAAdLuSJPFCA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-breadcrumb@9.2.6':
    resolution: {integrity: sha512-3ANVFDNkXQpmVHJF7Cotq404SWL/lv2NRcIcqg8zv6aCjUSC4dl8fx3QGxGsMrGZ+ckbxgcFMhsSi9XgrJVvNg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-button@9.5.3':
    resolution: {integrity: sha512-xkP2/+5Mwxk8kCdJkcTgP1yETvk5kKy7dM+QXCIXeOvY2BF4/eEaWTcIYL0rAuytaRPc9uMG285yXjw7gb+lSg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-card@9.3.3':
    resolution: {integrity: sha512-a2i7unGaEOUHr0Yn4Da2Ul6QlVeX7vlfpyjng25lOIoYbCFB55QiZX8WYyN5mnUDA+maC/nP6xCEUmjYzPongg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-checkbox@9.4.6':
    resolution: {integrity: sha512-f74lYgmL8ENh4erg7v9FMBHJEm48rn2TRLKIHaDmI66w788iN3Txi5RA2a1NpQzuRu0Bqzbbuz+8neqrfnHfXQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-combobox@9.15.6':
    resolution: {integrity: sha512-F9TqfN5hFakgXkEa6QKEOjOi+kr9qDbRA0JjeDdTsw4UqXEP3SjSktKxpcaucgwBE2Sz2YeE0Ush50f7ZpsZ7A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-components@9.46.2':
    resolution: {integrity: sha512-WqHu0rhu83T35tipARuWRwFIw1mMT6q6WNAhXSkNUEfmKiW8+96NbnxAykaO07OPMMMbsUqk9SP8hD4oqMiTkQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'
      scheduler: ^0.19.0 || ^0.20.0

  '@fluentui/react-context-selector@9.2.2':
    resolution: {integrity: sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'
      scheduler: '>=0.19.0 <=0.23.0'

  '@fluentui/react-dialog@9.13.6':
    resolution: {integrity: sha512-IC5fP3VGQtO0XO08DWD9uneBdqsyCE5TIdlDsiA5rypBINGpoZ41OrNQIop1wAzT9VGSxXdTmPZkWXY8HRR6sQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-divider@9.3.2':
    resolution: {integrity: sha512-YCaPVDhwQ3jYui0oc+R2rWnOGBS+iQqMkz7oxk3Uu+rDfH259daCkX5Wmvnvlb0sk2J9l3+cpEq++cf54Ierog==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-drawer@9.8.6':
    resolution: {integrity: sha512-ef0jfuZtHpehWosb0RONBq1wE+kSc5SUOwyytBgt8MQnOnET9nWE0x6Hg1iOLVHp56iwqzGn7s4lJQhlhZSIFQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-field@9.3.6':
    resolution: {integrity: sha512-17v+WkFVeSbl70Dq0z35G8iP6kQ92S1WNjJKXSHToYDWpaMhBlwVYm7NsQQ5Veb425+q4o5bBk59KYNryOMwXw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-icons@2.0.305':
    resolution: {integrity: sha512-lxJZsW4IKaPaIrlaZlvDFujztKwWXSR3tUMBUBG0WtEGoQkbrWhrt8fqzhQ9BEbq02FtifLFUpaIqiJ326//Rw==}
    peerDependencies:
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-image@9.2.2':
    resolution: {integrity: sha512-HYBQdaYr7xt1tSlPUEGyu/U3+TF3z2s2qxN41WQwjo93fhFwnVsYR1hDYQqVFa0tIOY1/ZGG3BIvgNINAFe/hw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infobutton@9.0.0-beta.91':
    resolution: {integrity: sha512-kuQRDJ+btPEaa19wqrkY+dBLic73IZZYpLAbMMyj0TgFU+4ATa8UI7SkVyEWBwRIFm8Grwl3378TyU8Mvgl2wQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infolabel@9.3.6':
    resolution: {integrity: sha512-19MNvTjipkieN4i6kWlaSSg1ijrfOuj3lVJ/Xsa1W7MjJUMkjKWLB0wHbJqPPu916s/k5AIvAtmoUyQoMN6x8Q==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-input@9.6.6':
    resolution: {integrity: sha512-YDpbmg7Xjpkyjc1F9Xk9FmSeK8smnUhKktMMKkXJAzNnXhHebtVBra5qAGC0z2LlboROo6ZNbn4CuFmd0dCdsA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-jsx-runtime@9.1.2':
    resolution: {integrity: sha512-igGuh0P7Gd09Kk3g6JwjnaRIRk+mluCbpf+KcAUde6bxZ/5qB50HGX+DOGWa3+RPd5240+HLBxpT3Y985INgqw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-label@9.2.2':
    resolution: {integrity: sha512-4SOSVCFzl5Of4D9OBkz0K4+jpkkuLBmurRzwTvgNkArMYYW7NpZZoVzxsGJLtXYMB+Uf2zIRyHUUM0CDTtGOjw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-link@9.5.3':
    resolution: {integrity: sha512-sEOU1jQoZODD2kVgyjYY/h5ylicj0ojVnLW2AaXnAK6kgkUiioDGDXz7rHsmVhTqKkLfUV2ZClFjc+DHI4b64A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-menu@9.17.6':
    resolution: {integrity: sha512-qM63MDY1ueJSlZwP1M4dVM91QpZt7YdhIKTkyVWLVIjCVwU+GAmxHk0B+23u5yjTjAEBfZvSqUaW8b33OBNv5A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-message-bar@9.5.3':
    resolution: {integrity: sha512-w6AQIz/O3HQX2S8LFnH7qNbhybG5KXv19BY8xeOchQJ+NAw3U1GgKX5+7UxKU/DNsBSL5adQoe3SUvexALfSUw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-motion-components-preview@0.6.2':
    resolution: {integrity: sha512-vVFnNXQESs2VbLQMx7Quu/49D+aUmXTS6/oTEfkK5fW5CCec11K4BNYkaQMorhMudbdBIPz4d4thzNx7YenHcg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-motion@9.9.0':
    resolution: {integrity: sha512-xgm/CkU1UvemooplEFKJL9mfGJFvzId2DJ1WYTFAa5TSZMtzOAZuPuwS/PrPNFuwjnhvCMShDj8zazgvR5i37A==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-overflow@9.4.6':
    resolution: {integrity: sha512-7a3Wf21lPSmMnmxYNyIPEEEne5SJCsd0f/3Jhmx32w1t0xW69dErq+za6MGuKo5n3B0aNGX+ItH9q8UmnzLXOA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-persona@9.4.6':
    resolution: {integrity: sha512-2tC3YzzOrOHcGS0XD0+WMmnm3l25qfxp+1xGStOKE59WgDDRWILZ8Cpr16TdmQFtw4b1z7xxLt44yMV0560m0A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-popover@9.11.6':
    resolution: {integrity: sha512-JgyM5gugBzx5F2ZB0pNuK0tG7DtLUInIHUtRdYv2jS+liUdDt+6KICSHZl/xNK1Tv+fNUtbWWXpvXtw/BMCnNg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-portal@9.6.4':
    resolution: {integrity: sha512-7NTxAZ0rMjbD0bvZUdcicbSHmbf/WnTT5YOhmevHWapEuBG4is/mZkMnn0s+x8heUrkE3Xa0NOaVpZvzG2p/ew==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-positioning@9.18.5':
    resolution: {integrity: sha512-cm3ZyLFpI9zOvO66UAT45Kn2f332Vw2WXUsUF4G+8WcnXroh/9kX4Wm0CfIbM5X7OOZW08mdq6LFi+MPbD+HhQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-progress@9.3.6':
    resolution: {integrity: sha512-/wgg8D+Rtq4HyOWgrehXNh1NPONivivXQaR7vO+1g6A2O8kCAqKhcxGDLFDrVTz2ngbqkVKpnBaJcTYBUO9+1Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-provider@9.21.3':
    resolution: {integrity: sha512-2f6hvro9o3xrIuV2LsTd4CsIPIv49J3uyDjN3psA83KSKc8LxmmduJrj5SMfZvlXLgdd8pL+Vy6ugwUCUysVDg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-radio@9.4.6':
    resolution: {integrity: sha512-/9J4JqSK82+tYKyVRe+F6ClEGbjXGSXghQxZu7Yy6pGzGIkrOjTSN43fY7NBUZTO0gj+kGhGc/ouVT7StNzCiA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-select@9.3.6':
    resolution: {integrity: sha512-Ivbbx8MNTfd9ikgKZAKYljDbvcVfrEl4CnpRqPcLgHcRFzAQzM1Ys1Dii08iYjKkSYj1FK7haMtthQc5P21eKQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-shared-contexts@9.24.0':
    resolution: {integrity: sha512-GA+uLv711E+YGrAP/aVB15ozvNCiuB2ZrPDC9aYF+A6sRDxoZZG8VgHjhQ/YWJfVjDXLky4ihirknzsW1sjGtg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-skeleton@9.3.6':
    resolution: {integrity: sha512-K8aPQCV7ayHC4bC3NLsTKvNR7w+HoFrEQsTDf73W4BlAhJ2AbI/r6aIBVzV11olOvIkOHqE7w52rc3Lc+4B52Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-slider@9.4.6':
    resolution: {integrity: sha512-yKqq2grLjQiNBq1EApZb0N8msX/u9U/4Bbq0Fo/+bhf11pa+hCMQ5UkQLkrKjik5v/pF3ZSO7EOyMKxmlrB8zg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinbutton@9.4.6':
    resolution: {integrity: sha512-LF95WmP8DZ6wq07Z4jMoTmEf81WPn21ubysURBmBEXmA5zSRD61jutW2ferpWxxXGQKtZ/NFnovFqX/AhR6Nug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinner@9.6.2':
    resolution: {integrity: sha512-HxgUQ2ZJtiGqMFlln5WicE/++89NRCELQbkxRvmJapqbYkXI108Cz/JF2RaLyWSYMEyaxHUd1sdxbCEOuRegpw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-switch@9.3.6':
    resolution: {integrity: sha512-yP+tXmsChEy2UJYf5SHP8ssfxjyVdavikb/SD2QAlZYutNSimuUdA1mypxkdzkv7li98fEnaYHqDSj3NTAQt3Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-table@9.17.6':
    resolution: {integrity: sha512-YhAlzD8Cf8A59vKsf9xtpqbcH7hHbtPBWzFyLCP/y/VVYZ0fAlcwULbrvvgc+PpdBur7XeczxGGIqe589w6vuw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabs@9.8.3':
    resolution: {integrity: sha512-UGcJoz6hbYmxC0yPmGJ3wls3bm3p/ROF6p/PzRvk3ceKXpQHiXElC2TIDgm0VPwIaA0HvYl+9AptSlLtdQe6EQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabster@9.25.3':
    resolution: {integrity: sha512-y5sNkYqZP3CaQLRfWwqqlenb5TGjb8lAExr68QYkuwzAgvqlNguxTbYM2PSgWG14tfqmW8cG9UxQdp9LGw2Nfw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tags@9.6.6':
    resolution: {integrity: sha512-IESredJstOTuoWJasCNdfvtUh/LLs6mYeVqn3C4mkHyngP/vepXyvvcP/e/ZI2mZi1SWE7MhrM1ubNIUOrVCIQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-text@9.5.2':
    resolution: {integrity: sha512-zhTBiy6k61e/my3VbTvFGlvOO6sQ3umFA3CTrvTuTB093giN6OqQ5rvHky2XwEQiVad9WoQ14MPgO0K0ORLluQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-textarea@9.5.6':
    resolution: {integrity: sha512-yLUntuLjNvrLJ+g2OjtImdkYNAqPdTfG5X9xPvt6h447c0yFzAd6oW9NqjAahDoEYTwMwWUb5+zv7cR88MHbUg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-theme@9.1.24':
    resolution: {integrity: sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==}

  '@fluentui/react-toast@9.5.4':
    resolution: {integrity: sha512-iLcNrtu3iVp4h8pw9KumhXwUrM8uUm3EU57hBU1FxXQpreEpnPuJLxQx1NmuNCeiBMFg2S0rtfsqI/aTWvG2nQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-toolbar@9.5.6':
    resolution: {integrity: sha512-nDujwd9vLwykAeF3lOVBuXZmGcyDLjwRcLHuvTKUGNvykr8TDbxeCq9ln2ZvfNhXHCzYvNnWPBIJKbndOVK+oQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tooltip@9.7.6':
    resolution: {integrity: sha512-xtkJ0A20jmmqw5KFypJZXpubHyKK4vQekjbr9IUFdHaxbyAo+9yxrYcdGQS0MdNnYwSlf8x8ShdwsUU3VccqVA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tree@9.11.6':
    resolution: {integrity: sha512-H14/IT0g77Z8DSMCwD88fXqeqOeot+ajh49FKgfU6K+xefz1/YgYNJ/Xy3ukODHkZoznS+XfvjRXnlHer5U/1w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-utilities@9.22.0':
    resolution: {integrity: sha512-O4D51FUyn5670SjduzzN1usmwWAmFPQA00Gu6jJrbDXvOXTpOAO/ApkLpSW87HChKGrj8Y0gjFHtK8xpC3qOCg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-virtualizer@9.0.0-alpha.67':
    resolution: {integrity: sha512-R/zxdlVymbRtZ/yHvd5cO4i9PZAc9u3hvMouJ8DrcTZeuudjEnPfH5svKZgW1h5XY5Oz8LzyWMmISP1oGkYeEw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/tokens@1.0.0-alpha.21':
    resolution: {integrity: sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==}

  '@griffel/core@1.19.2':
    resolution: {integrity: sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==}

  '@griffel/react@1.5.30':
    resolution: {integrity: sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==}
    peerDependencies:
      react: '>=16.8.0 <20.0.0'

  '@griffel/style-types@1.3.0':
    resolution: {integrity: sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}

  '@jridgewell/gen-mapping@0.3.12':
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.10':
    resolution: {integrity: sha512-0pPkgz9dY+bijgistcTTJ5mR+ocqRXLuhXHYdzoMmmoJ2C9S46RCm2GMUbatPEUK9Yjy26IrAy8D/M00lLkv+Q==}

  '@jridgewell/sourcemap-codec@1.5.4':
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}

  '@jridgewell/trace-mapping@0.3.29':
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}

  '@microsoft/applicationinsights-web-snippet@1.0.1':
    resolution: {integrity: sha512-2IHAOaLauc8qaAitvWS+U931T+ze+7MNWrDHY47IENP5y2UA0vqJDu67kWZDdpCN1fFC77sfgfB+HV7SrKshnQ==}

  '@microsoft/eslint-plugin-power-apps@0.2.51':
    resolution: {integrity: sha512-h/V7uSKCBXHTvc2ULbkvOA02zk+7JVTL7iNPFiyAiVPyei3BrXohAG7EqOTtfsRN4LTT9yKjTwElZJDV5VlqLw==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@opentelemetry/api-logs@0.200.0':
    resolution: {integrity: sha512-IKJBQxh91qJ+3ssRly5hYEJ8NDHu9oY/B1PXVSCWf7zytmYO9RNLB0Ox9XQ/fJ8m6gY6Q6NtBWlmXfaXt5Uc4Q==}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/core@1.30.1':
    resolution: {integrity: sha512-OOCM2C/QIURhJMuKaekP3TRBxBKxG/TWWA0TL2J6nXUtDnuCtccy49LUJF8xPFXMX+0LMcxFpCo8M9cGY1W6rQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/core@2.0.1':
    resolution: {integrity: sha512-MaZk9SJIDgo1peKevlbhP6+IwIiNPNmswNL4AF0WaQJLbHXjr9SrZMgS12+iqr9ToV4ZVosCcc0f8Rg67LXjxw==}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/instrumentation@0.200.0':
    resolution: {integrity: sha512-pmPlzfJd+vvgaZd/reMsC8RWgTXn2WY1OWT5RT42m3aOn5532TozwXNDhg1vzqJ+jnvmkREcdLr27ebJEQt0Jg==}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/resources@1.30.1':
    resolution: {integrity: sha512-5UxZqiAgLYGFjS4s9qm5mBVo433u+dSPUFWVWXmLAD4wB65oMCoXaJP1KJa9DIYYMeHu3z4BZcStG3LC593cWA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/resources@2.0.1':
    resolution: {integrity: sha512-dZOB3R6zvBwDKnHDTB4X1xtMArB/d324VsbiPkX/Yu0Q8T2xceRthoIVFhJdvgVM2QhGVUyX9tzwiNxGtoBJUw==}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.3.0 <1.10.0'

  '@opentelemetry/sdk-trace-base@1.30.1':
    resolution: {integrity: sha512-jVPgBbH1gCy2Lb7X0AVQ8XAfgg0pJ4nvl8/IiQA6nxOsPvS+0zMJaFSs2ltXe0J6C8dqjcnpyqINDJmU30+uOg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/sdk-trace-base@2.0.1':
    resolution: {integrity: sha512-xYLlvk/xdScGx1aEqvxLwf6sXQLXCjk3/1SQT9X9AoN5rXRhkdvIFShuNNmtTEPRBqcsMbS4p/gJLNI2wXaDuQ==}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.3.0 <1.10.0'

  '@opentelemetry/sdk-trace-web@2.0.1':
    resolution: {integrity: sha512-R4/i0rISvAujG4Zwk3s6ySyrWG+Db3SerZVM4jZ2lEzjrNylF7nRAy1hVvWe8gTbwIxX+6w6ZvZwdtl2C7UQHQ==}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/semantic-conventions@1.28.0':
    resolution: {integrity: sha512-lp4qAiMTD4sNWW4DbKLBkfiMZ4jbAboJIGOQr5DvciMRI494OapieI9qiODpOt0XBr1LjIDy1xAGAnVs5supTA==}
    engines: {node: '>=14'}

  '@opentelemetry/semantic-conventions@1.34.0':
    resolution: {integrity: sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==}
    engines: {node: '>=14'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==}
    engines: {node: '>= 10.0.0'}

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    resolution: {integrity: sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==}
    cpu: [x64]
    os: [linux]

  '@socket.io/component-emitter@3.1.2':
    resolution: {integrity: sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==}

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@types/cors@2.8.19':
    resolution: {integrity: sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg==}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/node@18.19.115':
    resolution: {integrity: sha512-kNrFiTgG4a9JAn1LMQeLOv3MvXIPokzXziohMrMsvpYgLpdEt/mMiVYc4sGKtDfyxM5gIDF4VgrPRyCw4fHOYg==}

  '@types/powerapps-component-framework@1.3.18':
    resolution: {integrity: sha512-g5JNTwjg/IIdHOuylNAHZh7FrJzeBPCenFVEihf9tB5t0SlP82YBa0InXRa6brFpVy06RFH217sDGYixJqy2IA==}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react@16.14.65':
    resolution: {integrity: sha512-Guc3kE+W8LrQB9I3bF3blvNH15dXFIVIHIJTqrF8cp5XI/3IJcHGo4C3sJNPb8Zx49aofXKnAGIKyonE4f7XWg==}

  '@types/scheduler@0.16.8':
    resolution: {integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==}

  '@types/shimmer@1.2.0':
    resolution: {integrity: sha512-UE7oxhQLLd9gub6JKIAhDq06T0F6FnztwMNRvYgjeQSBeMc1ZG/tA47EwfduvkuQS8apbkM/lpLpWsaCeYsXVg==}

  '@typescript-eslint/eslint-plugin@8.35.1':
    resolution: {integrity: sha512-9XNTlo7P7RJxbVeICaIIIEipqxLKguyh+3UbXuT2XQuFp6d8VOeDEGuz5IiX0dgZo8CiI6aOFLg4e8cF71SFVg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.35.1
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.35.1':
    resolution: {integrity: sha512-3MyiDfrfLeK06bi/g9DqJxP5pV74LNv4rFTyvGDmT3x2p1yp1lOd+qYZfiRPIOf/oON+WRZR5wxxuF85qOar+w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/project-service@8.35.1':
    resolution: {integrity: sha512-VYxn/5LOpVxADAuP3NrnxxHYfzVtQzLKeldIhDhzC8UHaiQvYlXvKuVho1qLduFbJjjy5U5bkGwa3rUGUb1Q6Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.35.1':
    resolution: {integrity: sha512-s/Bpd4i7ht2934nG+UoSPlYXd08KYz3bmjLEb7Ye1UVob0d1ENiT3lY8bsCmik4RqfSbPw9xJJHbugpPpP5JUg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.35.1':
    resolution: {integrity: sha512-K5/U9VmT9dTHoNowWZpz+/TObS3xqC5h0xAIjXPw+MNcKV9qg6eSatEnmeAwkjHijhACH0/N7bkhKvbt1+DXWQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/type-utils@8.35.1':
    resolution: {integrity: sha512-HOrUBlfVRz5W2LIKpXzZoy6VTZzMu2n8q9C2V/cFngIC5U1nStJgv0tMV4sZPzdf4wQm9/ToWUFPMN9Vq9VJQQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.35.1':
    resolution: {integrity: sha512-q/O04vVnKHfrrhNAscndAn1tuQhIkwqnaW+eu5waD5IPts2eX1dgJxgqcPx5BX109/qAz7IG6VrEPTOYKCNfRQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.35.1':
    resolution: {integrity: sha512-Vvpuvj4tBxIka7cPs6Y1uvM7gJgdF5Uu9F+mBJBPY4MhvjrjWGK4H0lVgLJd/8PWZ23FTqsaJaLEkBCFUk8Y9g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.35.1':
    resolution: {integrity: sha512-lhnwatFmOFcazAsUm3ZnZFpXSxiwoa1Lj50HphnDe1Et01NF4+hrdXONSUHIcbVu2eFb1bAf+5yjXkGVkXBKAQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.35.1':
    resolution: {integrity: sha512-VRwixir4zBWCSTP/ljEo091lbpypz57PoeAQ9imjG+vbeof9LplljsL1mos4ccG6H9IjfrVGM359RozUnuFhpw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typespec/ts-http-runtime@0.2.3':
    resolution: {integrity: sha512-oRhjSzcVjX8ExyaF8hC0zzTqxlVuRlgMHL/Bh4w3xB9+wjbm0FpXylVU/lBrn+kgphwYTrOk3tp+AVShGmlYCg==}
    engines: {node: '>=18.0.0'}

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-import-attributes@1.9.5:
    resolution: {integrity: sha512-n02Vykv5uA3eHGM/Z2dQrcD56kL8TyDb2p1+0P83PClMnC/nc+anbQRhIOWnSq4Ke/KvDPrY3C9hDtC/A3eHnQ==}
    peerDependencies:
      acorn: ^8

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@7.1.3:
    resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
    engines: {node: '>= 14'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  applicationinsights@2.9.7:
    resolution: {integrity: sha512-dxIVB2AAEMec3FDiYThgEbc9R4u1TatrzL+kFgOf+ABaEgHm8+i8ngVLHfKObjHvy2HHPf810OLWTrqyeWT/oA==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      applicationinsights-native-metrics: '*'
    peerDependenciesMeta:
      applicationinsights-native-metrics:
        optional: true

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}

  array-includes@3.1.9:
    resolution: {integrity: sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==}
    engines: {node: '>= 0.4'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}

  async-each-series@0.1.1:
    resolution: {integrity: sha512-p4jj6Fws4Iy2m0iCmI2am2ZNZCgbdgE+P8F/8csmn2vx7ixXrO2zGcuNsD46X5uZSVecmkEy/M06X2vG8KD6dQ==}
    engines: {node: '>=0.8.0'}

  async-function@1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}

  async-hook-jl@1.7.6:
    resolution: {integrity: sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==}
    engines: {node: ^4.7 || >=6.9 || >=7.3}

  async-listener@0.6.10:
    resolution: {integrity: sha512-gpuo6xOyF4D5DE5WvyqZdPA3NGhiT6Qf07l7DCB0wwDEsLvDIbCr6j9S5aj5Ch96dLace5tXVzWBZkxU/c5ohw==}
    engines: {node: <=0.11.8 || >0.11.10}

  async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  babel-loader@9.2.1:
    resolution: {integrity: sha512-fqe8naHt46e0yIdkjUZYqddSXfej3AHajX+CSO5X7oy0EmPc6o5Xh+RClNoHjnieWz9AW4kZxW9yyFMhVB1QLA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
      webpack: '>=5'

  babel-plugin-polyfill-corejs2@0.4.14:
    resolution: {integrity: sha512-Co2Y9wX854ts6U8gAAPXfn0GmAyctHuK8n0Yhfjd6t30g7yvKjspvvOo9yG+z52PZRgFErt7Ka2pYnXCjLKEpg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.13.0:
    resolution: {integrity: sha512-U+GNwMdSFgzVmfhNm8GJUX88AadB3uo9KpJqS3FaqNIPKgySuvMb+bHPsOmmuWyIcuqZj/pzt1RUIUZns4y2+A==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.5:
    resolution: {integrity: sha512-ISqQ2frbiNU9vIJkzg7dlPpznPZ4jOiUQ1uSmB0fEHeowtN3COYRsXr/xexn64NpU13P06jc/L5TgiJXOgrbEg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64id@2.0.0:
    resolution: {integrity: sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==}
    engines: {node: ^4.5.0 || >= 5.9}

  batch@0.6.1:
    resolution: {integrity: sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browser-sync-client@3.0.4:
    resolution: {integrity: sha512-+ew5ubXzGRKVjquBL3u6najS40TG7GxCdyBll0qSRc/n+JRV9gb/yDdRL1IAgRHqjnJTdqeBKKIQabjvjRSYRQ==}
    engines: {node: '>=8.0.0'}

  browser-sync-ui@3.0.4:
    resolution: {integrity: sha512-5Po3YARCZ/8yQHFzvrSjn8+hBUF7ZWac39SHsy8Tls+7tE62iq6pYWxpVU6aOOMAGD21RwFQhQeqmJPf70kHEQ==}

  browser-sync@3.0.4:
    resolution: {integrity: sha512-mcYOIy4BW6sWSEnTSBjQwWsnbx2btZX78ajTTjdNfyC/EqQVcIe0nQR6894RNAMtvlfAnLaH9L2ka97zpvgenA==}
    engines: {node: '>= 8.0.0'}
    hasBin: true

  browserslist@4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bs-recipes@1.3.4:
    resolution: {integrity: sha512-BXvDkqhDNxXEjeGM8LFkSbR+jzmP/CYpCiVKYn+soB1dDldeU15EBNDkwVXndKuX35wnNUaPd0qSoQEAkmQtMw==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001726:
    resolution: {integrity: sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}

  cls-hooked@4.2.2:
    resolution: {integrity: sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==}
    engines: {node: ^4.7 || >=6.9 || >=7.3 || >=8.2.1}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  common-path-prefix@3.0.0:
    resolution: {integrity: sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  connect-history-api-fallback@1.6.0:
    resolution: {integrity: sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==}
    engines: {node: '>=0.8'}

  connect@3.6.6:
    resolution: {integrity: sha512-OO7axMmPpu/2XuX1+2Yrg0ddju31B6xLZMWkJ5rYBu4YRmRVlOjvlY6kw2FJKiAzyxGwnrDUAG4s1Pf0sbBMCQ==}
    engines: {node: '>= 0.10.0'}

  continuation-local-storage@3.2.1:
    resolution: {integrity: sha512-jx44cconVqkCEEyLSKWwkvUXwO561jXMa3LPjTPsm5QR22PA0/mhe33FT4Xb5y74JDvt/Cq+5lm8S8rskLv9ZA==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie@0.7.2:
    resolution: {integrity: sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==}
    engines: {node: '>= 0.6'}

  core-js-compat@3.43.0:
    resolution: {integrity: sha512-2GML2ZsCc5LR7hZYz4AXmjQw8zuy2T//2QntwdnpuYI7jteT6GVYJL7F6C2C57R7gSYrcqVW3lAALefdbhBLDA==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  css-loader@6.11.0:
    resolution: {integrity: sha512-CTJ+AEQJjq5NzLga5pE39qdiSV56F8ywCIsqNIRF0r7BDgWsN25aazToqAFg7ZrtA/U016xudB3ffgweORxX7g==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  dev-ip@1.0.1:
    resolution: {integrity: sha512-LmVkry/oDShEgSZPNgqCIp2/TlqtExeGmymru3uCELnfyjY11IzpAproLYs+1X88fXO6DBoYP3ul2Xo2yz2j6A==}
    engines: {node: '>= 0.8.0'}
    hasBin: true

  diagnostic-channel-publishers@1.0.8:
    resolution: {integrity: sha512-HmSm9hXxSPxA9BaLGY98QU1zsdjeCk113KjAYGPCen1ZP6mhVaTPzHd6UYv5r21DnWANi+f+NyPOHruGT9jpqQ==}
    peerDependencies:
      diagnostic-channel: '*'

  diagnostic-channel@1.1.1:
    resolution: {integrity: sha512-r2HV5qFkUICyoaKlBEpLKHjxMXATUf/l+h8UZPGBHGLy4DDiY2sOLcIctax4eRnTw5wH2jTMExLntGPJ8eOJxw==}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  easy-extender@2.3.4:
    resolution: {integrity: sha512-8cAwm6md1YTiPpOvDULYJL4ZS6WfM5/cTeVVh4JsvyYZAoqlRVUpHL9Gr5Fy7HA6xcSZicUia3DeAgO3Us8E+Q==}
    engines: {node: '>= 4.0.0'}

  eazy-logger@4.1.0:
    resolution: {integrity: sha512-+mn7lRm+Zf1UT/YaH8WXtpU6PIV2iOjzP6jgKoiaq/VNrjYKp+OHZGe2znaLgDeFkw8cL9ffuaUm+nNnzcYyGw==}
    engines: {node: '>= 0.8.0'}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.5.179:
    resolution: {integrity: sha512-UWKi/EbBopgfFsc5k61wFpV7WrnnSlSzW/e2XcBmS6qKYTivZlLtoll5/rdqRTxGglGHkmkW0j0pFNJG10EUIQ==}

  emitter-listener@1.1.2:
    resolution: {integrity: sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  engine.io-client@6.6.3:
    resolution: {integrity: sha512-T0iLjnyNWahNyv/lcjS2y4oE358tVS/SYQNxYXGAJ9/GLgH4VCvOQ/mhTjqU88mLZCQgiG8RIegFHYCdVC+j5w==}

  engine.io-parser@5.2.3:
    resolution: {integrity: sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==}
    engines: {node: '>=10.0.0'}

  engine.io@6.6.4:
    resolution: {integrity: sha512-ZCkIjSYNDyGn0R6ewHDtXgns/Zre/NT6Agvq1/WobF7JXgFff4SeDroKiCO3fNJreU9YG429Sc81o4w5ok/W5g==}
    engines: {node: '>=10.2.0'}

  enhanced-resolve@5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==}
    engines: {node: '>=10.13.0'}

  es-abstract@1.24.0:
    resolution: {integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-plugin-promise@7.2.1:
    resolution: {integrity: sha512-SWKjd+EuvWkYaS+uN2csvj0KoP43YTu7+phKQ5v+xw6+A0gutVX2yqCeCkC3uLCJFiPfR2dD8Es5L7yUsmvEaA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0 || ^9.0.0

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.30.1:
    resolution: {integrity: sha512-zmxXPNMOXmwm9E0yQLi5uqXHs7uq2UIiqEKo3Gq+3fwo1XrJ+hijAZImyF7hclW3E6oHz43Yk3RP8at6OTKflQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  finalhandler@1.1.0:
    resolution: {integrity: sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw==}
    engines: {node: '>= 0.8'}

  find-cache-dir@4.0.0:
    resolution: {integrity: sha512-9ZonPT4ZAK4a+1pUPVPZJapbi7O5qbbJPdYw/NOQWZZbVLdDTYM3A4R9z/DpAM08IDaFGsvPgiGZ82WEwUDWjg==}
    engines: {node: '>=14.16'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  find-up@6.3.0:
    resolution: {integrity: sha512-v2ZsoEuVHYy8ZIlYqwPe/39Cy+cFDzp4dXPaxNvkEuouymu+2Jbz0PxpKarJHYJTmv2HWT3O382qY8l4jMWthw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fs-extra@11.3.0:
    resolution: {integrity: sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==}
    engines: {node: '>=14.14'}

  fs-extra@3.0.1:
    resolution: {integrity: sha512-V3Z3WZWVUYd8hoCL5xfXJCaHWYzmtwW5XWYSlLgERi8PWd8bx1kUHUk8L1BT57e49oKnDDD180mjfrHc1yA9rg==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globals@15.15.0:
    resolution: {integrity: sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  has-bigints@1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  icss-utils@5.1.0:
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}

  immutable@3.8.2:
    resolution: {integrity: sha512-15gZoQ38eYjEjxkorfbcgBKBL6R7T459OuK+CpcWt7O3KF4uPCx2tD0uFETlUDIyo+1789crbMhTvQBSR5yBMg==}
    engines: {node: '>=0.10.0'}

  immutable@5.1.3:
    resolution: {integrity: sha512-+chQdDfvscSF1SJqv2gn4SRO2ZyS3xL3r7IW/wWEEzrzLisnOlKiQu5ytC/BVNcS15C39WT2Hg/bjKjDMcu+zg==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  import-in-the-middle@1.14.2:
    resolution: {integrity: sha512-5tCuY9BV8ujfOpwtAGgsTx9CGUapcFMEEyByLv1B+v2+6DhAcw+Zr0nhQT7uwaZ7DiourxFEscghOR8e1aPLQw==}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  internal-slot@1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}

  is-async-function@2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}

  is-number-like@1.0.8:
    resolution: {integrity: sha512-6rZi3ezCyFcn5L71ywzz2bS5b2Igl1En3eTlZlvKjpz1n3IZLAYMbKYAIQgFmEu0GENg92ziU/faEOA/aixjbA==}

  is-number-object@1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-regex@1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}

  is-string@1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}

  is-wsl@1.1.0:
    resolution: {integrity: sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==}
    engines: {node: '>=4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==}
    engines: {node: '>= 0.4'}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@3.0.1:
    resolution: {integrity: sha512-oBko6ZHlubVB5mRFkur5vgYR1UyqX+S6Y/oCfLhqNdcc2fYFlDpIoNc7AfKS1KOGcnNAkvsr0grLck9ANM815w==}

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsonschema@1.5.0:
    resolution: {integrity: sha512-K+A9hhqbn0f3pJX17Q/7H6yQfD/5OXgdrR5UE12gMXCiN9D5Xq2o5mddV2QEcX/bjla99ASsAAQUyMCCRWAEhw==}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}

  keyborg@2.6.0:
    resolution: {integrity: sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  limiter@1.1.5:
    resolution: {integrity: sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  locate-path@7.2.0:
    resolution: {integrity: sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.isfinite@3.3.2:
    resolution: {integrity: sha512-7FGG40uhC8Mm633uKW1r58aElFlBlxCrg9JfSi3P6aYiWmfiWF0PgMd86ZUsxE5GwWPdHoS2+48bwTh2VPkIQA==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  mitt@1.2.0:
    resolution: {integrity: sha512-r6lj77KlwqLhIUku9UWYes7KJtsczvolZkzp8hbaDPPaE24OmWl5s539Mytlj22siEQKosZ26qCBgda2PKwoJw==}

  module-details-from-path@1.0.4:
    resolution: {integrity: sha512-EGWKgxALGMgzvxYF1UyGTy0HXX/2vHLkw6+NvDKW2jypWbHpjQuj4UMcqQWXHERJhVGKikolT06G3bcKe4fi7w==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  opn@5.3.0:
    resolution: {integrity: sha512-bYJHo/LOmoTd+pfiYhfZDnf9zekVJrY+cnS2a5F2x+w5ppvTqObojTP7WiFG+kVZs9Inw+qQ/lw7TroWwhdd2g==}
    engines: {node: '>=4'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-limit@4.0.0:
    resolution: {integrity: sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-locate@6.0.0:
    resolution: {integrity: sha512-wPrq66Llhl7/4AGC6I+cqxT07LhXvWL08LNXz1fENOw0Ap4sRZZ/gZpTTJ5jpurzzzfS2W/Ge9BY3LgLjCShcw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  pcf-scripts@1.44.2:
    resolution: {integrity: sha512-8hjb02jRkqf02lJ336WbtyALTdr/kdx/VpGQbO3z9iu9uz0CjoAx09rdd4rHdSsP+h+jq6VCxcupaPHif8Wteg==}
    engines: {node: '>=18'}
    hasBin: true
    peerDependencies:
      '@svgr/webpack': ^8.1.0
      eslint: ^8 || ^9
      typescript: ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      '@svgr/webpack':
        optional: true

  pcf-start@1.44.2:
    resolution: {integrity: sha512-hyJPPWaVlxGToef9fY26OPPOVcn39wZ7Rh87p2z8Q0Wo2xDy9eVMTBL4uwMlkE2d1mdvGT4crZcVFsIvrYfANw==}
    engines: {node: '>=18'}
    hasBin: true

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pkg-dir@7.0.0:
    resolution: {integrity: sha512-Ie9z/WINcxxLp27BKOCHGde4ITq9UklYKDzVo1nhk5sqGEXU3FpkwP5GM2voTGJkGd9B3Otl+Q4uwSOeSUtOBA==}
    engines: {node: '>=14.16'}

  portscanner@2.2.0:
    resolution: {integrity: sha512-IFroCz/59Lqa2uBvzK3bKDbDDIEaAY8XJ1jFxcLWTqosrsc32//P4VuSB2vZXoHiHqOmx8B5L5hnKOxL/7FlPw==}
    engines: {node: '>=0.4', npm: '>=1.0.0'}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}

  postcss-modules-extract-imports@3.1.0:
    resolution: {integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.2.0:
    resolution: {integrity: sha512-5kcJm/zk+GJDSfw+V/42fJ5fhjL5YbFDl8nVdXkJPLLW+Vf9mTD5Xe0wqIaDnLuL2U6cDNpTr+UQ+v2HWIBhzw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.2.1:
    resolution: {integrity: sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha512-8sLjZwK0R+JlxlYcTuVnyT2v+htpdrjDOKuMcOVdYjt52Lh8hWRYpxBPoKx/Zg+bcjc3wx6fmQevMmUztS/ccA==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}

  regexpu-core@6.2.0:
    resolution: {integrity: sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==}

  regjsparser@0.12.0:
    resolution: {integrity: sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  require-in-the-middle@7.5.2:
    resolution: {integrity: sha512-gAZ+kLqBdHarXB64XpAe2VCjB7rIRv+mU8tfRWziHRJ5umKsIHN2tLLv6EtMw7WCdP19S0ERVMldNvxYCHnhSQ==}
    engines: {node: '>=8.6.0'}

  requireindex@1.2.0:
    resolution: {integrity: sha512-L9jEkOi3ASd9PYit2cwRfyppc9NoABujTP8/5gFcbERmo5jUoAKovIC3fsF17pkTnGsrByysqX+Kxd2OTNI1ww==}
    engines: {node: '>=0.10.5'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true

  resp-modifier@6.0.2:
    resolution: {integrity: sha512-U1+0kWC/+4ncRFYqQWTx/3qkfE6a4B/h3XXgmXypfa0SPZ3t7cbbaFk297PjQS/yov24R18h6OZe6iZwj3NSLw==}
    engines: {node: '>= 0.8.0'}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rx@4.1.0:
    resolution: {integrity: sha512-CiaiuN6gapkdl+cZUr67W6I8jquN4lkak3vtIsIWCl4XIPP8ffsoyN6/+PuGXnQy8Cu8W2y9Xxh31Rq4M6wUug==}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass-loader@13.3.3:
    resolution: {integrity: sha512-mt5YN2F1MOZr3d/wBRcZxeFgwgkH44wVc2zohO2YF6JiOMkiXe4BYRZpSu2sO1g71mo/j16txzUhsKZlqjVGzA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true

  sass@1.89.2:
    resolution: {integrity: sha512-xCmtksBKd/jdJ9Bt9p7nPKiuqrlBMBuuGkQlkhZjjQk3Ty48lv93k5Dq6OPkKt4XwxDJ7tvlfrTa1MPA9bf+QA==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  scheduler@0.20.2:
    resolution: {integrity: sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  schema-utils@4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==}
    engines: {node: '>= 10.13.0'}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  send@0.19.1:
    resolution: {integrity: sha512-p4rRk4f23ynFEfcD9LA0xRYngj+IyGiEYyqqOak8kaN0TvNmuxC2dcVeBn62GpCeR2CpWqyHCNScTP91QbAVFg==}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}

  serve-index@1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  server-destroy@1.0.1:
    resolution: {integrity: sha512-rb+9B5YBIEzYcD6x2VKidaa+cqYBJQKnU4oe4E3ANwRRN56yk/ua1YCJT1n21NTS8w6CcOclAKNP3PhdCXKYtQ==}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}

  setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shimmer@1.2.1:
    resolution: {integrity: sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  socket.io-adapter@2.5.5:
    resolution: {integrity: sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg==}

  socket.io-client@4.8.1:
    resolution: {integrity: sha512-hJVXfu3E28NmzGk8o1sHhN3om52tRvwYeidbj7xKy2eIIse5IoKX3USlS6Tqt3BHAtflLIkCQBkzVrEEfWUyYQ==}
    engines: {node: '>=10.0.0'}

  socket.io-parser@4.2.4:
    resolution: {integrity: sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==}
    engines: {node: '>=10.0.0'}

  socket.io@4.8.1:
    resolution: {integrity: sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg==}
    engines: {node: '>=10.2.0'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  stack-chain@1.3.7:
    resolution: {integrity: sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==}

  statuses@1.3.1:
    resolution: {integrity: sha512-wuTCPGlJONk/a1kqZ4fQM2+908lC7fa7nPYpTC1EhnvqLX/IICbeP1OZGDtA374trpSq68YubKUMo8oRhN46yg==}
    engines: {node: '>= 0.6'}

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stop-iteration-iterator@1.1.0:
    resolution: {integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==}
    engines: {node: '>= 0.4'}

  stream-throttle@0.1.3:
    resolution: {integrity: sha512-889+B9vN9dq7/vLbGyuHeZ6/ctf5sNuGWsDy89uNxkFTAgzy0eK7+w5fL3KLNRTkLle7EgZGvHUphZW0Q26MnQ==}
    engines: {node: '>= 0.10.0'}
    hasBin: true

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  style-loader@3.3.4:
    resolution: {integrity: sha512-0WqXzrsMTyb8yjZJHDqwmnwRJvhALK9LfRtRc6B4UTWe8AijYLZYZ9thuJTZc2VfQWINADW/j+LiJnfy2RoC1w==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  stylis@4.3.6:
    resolution: {integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  tabster@8.5.6:
    resolution: {integrity: sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==}

  tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.43.1:
    resolution: {integrity: sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg==}
    engines: {node: '>=10'}
    hasBin: true

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  ts-loader@9.5.2:
    resolution: {integrity: sha512-Qo4piXvOTWcMGIgRiuFa6nHNm+54HbYaZCKqc9eeZCLRy3XqafQgwX2F7mofrbJG3g7EEb+lkiR+z2Lic2s3Zw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}

  typescript-eslint@8.35.1:
    resolution: {integrity: sha512-xslJjFzhOmHYQzSB/QTeASAHbjmxOGEP6Coh93TXmUBFQoJ1VU35UHIDmG06Jd6taf3wqqC1ntBnCMeymy5Ovw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  ua-parser-js@1.0.40:
    resolution: {integrity: sha512-z6PJ8Lml+v3ichVojCiB8toQJBuwR42ySM4ezjXIqXK3M0HczmKQ3LF4rhU55PfD99KEEXQG6yb7iOMyvYuHew==}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  use-disposable@1.0.4:
    resolution: {integrity: sha512-j83t6AMLWUyb5zwlTDqf6dP9LezM9R0yTbI/b6olmdaGtCKQUe9pgJWV6dRaaQLcozypjIEp4EmZr2DkZGKLSg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  watchpack@2.4.4:
    resolution: {integrity: sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA==}
    engines: {node: '>=10.13.0'}

  webpack-merge@5.10.0:
    resolution: {integrity: sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==}
    engines: {node: '>=10.0.0'}

  webpack-sources@3.3.3:
    resolution: {integrity: sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==}
    engines: {node: '>=10.13.0'}

  webpack@5.99.9:
    resolution: {integrity: sha512-brOPwM3JnmOa+7kd3NsmOUOwbDAj8FT9xDsG3IW0MgbN9yZV7Oi/s/+MNQ/EcSMqw7qfoRyXPoeEWT8zLVdVGg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wildcard@2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==}

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  ws@8.17.1:
    resolution: {integrity: sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml2js@0.5.0:
    resolution: {integrity: sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xmlhttprequest-ssl@2.1.2:
    resolution: {integrity: sha512-TEU+nJVUUnA4CYJFLvK5X9AOeH4KvDvhIfm0vV1GaQRtchnG0hgK5p8hw/xjv8cunWYCsiPCSDzObPyhEwq3KQ==}
    engines: {node: '>=0.4.0'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  yocto-queue@1.2.1:
    resolution: {integrity: sha512-AyeEbWOu/TAXdxlV9wmGcR0+yh2j3vYPGOECcIj2S7MkrLyC7ne+oye2BKTItt0ii2PHk4cDy+95+LshzbXnGg==}
    engines: {node: '>=12.20'}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@azure/abort-controller@2.1.2':
    dependencies:
      tslib: 2.8.1

  '@azure/core-auth@1.7.2':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-util': 1.12.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-rest-pipeline@1.16.3':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@azure/core-auth': 1.7.2
      '@azure/core-tracing': 1.2.0
      '@azure/core-util': 1.12.0
      '@azure/logger': 1.2.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/core-tracing@1.2.0':
    dependencies:
      tslib: 2.8.1

  '@azure/core-util@1.12.0':
    dependencies:
      '@azure/abort-controller': 2.1.2
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/logger@1.2.0':
    dependencies:
      '@typespec/ts-http-runtime': 0.2.3
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@azure/opentelemetry-instrumentation-azure-sdk@1.0.0-beta.9':
    dependencies:
      '@azure/core-tracing': 1.2.0
      '@azure/logger': 1.2.0
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.200.0(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 2.0.1(@opentelemetry/api@1.9.0)
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.0': {}

  '@babel/core@7.28.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.28.0':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.27.3':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.28.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      debug: 4.4.1
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-member-expression-to-functions@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.27.1':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-wrap-function': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helper-wrap-function@7.27.1':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0

  '@babel/parser@7.28.0':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-decorators': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-globals': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2

  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-parameters': 7.27.7(@babel/core@7.28.0)
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.28.0)
      '@babel/types': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regenerator@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.28.0)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/preset-env@7.28.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-assertions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.28.0)
      '@babel/plugin-transform-arrow-functions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-async-generator-functions': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-async-to-generator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-block-scoped-functions': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-block-scoping': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-class-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-class-static-block': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-classes': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-computed-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-destructuring': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-dotall-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-duplicate-keys': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-dynamic-import': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-explicit-resource-management': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-exponentiation-operator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-export-namespace-from': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-for-of': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-function-name': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-json-strings': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-logical-assignment-operators': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-member-expression-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-amd': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-systemjs': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-modules-umd': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-new-target': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-numeric-separator': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-object-rest-spread': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-object-super': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-optional-catch-binding': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-parameters': 7.27.7(@babel/core@7.28.0)
      '@babel/plugin-transform-private-methods': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-private-property-in-object': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-property-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-regenerator': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-regexp-modifiers': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-reserved-words': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-shorthand-properties': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-spread': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-sticky-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-template-literals': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-typeof-symbol': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-escapes': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-property-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-unicode-sets-regex': 7.27.1(@babel/core@7.28.0)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.28.0)
      babel-plugin-polyfill-corejs2: 0.4.14(@babel/core@7.28.0)
      babel-plugin-polyfill-corejs3: 0.13.0(@babel/core@7.28.0)
      babel-plugin-polyfill-regenerator: 0.6.5(@babel/core@7.28.0)
      core-js-compat: 3.43.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/types': 7.28.0
      esutils: 2.0.3

  '@babel/preset-react@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-transform-react-display-name': 7.28.0(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx-development': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-pure-annotations': 7.27.1(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.0

  '@babel/traverse@7.28.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.0
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.0':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@emotion/hash@0.9.2': {}

  '@eslint-community/eslint-utils@4.7.0(eslint@9.30.1)':
    dependencies:
      eslint: 9.30.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.21.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.3.0': {}

  '@eslint/core@0.14.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.15.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.30.1': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.3':
    dependencies:
      '@eslint/core': 0.15.1
      levn: 0.4.1

  '@floating-ui/core@1.7.2':
    dependencies:
      '@floating-ui/utils': 0.2.10

  '@floating-ui/devtools@0.2.1(@floating-ui/dom@1.7.2)':
    dependencies:
      '@floating-ui/dom': 1.7.2

  '@floating-ui/dom@1.7.2':
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/utils': 0.2.10

  '@floating-ui/utils@0.2.10': {}

  '@fluentui/keyboard-keys@9.0.8':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/priority-overflow@9.1.15':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/react-accordion@9.7.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-motion-components-preview': 0.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-alert@9.0.0-beta.107(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-aria@9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-avatar@9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-badge': 9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-popover': 9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.7.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-badge@9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-breadcrumb@9.2.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-link': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-button@9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-card@9.3.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-text': 9.5.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-checkbox@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-combobox@9.15.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-positioning': 9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-components@9.46.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-accordion': 9.7.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-alert': 9.0.0-beta.107(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-badge': 9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-breadcrumb': 9.2.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-card': 9.3.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-checkbox': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-combobox': 9.15.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-dialog': 9.13.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-divider': 9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-drawer': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-image': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-infobutton': 9.0.0-beta.91(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-infolabel': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-input': 9.6.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-link': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-menu': 9.17.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-message-bar': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-overflow': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-persona': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-popover': 9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-positioning': 9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-progress': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-provider': 9.21.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-radio': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-select': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-skeleton': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-slider': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-spinbutton': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-spinner': 9.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-switch': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-table': 9.17.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-tabs': 9.8.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-tags': 9.6.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-text': 9.5.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-textarea': 9.5.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-toast': 9.5.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-toolbar': 9.5.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-tooltip': 9.7.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-tree': 9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-virtualizer': 9.0.0-alpha.67(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      scheduler: 0.20.2

  '@fluentui/react-context-selector@9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      scheduler: 0.20.2

  '@fluentui/react-dialog@9.13.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-motion-components-preview': 0.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-divider@9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-drawer@9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-dialog': 9.13.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-field@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-icons@2.0.305(react@16.14.0)':
    dependencies:
      '@griffel/react': 1.5.30(react@16.14.0)
      react: 16.14.0
      tslib: 2.8.1

  '@fluentui/react-image@9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-infobutton@9.0.0-beta.91(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-popover': 9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-infolabel@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-popover': 9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-input@9.6.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-jsx-runtime@9.1.2(@types/react@16.14.65)(react@16.14.0)':
    dependencies:
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      react: 16.14.0
      react-is: 17.0.2

  '@fluentui/react-label@9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-link@9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-menu@9.17.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-positioning': 9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-message-bar@9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-link': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@16.14.0))(react@16.14.0)

  '@fluentui/react-motion-components-preview@0.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-motion@9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-overflow@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/priority-overflow': 9.1.15
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-persona@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-badge': 9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-popover@9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-positioning': 9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-portal@9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      use-disposable: 1.0.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)

  '@fluentui/react-positioning@9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@floating-ui/devtools': 0.2.1(@floating-ui/dom@1.7.2)
      '@floating-ui/dom': 1.7.2
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      use-sync-external-store: 1.5.0(react@16.14.0)

  '@fluentui/react-progress@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-provider@9.21.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/core': 1.19.2
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-radio@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-select@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-shared-contexts@9.24.0(@types/react@16.14.65)(react@16.14.0)':
    dependencies:
      '@fluentui/react-theme': 9.1.24
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      react: 16.14.0

  '@fluentui/react-skeleton@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-slider@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinbutton@9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinner@9.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-switch@9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-label': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-table@9.17.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-checkbox': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-radio': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabs@9.8.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabster@9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      keyborg: 2.6.0
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
      tabster: 8.5.6

  '@fluentui/react-tags@9.6.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-text@9.5.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-textarea@9.5.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-field': 9.3.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-theme@9.1.24':
    dependencies:
      '@fluentui/tokens': 1.0.0-alpha.21
      '@swc/helpers': 0.5.17

  '@fluentui/react-toast@9.5.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-motion-components-preview': 0.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-toolbar@9.5.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-divider': 9.3.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-radio': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tooltip@9.7.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-portal': 9.6.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-positioning': 9.18.5(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/react-tree@9.11.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.15.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-avatar': 9.8.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-button': 9.5.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-checkbox': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-context-selector': 9.2.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-icons': 2.0.305(react@16.14.0)
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-motion': 9.9.0(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-motion-components-preview': 0.6.2(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-radio': 9.4.6(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)(scheduler@0.20.2)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-tabster': 9.25.3(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-utilities@9.22.0(@types/react@16.14.65)(react@16.14.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      react: 16.14.0

  '@fluentui/react-virtualizer@9.0.0-alpha.67(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.1.2(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-shared-contexts': 9.24.0(@types/react@16.14.65)(react@16.14.0)
      '@fluentui/react-utilities': 9.22.0(@types/react@16.14.65)(react@16.14.0)
      '@griffel/react': 1.5.30(react@16.14.0)
      '@swc/helpers': 0.5.17
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  '@fluentui/tokens@1.0.0-alpha.21':
    dependencies:
      '@swc/helpers': 0.5.17

  '@griffel/core@1.19.2':
    dependencies:
      '@emotion/hash': 0.9.2
      '@griffel/style-types': 1.3.0
      csstype: 3.1.3
      rtl-css-js: 1.16.1
      stylis: 4.3.6
      tslib: 2.8.1

  '@griffel/react@1.5.30(react@16.14.0)':
    dependencies:
      '@griffel/core': 1.19.2
      react: 16.14.0
      tslib: 2.8.1

  '@griffel/style-types@1.3.0':
    dependencies:
      csstype: 3.1.3

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@jridgewell/gen-mapping@0.3.12':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/source-map@0.3.10':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/sourcemap-codec@1.5.4': {}

  '@jridgewell/trace-mapping@0.3.29':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  '@microsoft/applicationinsights-web-snippet@1.0.1': {}

  '@microsoft/eslint-plugin-power-apps@0.2.51':
    dependencies:
      requireindex: 1.2.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@opentelemetry/api-logs@0.200.0':
    dependencies:
      '@opentelemetry/api': 1.9.0

  '@opentelemetry/api@1.9.0': {}

  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.34.0

  '@opentelemetry/instrumentation@0.200.0(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.200.0
      '@types/shimmer': 1.2.0
      import-in-the-middle: 1.14.2
      require-in-the-middle: 7.5.2
      shimmer: 1.2.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/resources@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0

  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.28.0

  '@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0

  '@opentelemetry/sdk-trace-web@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 2.0.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/semantic-conventions@1.28.0': {}

  '@opentelemetry/semantic-conventions@1.34.0': {}

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    optional: true

  '@socket.io/component-emitter@3.1.2': {}

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@types/cors@2.8.19':
    dependencies:
      '@types/node': 18.19.115

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.8

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.8': {}

  '@types/json-schema@7.0.15': {}

  '@types/node@18.19.115':
    dependencies:
      undici-types: 5.26.5

  '@types/powerapps-component-framework@1.3.18':
    dependencies:
      '@types/react': 16.14.65

  '@types/prop-types@15.7.15': {}

  '@types/react-dom@18.3.7(@types/react@16.14.65)':
    dependencies:
      '@types/react': 16.14.65

  '@types/react@16.14.65':
    dependencies:
      '@types/prop-types': 15.7.15
      '@types/scheduler': 0.16.8
      csstype: 3.1.3

  '@types/scheduler@0.16.8': {}

  '@types/shimmer@1.2.0': {}

  '@typescript-eslint/eslint-plugin@8.35.1(@typescript-eslint/parser@8.35.1(eslint@9.30.1)(typescript@5.8.3))(eslint@9.30.1)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.35.1
      '@typescript-eslint/type-utils': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      '@typescript-eslint/utils': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.35.1
      eslint: 9.30.1
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.35.1(eslint@9.30.1)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.35.1
      '@typescript-eslint/types': 8.35.1
      '@typescript-eslint/typescript-estree': 8.35.1(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.35.1
      debug: 4.4.1
      eslint: 9.30.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.35.1(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.35.1(typescript@5.8.3)
      '@typescript-eslint/types': 8.35.1
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.35.1':
    dependencies:
      '@typescript-eslint/types': 8.35.1
      '@typescript-eslint/visitor-keys': 8.35.1

  '@typescript-eslint/tsconfig-utils@8.35.1(typescript@5.8.3)':
    dependencies:
      typescript: 5.8.3

  '@typescript-eslint/type-utils@8.35.1(eslint@9.30.1)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.35.1(typescript@5.8.3)
      '@typescript-eslint/utils': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.30.1
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.35.1': {}

  '@typescript-eslint/typescript-estree@8.35.1(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.35.1(typescript@5.8.3)
      '@typescript-eslint/tsconfig-utils': 8.35.1(typescript@5.8.3)
      '@typescript-eslint/types': 8.35.1
      '@typescript-eslint/visitor-keys': 8.35.1
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.35.1(eslint@9.30.1)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1)
      '@typescript-eslint/scope-manager': 8.35.1
      '@typescript-eslint/types': 8.35.1
      '@typescript-eslint/typescript-estree': 8.35.1(typescript@5.8.3)
      eslint: 9.30.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.35.1':
    dependencies:
      '@typescript-eslint/types': 8.35.1
      eslint-visitor-keys: 4.2.1

  '@typespec/ts-http-runtime@0.2.3':
    dependencies:
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-import-attributes@1.9.5(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  agent-base@7.1.3: {}

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-regex@5.0.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  applicationinsights@2.9.7:
    dependencies:
      '@azure/core-auth': 1.7.2
      '@azure/core-rest-pipeline': 1.16.3
      '@azure/opentelemetry-instrumentation-azure-sdk': 1.0.0-beta.9
      '@microsoft/applicationinsights-web-snippet': 1.0.1
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 1.30.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.34.0
      cls-hooked: 4.2.2
      continuation-local-storage: 3.2.1
      diagnostic-channel: 1.1.1
      diagnostic-channel-publishers: 1.0.8(diagnostic-channel@1.1.1)
    transitivePeerDependencies:
      - supports-color

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-includes@3.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  async-each-series@0.1.1: {}

  async-function@1.0.0: {}

  async-hook-jl@1.7.6:
    dependencies:
      stack-chain: 1.3.7

  async-listener@0.6.10:
    dependencies:
      semver: 5.7.2
      shimmer: 1.2.1

  async@2.6.4:
    dependencies:
      lodash: 4.17.21

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  babel-loader@9.2.1(@babel/core@7.28.0)(webpack@5.99.9):
    dependencies:
      '@babel/core': 7.28.0
      find-cache-dir: 4.0.0
      schema-utils: 4.3.2
      webpack: 5.99.9

  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
      core-js-compat: 3.43.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-define-polyfill-provider': 0.6.5(@babel/core@7.28.0)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64id@2.0.0: {}

  batch@0.6.1: {}

  binary-extensions@2.3.0: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browser-sync-client@3.0.4:
    dependencies:
      etag: 1.8.1
      fresh: 0.5.2
      mitt: 1.2.0

  browser-sync-ui@3.0.4:
    dependencies:
      async-each-series: 0.1.1
      chalk: 4.1.2
      connect-history-api-fallback: 1.6.0
      immutable: 3.8.2
      server-destroy: 1.0.1
      socket.io-client: 4.8.1
      stream-throttle: 0.1.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  browser-sync@3.0.4:
    dependencies:
      browser-sync-client: 3.0.4
      browser-sync-ui: 3.0.4
      bs-recipes: 1.3.4
      chalk: 4.1.2
      chokidar: 3.6.0
      connect: 3.6.6
      connect-history-api-fallback: 1.6.0
      dev-ip: 1.0.1
      easy-extender: 2.3.4
      eazy-logger: 4.1.0
      etag: 1.8.1
      fresh: 0.5.2
      fs-extra: 3.0.1
      http-proxy: 1.18.1
      immutable: 3.8.2
      micromatch: 4.0.8
      opn: 5.3.0
      portscanner: 2.2.0
      raw-body: 2.5.2
      resp-modifier: 6.0.2
      rx: 4.1.0
      send: 0.19.1
      serve-index: 1.9.1
      serve-static: 1.16.2
      server-destroy: 1.0.1
      socket.io: 4.8.1
      ua-parser-js: 1.0.40
      yargs: 17.7.2
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  browserslist@4.25.1:
    dependencies:
      caniuse-lite: 1.0.30001726
      electron-to-chromium: 1.5.179
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.1)

  bs-recipes@1.3.4: {}

  buffer-from@1.1.2: {}

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  caniuse-lite@1.0.30001726: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chrome-trace-event@1.0.4: {}

  cjs-module-lexer@1.4.3: {}

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  cls-hooked@4.2.2:
    dependencies:
      async-hook-jl: 1.7.6
      emitter-listener: 1.1.2
      semver: 5.7.2

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  commander@2.20.3: {}

  common-path-prefix@3.0.0: {}

  concat-map@0.0.1: {}

  connect-history-api-fallback@1.6.0: {}

  connect@3.6.6:
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.0
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color

  continuation-local-storage@3.2.1:
    dependencies:
      async-listener: 0.6.10
      emitter-listener: 1.1.2

  convert-source-map@2.0.0: {}

  cookie@0.7.2: {}

  core-js-compat@3.43.0:
    dependencies:
      browserslist: 4.25.1

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-loader@6.11.0(webpack@5.99.9):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-modules-extract-imports: 3.1.0(postcss@8.5.6)
      postcss-modules-local-by-default: 4.2.0(postcss@8.5.6)
      postcss-modules-scope: 3.2.1(postcss@8.5.6)
      postcss-modules-values: 4.0.0(postcss@8.5.6)
      postcss-value-parser: 4.2.0
      semver: 7.7.2
    optionalDependencies:
      webpack: 5.99.9

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.3.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  deep-is@0.1.4: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  depd@1.1.2: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-libc@1.0.3:
    optional: true

  dev-ip@1.0.1: {}

  diagnostic-channel-publishers@1.0.8(diagnostic-channel@1.1.1):
    dependencies:
      diagnostic-channel: 1.1.1

  diagnostic-channel@1.1.1:
    dependencies:
      semver: 7.7.2

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  easy-extender@2.3.4:
    dependencies:
      lodash: 4.17.21

  eazy-logger@4.1.0:
    dependencies:
      chalk: 4.1.2

  ee-first@1.1.1: {}

  electron-to-chromium@1.5.179: {}

  emitter-listener@1.1.2:
    dependencies:
      shimmer: 1.2.1

  emoji-regex@8.0.0: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  engine.io-client@6.6.3:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
      engine.io-parser: 5.2.3
      ws: 8.17.1
      xmlhttprequest-ssl: 2.1.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  engine.io-parser@5.2.3: {}

  engine.io@6.6.4:
    dependencies:
      '@types/cors': 2.8.19
      '@types/node': 18.19.115
      accepts: 1.3.8
      base64id: 2.0.0
      cookie: 0.7.2
      cors: 2.8.5
      debug: 4.3.7
      engine.io-parser: 5.2.3
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  enhanced-resolve@5.18.2:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  es-abstract@1.24.0:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@4.0.0: {}

  eslint-plugin-promise@7.2.1(eslint@9.30.1):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1)
      eslint: 9.30.1

  eslint-plugin-react@7.37.5(eslint@9.30.1):
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.30.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.30.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.30.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.21.0
      '@eslint/config-helpers': 0.3.0
      '@eslint/core': 0.14.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.30.1
      '@eslint/plugin-kit': 0.3.3
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.1.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.3.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@4.0.0:
    dependencies:
      common-path-prefix: 3.0.0
      pkg-dir: 7.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@6.3.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flat@5.0.2: {}

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  fresh@0.5.2: {}

  fs-extra@11.3.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-extra@3.0.1:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 3.0.1
      universalify: 0.1.2

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.1.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  icss-utils@5.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  immutable@3.8.2: {}

  immutable@5.1.3: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-in-the-middle@1.14.2:
    dependencies:
      acorn: 8.15.0
      acorn-import-attributes: 1.9.5(acorn@8.15.0)
      cjs-module-lexer: 1.4.3
      module-details-from-path: 1.0.4

  imurmurhash@0.1.4: {}

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-like@1.0.8:
    dependencies:
      lodash.isfinite: 3.3.2

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-wsl@1.1.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 18.19.115
      merge-stream: 2.0.0
      supports-color: 8.1.1

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  jsonfile@3.0.1:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonschema@1.5.0: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyborg@2.6.0: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  limiter@1.1.5: {}

  loader-runner@4.3.0: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash.debounce@4.0.8: {}

  lodash.isfinite@3.3.2: {}

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  math-intrinsics@1.1.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  mitt@1.2.0: {}

  module-details-from-path@1.0.4: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  neo-async@2.6.2: {}

  node-addon-api@7.1.1:
    optional: true

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  opn@5.3.0:
    dependencies:
      is-wsl: 1.1.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parseurl@1.3.3: {}

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  pcf-scripts@1.44.2(eslint@9.30.1)(typescript@5.8.3):
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-proposal-decorators': 7.28.0(@babel/core@7.28.0)
      '@babel/preset-env': 7.28.0(@babel/core@7.28.0)
      '@babel/preset-react': 7.27.1(@babel/core@7.28.0)
      applicationinsights: 2.9.7
      babel-loader: 9.2.1(@babel/core@7.28.0)(webpack@5.99.9)
      chokidar: 3.6.0
      css-loader: 6.11.0(webpack@5.99.9)
      eslint: 9.30.1
      fs-extra: 11.3.0
      jsonschema: 1.5.0
      lodash: 4.17.21
      minimist: 1.2.8
      sass: 1.89.2
      sass-loader: 13.3.3(sass@1.89.2)(webpack@5.99.9)
      semver: 7.7.2
      style-loader: 3.3.4(webpack@5.99.9)
      tapable: 2.2.2
      ts-loader: 9.5.2(typescript@5.8.3)(webpack@5.99.9)
      typescript: 5.8.3
      uuid: 8.3.2
      webpack: 5.99.9
      webpack-merge: 5.10.0
      xml2js: 0.5.0
      yargs: 16.2.0
    transitivePeerDependencies:
      - '@rspack/core'
      - '@swc/core'
      - applicationinsights-native-metrics
      - esbuild
      - fibers
      - node-sass
      - sass-embedded
      - supports-color
      - uglify-js
      - webpack-cli

  pcf-start@1.44.2:
    dependencies:
      applicationinsights: 2.9.7
      browser-sync: 3.0.4
      yargs: 16.2.0
    transitivePeerDependencies:
      - applicationinsights-native-metrics
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pkg-dir@7.0.0:
    dependencies:
      find-up: 6.3.0

  portscanner@2.2.0:
    dependencies:
      async: 2.6.4
      is-number-like: 1.0.8

  possible-typed-array-names@1.1.0: {}

  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6

  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.2.1(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 7.1.0

  postcss-modules-values@4.0.0(postcss@8.5.6):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.6)
      postcss: 8.5.6

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  react-dom@18.3.1(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      react: 16.14.0
      scheduler: 0.23.2

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-transition-group@4.4.5(react-dom@18.3.1(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.27.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  react@16.14.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-in-the-middle@7.5.2:
    dependencies:
      debug: 4.4.1
      module-details-from-path: 1.0.4
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  requireindex@1.2.0: {}

  requires-port@1.0.0: {}

  resolve-from@4.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resp-modifier@6.0.2:
    dependencies:
      debug: 2.6.9
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  reusify@1.1.0: {}

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.27.6

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rx@4.1.0: {}

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  sass-loader@13.3.3(sass@1.89.2)(webpack@5.99.9):
    dependencies:
      neo-async: 2.6.2
      webpack: 5.99.9
    optionalDependencies:
      sass: 1.89.2

  sass@1.89.2:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.3
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1

  sax@1.4.1: {}

  scheduler@0.20.2:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@4.3.2:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  send@0.19.1:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  server-destroy@1.0.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shimmer@1.2.1: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  socket.io-adapter@2.5.5:
    dependencies:
      debug: 4.3.7
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-client@4.8.1:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
      engine.io-client: 6.6.3
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-parser@4.2.4:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
    transitivePeerDependencies:
      - supports-color

  socket.io@4.8.1:
    dependencies:
      accepts: 1.3.8
      base64id: 2.0.0
      cors: 2.8.5
      debug: 4.3.7
      engine.io: 6.6.4
      socket.io-adapter: 2.5.5
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  stack-chain@1.3.7: {}

  statuses@1.3.1: {}

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stop-iteration-iterator@1.1.0:
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0

  stream-throttle@0.1.3:
    dependencies:
      commander: 2.20.3
      limiter: 1.1.5

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-json-comments@3.1.1: {}

  style-loader@3.3.4(webpack@5.99.9):
    dependencies:
      webpack: 5.99.9

  stylis@4.3.6: {}

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  tabster@8.5.6:
    dependencies:
      keyborg: 2.6.0
      tslib: 2.8.1
    optionalDependencies:
      '@rollup/rollup-linux-x64-gnu': 4.40.0

  tapable@2.2.2: {}

  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.43.1
      webpack: 5.99.9

  terser@5.43.1:
    dependencies:
      '@jridgewell/source-map': 0.3.10
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  ts-loader@9.5.2(typescript@5.8.3)(webpack@5.99.9):
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.18.2
      micromatch: 4.0.8
      semver: 7.7.2
      source-map: 0.7.4
      typescript: 5.8.3
      webpack: 5.99.9

  tslib@2.8.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript-eslint@8.35.1(eslint@9.30.1)(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.35.1(@typescript-eslint/parser@8.35.1(eslint@9.30.1)(typescript@5.8.3))(eslint@9.30.1)(typescript@5.8.3)
      '@typescript-eslint/parser': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      '@typescript-eslint/utils': 8.35.1(eslint@9.30.1)(typescript@5.8.3)
      eslint: 9.30.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  ua-parser-js@1.0.40: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@5.26.5: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@0.1.2: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.25.1):
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-disposable@1.0.4(@types/react-dom@18.3.7(@types/react@16.14.65))(@types/react@16.14.65)(react-dom@18.3.1(react@16.14.0))(react@16.14.0):
    dependencies:
      '@types/react': 16.14.65
      '@types/react-dom': 18.3.7(@types/react@16.14.65)
      react: 16.14.0
      react-dom: 18.3.1(react@16.14.0)

  use-sync-external-store@1.5.0(react@16.14.0):
    dependencies:
      react: 16.14.0

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  uuid@8.3.2: {}

  vary@1.1.2: {}

  watchpack@2.4.4:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  webpack-merge@5.10.0:
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1

  webpack-sources@3.3.3: {}

  webpack@5.99.9:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      browserslist: 4.25.1
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.2
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14(webpack@5.99.9)
      watchpack: 2.4.4
      webpack-sources: 3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wildcard@2.0.1: {}

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  ws@8.17.1: {}

  xml2js@0.5.0:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xmlhttprequest-ssl@2.1.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}
