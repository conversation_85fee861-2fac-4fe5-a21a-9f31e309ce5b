<?xml version="1.0" encoding="utf-8"?>
<manifest>
  <control namespace="LokBiz.Controls" constructor="ActionButton" version="0.0.5" display-name-key="Action Button" description-key="Action Button Control" control-type="virtual">
    <external-service-usage enabled="false"></external-service-usage>
    <property name="sourceControl" display-name-key="Data Field" description-key="The field that will store the value of the selected option." of-type="SingleLine.Text" usage="bound" required="true" />
    <property name="apiEndpoint" display-name-key="API Endpoint" description-key="Input the API endpoint to call." of-type="SingleLine.Text" usage="input" required="false" />
    <resources>
      <code path="index.ts" order="1" />
      <platform-library name="React" version="16.14.0" />
      <platform-library name="Fluent" version="9.46.2" />
    </resources>
  </control>
</manifest>