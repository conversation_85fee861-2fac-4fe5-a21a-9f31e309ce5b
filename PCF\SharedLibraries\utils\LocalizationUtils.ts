/**
 * Centralized localization utilities for PCF controls
 * Provides type-safe, performance-optimized localization processing
 */

import type { NestedObject, Entity } from "../types/CoreTypes";

// ============================================================================
// CONSTANTS AND TYPES
// ============================================================================

export const LANGUAGES = {
  ENG: 'en',
  VN: 'vi',
} as const;

export type SupportedLanguage = typeof LANGUAGES[keyof typeof LANGUAGES];

export const DEFAULT_LANGUAGE: SupportedLanguage = LANGUAGES.ENG;

export const LANGUAGE_CODE_MAP = {
  // Microsoft LCID mappings
  1033: LANGUAGES.ENG, // English (United States)
  1066: LANGUAGES.VN,  // Vietnamese
  // ISO 639-1 codes
  'en': LANGUAGES.ENG,
  'en-US': LANGUAGES.ENG,
  'en-GB': LANGUAGES.ENG,
  'vi': LANGUAGES.VN,
  'vi-VN': LANGUAGES.VN,
  // Common variations
  'english': LANGUAGES.ENG,
  'vietnamese': LANGUAGES.VN,
  'vie': LANGUAGES.VN,
} as const;

// ============================================================================
// CORE UTILITIES
// ============================================================================

const isObject = (value: unknown): value is Entity => {
  return typeof value === "object" && value !== null && !Array.isArray(value) && !(value instanceof Date);
};

const deepMerge = <T extends NestedObject>(base: T, overrides: Partial<T>): T => {
  const result = { ...base };

  for (const key in overrides) {
    // biome-ignore lint/suspicious/noPrototypeBuiltins: Library shared code
    if (!Object.prototype.hasOwnProperty.call(overrides, key)) continue;

    const typedKey = key as keyof T;
    const baseValue = result[typedKey];
    const overrideValue = overrides[typedKey];

    if (isObject(baseValue) && isObject(overrideValue)) {
      result[typedKey] = deepMerge(
        baseValue as T[keyof T] & NestedObject,
        overrideValue as Partial<T[keyof T] & NestedObject>
      ) as T[keyof T];
    } else if (overrideValue !== undefined) {
      result[typedKey] = overrideValue as T[keyof T];
    }
  }

  return result;
};

export const mergeStrings = <T extends NestedObject>(
  base: T,
  overrides: Partial<T>,
): T => {
  return deepMerge(base, overrides);
};

export const safeJsonParse = <T = unknown>(
  jsonString: string,
  fallback: T | null = null
): T | null => {
  if (!jsonString || typeof jsonString !== 'string') {
    return fallback;
  }

  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return fallback;
  }
};

export const validateLocalizationStructure = (
  obj: unknown,
  requiredKeys: string[] = []
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!isObject(obj)) {
    errors.push('Object must be a valid object');
    return { isValid: false, errors };
  }

  for (const key of requiredKeys) {
    if (!(key in obj)) {
      errors.push(`Missing required key: ${key}`);
    }
  }

  return { isValid: errors.length === 0, errors };
};

// ============================================================================
// STRING PROCESSING
// ============================================================================

export const replaceStringPlaceholders = (
  template: string,
  ...values: (string | number)[]
): string => {
  return template.replace(/\{(\d+)\}/g, (_, index) => {
    const idx = parseInt(index, 10);
    return idx < values.length ? String(values[idx]) : `{${index}}`;
  });
};

export const getNestedProperty = <T = unknown>(
  obj: Entity,
  path: string,
  defaultValue: T
): T => {
  if (!path || typeof path !== 'string') {
    return defaultValue;
  }

  const keys = path.split('.');
  let current: unknown = obj;

  for (const key of keys) {
    if (isObject(current) && key in current) {
      current = current[key];
    } else {
      return defaultValue;
    }
  }

  return current as T;
};

export const flattenObject = (
  obj: Entity,
  separator = '.',
  prefix = ''
): Entity => {
  const flattened: Entity = {};

  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}${separator}${key}` : key;

    if (isObject(value)) {
      Object.assign(flattened, flattenObject(value, separator, newKey));
    } else {
      flattened[newKey] = value;
    }
  }

  return flattened;
};

export const unflattenObject = (
  flatObj: Entity,
  separator = '.'
): Entity => {
  const result: Entity = {};

  for (const [key, value] of Object.entries(flatObj)) {
    const keys = key.split(separator);
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
      const currentKey = keys[i];
      if (!(currentKey in current)) {
        current[currentKey] = {};
      }
      current = current[currentKey] as Entity;
    }

    current[keys[keys.length - 1]] = value;
  }

  return result;
};

// ============================================================================
// LANGUAGE PROCESSING
// ============================================================================

export const getValidatedLanguage = (
  lang: string | number | undefined | null,
  fallback: SupportedLanguage = DEFAULT_LANGUAGE
): SupportedLanguage => {
  if (!lang) return fallback;

  if (typeof lang === 'number') {
    return LANGUAGE_CODE_MAP[lang as keyof typeof LANGUAGE_CODE_MAP] || fallback;
  }

  const normalizedLang = lang.toString().toLowerCase().trim();

  if (normalizedLang in LANGUAGE_CODE_MAP) {
    return LANGUAGE_CODE_MAP[normalizedLang as keyof typeof LANGUAGE_CODE_MAP];
  }

  if (normalizedLang.includes('en') || normalizedLang.includes('english')) {
    return LANGUAGES.ENG;
  }

  if (normalizedLang.includes('vi') || normalizedLang.includes('viet')) {
    return LANGUAGES.VN;
  }

  return fallback;
};

export const getOppositeLanguage = (lang: SupportedLanguage): SupportedLanguage => {
  return lang === LANGUAGES.ENG ? LANGUAGES.VN : LANGUAGES.ENG;
};

export const isSupportedLanguage = (lang: string): lang is SupportedLanguage => {
  return Object.values(LANGUAGES).includes(lang as SupportedLanguage);
};

export const getSupportedLanguages = (): SupportedLanguage[] => {
  return Object.values(LANGUAGES);
};

export const getLanguageDisplayName = (lang: SupportedLanguage): string => {
  const displayNames: Record<SupportedLanguage, string> = {
    [LANGUAGES.ENG]: 'English',
    [LANGUAGES.VN]: 'Tiếng Việt',
  };
  return displayNames[lang] || lang;
};

export const detectSystemLanguage = (
  navigator?: Pick<Navigator, 'language' | 'languages'>
): SupportedLanguage => {
  const nav = navigator || (typeof window !== 'undefined' ? window.navigator : null);

  if (!nav) return DEFAULT_LANGUAGE;

  const primaryLang = nav.language;
  if (primaryLang) {
    const validated = getValidatedLanguage(primaryLang);
    if (validated !== DEFAULT_LANGUAGE) return validated;
  }

  if (nav.languages) {
    for (const lang of nav.languages) {
      const validated = getValidatedLanguage(lang);
      if (validated !== DEFAULT_LANGUAGE) return validated;
    }
  }

  return DEFAULT_LANGUAGE;
};

// ============================================================================
// LEGACY SUPPORT FUNCTIONS
// ============================================================================

export const createLanguageLoader = <T extends NestedObject>(
  localizationMap: Record<SupportedLanguage, T>
) => {
  return (lang: string | number | undefined | null): T => {
    const validatedLang = getValidatedLanguage(lang);
    return localizationMap[validatedLang] || localizationMap[DEFAULT_LANGUAGE];
  };
};

export const createAdvancedLocalizationMerger = <T extends NestedObject>(
  defaultStrings: T,
  languageMap?: Partial<Record<SupportedLanguage, T>>
) => {
  return (
    customStrings: string | Partial<T> | null,
    targetLanguage?: string | number | null
  ): T => {
    const validatedLang = getValidatedLanguage(targetLanguage);
    const baseStrings = languageMap?.[validatedLang] || defaultStrings;

    if (!customStrings) {
      return baseStrings;
    }

    if (typeof customStrings === 'string') {
      const parsed = safeJsonParse<Partial<T>>(customStrings);
      if (!parsed) {
        return baseStrings;
      }
      return mergeStrings(baseStrings, parsed);
    }

    return mergeStrings(baseStrings, customStrings);
  };
};

// ============================================================================
// MAIN LOCALIZATION FUNCTIONS
// ============================================================================

export const validateAndMergeStrings = <T extends NestedObject>(
  defaultStrings: T,
  customStrings: string | Partial<T> | null,
  requiredKeys?: string[]
): { result: T; isValid: boolean; errors: string[] } => {
  if (!customStrings) {
    return { result: defaultStrings, isValid: true, errors: [] };
  }

  let parsedCustom: Partial<T> | null = null;

  if (typeof customStrings === 'string') {
    parsedCustom = safeJsonParse<Partial<T>>(customStrings);
    if (!parsedCustom) {
      return {
        result: defaultStrings,
        isValid: false,
        errors: ['Invalid JSON format in custom strings']
      };
    }
  } else {
    parsedCustom = customStrings;
  }

  if (requiredKeys && requiredKeys.length > 0) {
    const validation = validateLocalizationStructure(parsedCustom, requiredKeys);
    if (!validation.isValid) {
      return {
        result: defaultStrings,
        isValid: false,
        errors: validation.errors
      };
    }
  }

  const result = mergeStrings(defaultStrings, parsedCustom);
  return { result, isValid: true, errors: [] };
};

/**
 * Centralized localization processor for PCF controls
 * Handles language priority, custom text merging, and fallback logic
 */
export const processControlLocalization = <T extends NestedObject>(
  localizationMap: Record<string, T>,
  userLanguage?: string | number | null,
  defaultLanguage: SupportedLanguage = DEFAULT_LANGUAGE,
  customText?: string | Partial<T> | null
): T => {
  // Determine target language with priority: userLanguage -> defaultLanguage -> DEFAULT_LANGUAGE
  let targetLanguage = defaultLanguage;

  if (userLanguage) {
    const validatedUserLang = getValidatedLanguage(userLanguage);
    if (validatedUserLang && localizationMap[validatedUserLang]) {
      targetLanguage = validatedUserLang;
    }
  }

  // Get base strings for target language with fallbacks
  const baseStrings = localizationMap[targetLanguage] ||
                     localizationMap[defaultLanguage] ||
                     localizationMap[DEFAULT_LANGUAGE] ||
                     Object.values(localizationMap)[0];

  if (!baseStrings) {
    throw new Error('No localization strings available in localizationMap');
  }

  // Apply custom text overrides if provided
  if (!customText) {
    return baseStrings;
  }

  const { result, isValid } = validateAndMergeStrings(baseStrings, customText);
  return isValid ? result : baseStrings;
};