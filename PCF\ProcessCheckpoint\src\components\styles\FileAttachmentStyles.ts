import type { CSSProperties } from "react";
import {
  UI_COLORS,
  UI_CURSORS,
  UI_DIMENSIONS,
  UI_EFFECTS,
  UI_SPACING,
} from "../../types";
import { layoutStyles, textStyles } from "./CommonStyles";

export const fileAttachmentContainerStyle: CSSProperties = {
  marginTop: UI_SPACING.SPACING_L,
  padding: UI_SPACING.SPACING_L,
  border: `1px dashed ${UI_COLORS.border.DEFAULT}`,
  borderRadius: UI_DIMENSIONS.MEDIUM_BORDER_RADIUS,
  textAlign: "center",
  cursor: UI_CURSORS.POINTER,
  transition: UI_EFFECTS.DEFAULT_TRANSITION,
};

export const dragActiveStyle: CSSProperties = {
  borderColor: UI_COLORS.border.DRAG_ACTIVE,
  backgroundColor: UI_COLORS.background.DRAG_ACTIVE,
};

export const fileListStyle: CSSProperties = {
  marginTop: UI_SPACING.SPACING_L,
  display: "flex",
  flexDirection: "column",
  gap: UI_SPACING.SPACING_S,
};

export const fileItemStyle: CSSProperties = {
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: UI_SPACING.SPACING_S,
  border: `1px solid ${UI_COLORS.border.DEFAULT}`,
  borderRadius: UI_DIMENSIONS.MEDIUM_BORDER_RADIUS,
  backgroundColor: UI_COLORS.WHITE,
};

export const fileInfoStyle: CSSProperties = {
  ...layoutStyles.flexRow,
  gap: UI_SPACING.SPACING_S,
  flex: 1,
};

export const infoTextStyle: CSSProperties = {
  ...textStyles.smallSecondary,
  marginTop: UI_SPACING.SPACING_S,
};

export const actionsStyle: CSSProperties = {
  ...layoutStyles.flexRow,
  gap: UI_SPACING.SPACING_XS,
};

export const readOnlyContainerStyle: CSSProperties = {
  marginTop: UI_SPACING.SPACING_L,
  padding: UI_SPACING.SPACING_L,
  border: `1px solid ${UI_COLORS.border.DEFAULT}`,
  borderRadius: UI_DIMENSIONS.MEDIUM_BORDER_RADIUS,
  backgroundColor: UI_COLORS.background.LIGHT_GRAY,
};

export const textStyle: CSSProperties = {
  fontSize: "14px",
  color: "#323130",
};

export const utilityStyle: CSSProperties = {
  padding: "12px",
  border: "2px dashed #d1d1d1",
  borderRadius: "4px",
  textAlign: "center",
  backgroundColor: "#fafafa",
  cursor: "pointer",
};
