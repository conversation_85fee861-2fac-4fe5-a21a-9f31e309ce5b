/**
 * Vietnamese localization strings for ActionButton PCF Control
 * Following SharedLibraries LocalizationUtils patterns
 */

import type { LocalizationStrings } from '../types/LocalizationTypes';

export const vietnameseStrings: LocalizationStrings = {
    ui: {
        buttons: {
            create: "Áp Dụng Control",
            update: "Chỉnh Sửa Control",
            remove: "Hoàn Nguyên Control",
        },
        messages: {
            processing: "Đang xử lý yêu cầu của bạn...",
        },
        errors: {
            apiError: "Không thể hoàn thành thao tác. Vui lòng thử lại.",
            unknownError: "<PERSON><PERSON> xảy ra lỗi không mong muốn.",
        },
    },
    api: {
        actions: {
            create: "applycontrol",
            update: "modifycontrol",
            remove: "revertcontrol",
        },
        messages: {
            createSuccess: "Áp dụng control thành công",
            updateSuccess: "Chỉnh sửa control thành công",
            removeSuccess: "Hoàn nguyên control thành công",
        },
    },
};
