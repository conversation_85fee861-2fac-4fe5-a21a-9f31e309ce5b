// Note: BpfStage, <PERSON>pfCollection, and BpfStageInfo are imported from SharedLibraries via types/index.ts

export enum BpfActionType {
  CANCEL = "cancel",
  SUBMIT = "submit",
  ADVISE = "advise",
  APPROVE = "approve",
  REJECT = "reject",
}

export type BpfActionName = "submit" | "advise" | "approve" | "reject";

export const BPF_NOTE_ACTIONS: BpfActionName[] = [
  BpfActionType.SUBMIT,
  BpfActionType.ADVISE,
  BpfActionType.APPROVE,
  BpfActionType.REJECT,
];

export enum BpfStatusType {
  ACTIVE = "active",
  ABORT = "aborted",
  FINISH = "finished",
}

export enum ReadOnlyReasonType {
  PREVIOUS = "previous",
  NEXT = "next",
  FINISHED = "finished",
  COMPLETED = "completed",
  ERROR = "error",
}

export interface BpfActionResult {
  success: boolean;
  message?: string;
  error?: string;
}

export interface BpfActionHandlers {
  onCancel: () => void;
  onSubmit?: () => Promise<BpfActionResult>;
  onAdvise?: () => Promise<BpfActionResult>;
  onApprove?: () => Promise<BpfActionResult>;
  onReject?: () => Promise<BpfActionResult>;
}

// BpfStageInfo is now imported from SharedLibraries via types/index.ts

export interface BpfButtonConfig {
  showCancel: boolean;
  showSubmit: boolean;
  showAdvise: boolean;
  showApprove: boolean;
  showReject: boolean;
}
