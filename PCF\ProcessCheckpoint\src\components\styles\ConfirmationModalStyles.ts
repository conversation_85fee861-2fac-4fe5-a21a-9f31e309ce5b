import type { CSSProperties } from "react";
import {
  UI_COLORS,
  UI_DIMENSIONS,
  UI_EFFECTS,
  UI_SPACING,
  UI_TYPOGRAPHY,
} from "../../types";
import {
  modalFooterStyle as baseModalFooterStyle,
  modalContentStyle,
  modalHeaderStyle,
  modalOverlayStyle,
} from "./ModalStyles";

export const confirmationModalOverlayStyle: CSSProperties = {
  ...modalOverlayStyle,
  backgroundColor: UI_COLORS.background.MODAL_CONFIRMATION_OVERLAY,
  zIndex: UI_DIMENSIONS.CONFIRMATION_MODAL_Z_INDEX,
};

export const confirmationModalContentStyle: CSSProperties = {
  ...modalContentStyle,
  boxShadow: UI_EFFECTS.CONFIRMATION_MODAL_SHADOW,
  maxWidth: UI_DIMENSIONS.CONFIRMATION_MODAL_MAX_WIDTH,
  maxHeight: UI_DIMENSIONS.CONFIRMATION_MODAL_MAX_HEIGHT,
  overflow: "auto",
};

export const confirmationModalHeaderStyle: CSSProperties = {
  ...modalHeaderStyle,
  padding: `${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_L} ${UI_SPACING.SPACING_XXL}`,
  borderBottom: `1px solid ${UI_COLORS.border.DIVIDER_LIGHT}`,
};

export const bodyStyles: CSSProperties = {
  padding: `${UI_SPACING.SPACING_L} ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_XXL}`,
};

export const messageStyles: CSSProperties = {
  margin: 0,
  fontSize: UI_TYPOGRAPHY.DEFAULT_FONT_SIZE,
  lineHeight: UI_TYPOGRAPHY.LINE_HEIGHT_NORMAL,
  color: UI_COLORS.text.SECONDARY,
};

export const confirmationModalFooterStyle: CSSProperties = {
  ...baseModalFooterStyle,
  borderTop: "none",
  padding: `0 ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_XXL} ${UI_SPACING.SPACING_XXL}`,
  gap: UI_SPACING.SPACING_M,
};
